# Meena Backend API

This is the backend API for the Meena messaging application. It provides a RESTful API and WebSocket support for real-time messaging.

## Features

- User authentication (login, registration, token refresh)
- User profile management
- Contact management
- Conversation management
- Messaging (text, media)
- Real-time messaging via WebSockets
- Media upload and management

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Docker (optional, for containerization)

### Running Locally

1. Clone the repository:

```bash
git clone https://github.com/yourusername/meena.git
cd meena/backend
```

2. Install dependencies:

```bash
go mod download
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Run the application:

```bash
go run cmd/api/main.go
```

The API will be available at http://localhost:8080.

### Running with Docker

1. Build the Docker image:

```bash
docker build -t meena-api .
```

2. Run the Docker container:

```bash
docker run -p 8080:8080 meena-api
```

The API will be available at http://localhost:8080.

### Deploying to Railway

1. Install the Railway CLI:

```bash
npm install -g @railway/cli
```

2. Login to Railway:

```bash
railway login
```

3. Link your project to Railway:

```bash
railway link
```

4. Deploy the application:

```bash
railway up
```

This will use the Dockerfile to build and deploy the application. The railway.toml file configures deployment settings like health checks and restart policies.

5. Generate a domain for your API:

Go to the Railway dashboard, select your service, and click on "Generate Domain" in the Settings tab.

6. Set environment variables:

In the Railway dashboard, go to the Variables tab of your service and set the following variables:
- `JWT_SECRET`: A secure random string for JWT token signing
- `ENV`: Set to "production"

The API will be available at the generated Railway domain.
## Security Configuration

### IMPORTANT: Production JWT Secret

**For all production deployments, it is ABSOLUTELY CRITICAL to set a strong, unique, and randomly generated `JWT_SECRET` environment variable.**

Failure to set a strong `JWT_SECRET` will expose your application to severe security vulnerabilities, potentially allowing attackers to forge authentication tokens and gain unauthorized access.

**Recommendations for `JWT_SECRET`:**
*   Use a cryptographically secure random string.
*   Minimum length of 32 characters is advised; 64 characters is preferred.
*   Do NOT use default, common, or easily guessable phrases.
*   Store this secret securely and do not commit it to your version control system if it's hardcoded anywhere during development (it should always be loaded from an environment variable in production).

The application loads this secret from the `JWT_SECRET` environment variable (see [`backend/internal/config/config.go`](backend/internal/config/config.go:0)).

## API Documentation

### Authentication

#### Login

```
POST /api/v1/auth/login
```

Request:
```json
{
  "identifier": "username_or_email",
  "password": "password"
}
```

Response:
```json
{
  "user": {
    "id": "user_id",
    "user_handle": "username",
    "display_name": "Display Name",
    "bio": "User bio",
    "avatar_url": "https://example.com/avatar.jpg",
    "is_verified": false,
    "is_gold_member": false,
    "last_active": "2023-07-15T12:34:56Z",
    "created_at": "2023-07-01T00:00:00Z",
    "follower_count": 10,
    "following_count": 20
  },
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "expires_in": 86400
}
```

#### Register

```
POST /api/v1/auth/register
```

Request:
```json
{
  "user_handle": "username",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "password": "password"
}
```

Response: Same as login response.

### User Profile

#### Get Profile

```
GET /api/v1/users/me
```

Response:
```json
{
  "id": "user_id",
  "user_handle": "username",
  "display_name": "Display Name",
  "bio": "User bio",
  "avatar_url": "https://example.com/avatar.jpg",
  "is_verified": false,
  "is_gold_member": false,
  "last_active": "2023-07-15T12:34:56Z",
  "created_at": "2023-07-01T00:00:00Z",
  "follower_count": 10,
  "following_count": 20
}
```

#### Update Profile

```
PUT /api/v1/users/me
```

Request:
```json
{
  "display_name": "New Display Name",
  "bio": "New bio",
  "avatar_url": "https://example.com/new-avatar.jpg"
}
```

Response: Updated user profile.

### Contacts

#### Get Contacts

```
GET /api/v1/contacts
```

Response:
```json
{
  "contacts": [
    {
      "id": "contact_id",
      "user_id": "user_id",
      "contact_id": "contact_user_id",
      "display_name": "Contact Name",
      "user_handle": "contact_username",
      "avatar_url": "https://example.com/avatar.jpg",
      "relationship": "friend",
      "last_active": "2023-07-15T12:34:56Z",
      "is_online": true
    }
  ]
}
```

#### Add Contact

```
POST /api/v1/contacts
```

Request:
```json
{
  "user_handle": "contact_username",
  "display_name": "Contact Name",
  "notes": "Notes about the contact"
}
```

Response: The created contact.

### Conversations

#### Get Conversations

```
GET /api/v1/conversations
```

Response:
```json
{
  "conversations": [
    {
      "id": "conversation_id",
      "type": "one_to_one",
      "name": "Conversation Name",
      "avatar_url": "https://example.com/avatar.jpg",
      "participant_count": 2,
      "last_message": {
        "id": "message_id",
        "sender_id": "sender_id",
        "sender_name": "Sender Name",
        "type": "text",
        "content": "Hello, how are you?",
        "created_at": "2023-07-15T12:34:56Z"
      },
      "last_message_time": "2023-07-15T12:34:56Z",
      "unread_count": 2,
      "is_encrypted": true,
      "is_archived": false,
      "is_muted": false,
      "is_pinned": true
    }
  ]
}
```

#### Create Conversation

```
POST /api/v1/conversations
```

Request:
```json
{
  "type": "one_to_one",
  "name": "Conversation Name",
  "avatar_url": "https://example.com/avatar.jpg",
  "participant_ids": ["user_id_1", "user_id_2"],
  "is_encrypted": true
}
```

Response: The created conversation.

### Messages

#### Get Messages

```
GET /api/v1/messages?conversation_id=conversation_id&limit=50&offset=0
```

Response:
```json
{
  "messages": [
    {
      "id": "message_id",
      "conversation_id": "conversation_id",
      "sender_id": "sender_id",
      "type": "text",
      "content": "Hello, how are you?",
      "status": "read",
      "created_at": "2023-07-15T12:34:56Z",
      "updated_at": "2023-07-15T12:34:56Z",
      "delivered_at": "2023-07-15T12:34:56Z",
      "read_at": "2023-07-15T12:35:00Z"
    }
  ],
  "total": 100
}
```

#### Send Message

```
POST /api/v1/messages
```

Request:
```json
{
  "conversation_id": "conversation_id",
  "type": "text",
  "content": "Hello, how are you?",
  "reply_to_id": "message_id_to_reply_to"
}
```

Response: The created message.

### WebSocket

Connect to the WebSocket endpoint:

```
wss://api.meena.com/api/v1/ws?token=jwt_token
```

Message format:

```json
{
  "type": "message",
  "id": "message_id",
  "payload": {
    "conversation_id": "conversation_id",
    "content": "Hello, how are you?"
  },
  "timestamp": 1626962475
}
```

## Project Structure

```
backend/
├── cmd/
│   └── api/
│       └── main.go           # Entry point
├── internal/
│   ├── api/                  # API handlers
│   ├── config/               # Configuration
│   ├── database/             # Database access
│   ├── middleware/           # Middleware
│   ├── models/               # Data models
│   ├── services/             # Business logic
│   └── utils/                # Utilities
├── .env.example              # Example environment variables
├── Dockerfile                # Docker configuration
├── railway.toml              # Railway configuration
├── go.mod                    # Go module file
└── README.md                 # This file
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
