package main

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/api"
	"github.com/meena/backend/internal/config"
	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/logging"
	"github.com/meena/backend/internal/middleware"
	"go.uber.org/zap"

	"github.com/DataDog/datadog-go/statsd"
)

func main() {
	// Load configuration
	cfg, loadConfigErr := config.LoadConfig(".")
	if loadConfigErr != nil {
		logging.Fatal("Failed to load configuration", loadConfigErr)
	}

	// Log critical configuration values
	logging.Info("Loaded configuration",
		zap.String("server_env", cfg.Server.Env),
		zap.String("server_port", cfg.Server.Port),
		zap.String("datadog_host", cfg.Datadog.Host),
		zap.String("datadog_port", cfg.Datadog.Port),
	)

	// Set up logging
	if cfg.Server.Env == "production" {
		logging.SetLevel(logging.INFO)
	} else {
		logging.SetLevel(logging.DEBUG)
	}

	logging.Info("Starting Meena API server",
		zap.String("environment", cfg.Server.Env),
		zap.String("port", cfg.Server.Port),
	)

	// Set Gin mode based on environment
	if cfg.Server.Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize database connection
	var db *database.DB
	var dbErr error

	// Try to connect to the database
	db, dbErr = database.New(cfg)
	if dbErr != nil {
		logging.Warn("Failed to connect to database. Continuing without database connection.",
			zap.Error(dbErr),
		)
		// Create a mock database for testing
		db = &database.DB{}
	} else {
		logging.Info("Successfully connected to database")
		defer db.Close()
	}

	// Create a new Gin router
	router := gin.Default()

	// Setup CORS middleware
	router.Use(corsMiddleware())

	// Setup error middleware
	router.Use(middleware.ErrorMiddleware())

	// Setup Correlation ID middleware
	router.Use(middleware.CorrelationIDMiddleware())

	// Create repositories
	userRepo := database.NewUserRepository(db)
	contactRepo := database.NewContactRepository(db)
	contactGroupRepo := database.NewContactGroupRepository(db)
	refreshTokenRepo := database.NewRefreshTokenRepository(db)

	// Initialize Datadog Statsd client
	logging.Info("Attempting to initialize Datadog StatsD client...")
	statsdClient, err := statsd.New(fmt.Sprintf("%s:%s", cfg.Datadog.Host, cfg.Datadog.Port))
	if err != nil {
		logging.Error("Failed to create Datadog Statsd client", err)
		// Continue without statsd client if initialization fails
	} else {
		logging.Info("Successfully connected to Datadog Statsd agent",
			zap.String("host", cfg.Datadog.Host),
			zap.String("port", cfg.Datadog.Port),
		)
		defer statsdClient.Close()
	}

	// Setup API routes
	api.SetupRoutes(router, userRepo, contactRepo, contactGroupRepo, refreshTokenRepo, statsdClient, cfg)

	// Start the server
	serverAddr := fmt.Sprintf(":%s", cfg.Server.Port)
	logging.Info("Server is preparing to start and handle requests",
		zap.String("address", serverAddr),
		zap.String("environment", cfg.Server.Env),
	)
	logging.Info("Attempting to start server...")
	if err := router.Run(serverAddr); err != nil {
		logging.Fatal("Failed to start server", err)
	}
}

// CORS middleware
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
