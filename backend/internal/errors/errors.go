package errors

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
)

// AppError represents an application error
type AppError struct {
	StatusCode int
	Code       string
	Message    string
	Details    []models.ErrorDetail
	Err        error
}

// Error returns the error message
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

// Unwrap returns the wrapped error
func (e *AppError) Unwrap() error {
	return e.Err
}

// New creates a new AppError
func New(statusCode int, code, message string, err error, details []models.ErrorDetail) *AppError {
	return &AppError{
		StatusCode: statusCode,
		Code:       code,
		Message:    message,
		Details:    details,
		Err:        err,
	}
}

// ValidationError creates a new validation error
func ValidationError(details []models.ErrorDetail) *AppError {
	return New(http.StatusUnprocessableEntity, "validation_failed", "Validation failed", nil, details)
}

// NotFoundError creates a new not found error
func NotFoundError(resource, id string) *AppError {
	details := []models.ErrorDetail{
		{
			Field:   resource + "_id",
			Issue:   "not_found",
			Message: resource + " with ID '" + id + "' does not exist",
		},
	}
	return New(http.StatusNotFound, "resource_not_found", "The requested "+resource+" was not found", nil, details)
}

// UnauthorizedError creates a new unauthorized error
func UnauthorizedError() *AppError {
	return New(http.StatusUnauthorized, "auth_required", "Authentication is required", nil, nil)
}

// ForbiddenError creates a new forbidden error
func ForbiddenError() *AppError {
	return New(http.StatusForbidden, "insufficient_permissions", "You don't have permission to perform this action", nil, nil)
}

// InternalError creates a new internal error
func InternalError(err error) *AppError {
	return New(http.StatusInternalServerError, "internal_error", "An unexpected error occurred", err, nil)
}

// BadRequestError creates a new bad request error
func BadRequestError(message string) *AppError {
	return New(http.StatusBadRequest, "bad_request", message, nil, nil)
}

// ConflictError creates a new conflict error
func ConflictError(message string) *AppError {
	return New(http.StatusConflict, "conflict", message, nil, nil)
}

// SendError sends an error response
func SendError(c *gin.Context, statusCode int, code, message string, details []models.ErrorDetail, err error) {
	appErr := New(statusCode, code, message, err, details)
	c.Error(appErr)
}

// SendValidationError sends a validation error response
func SendValidationError(c *gin.Context, details []models.ErrorDetail) {
	appErr := ValidationError(details)
	c.Error(appErr)
}

// SendNotFoundError sends a not found error response
func SendNotFoundError(c *gin.Context, resource, id string) {
	appErr := NotFoundError(resource, id)
	c.Error(appErr)
}

// SendUnauthorizedError sends an unauthorized error response
func SendUnauthorizedError(c *gin.Context) {
	appErr := UnauthorizedError()
	c.Error(appErr)
}

// SendForbiddenError sends a forbidden error response
func SendForbiddenError(c *gin.Context) {
	appErr := ForbiddenError()
	c.Error(appErr)
}

// SendInternalError sends an internal error response
func SendInternalError(c *gin.Context, err error) {
	appErr := InternalError(err)
	c.Error(appErr)
}

// SendBadRequestError sends a bad request error response
func SendBadRequestError(c *gin.Context, message string) {
	appErr := BadRequestError(message)
	c.Error(appErr)
}

// SendConflictError sends a conflict error response
func SendConflictError(c *gin.Context, message string) {
	appErr := ConflictError(message)
	c.Error(appErr)
}

// IsNotFound checks if an error is a not found error
func IsNotFound(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusNotFound
	}
	return false
}

// IsUnauthorized checks if an error is an unauthorized error
func IsUnauthorized(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusUnauthorized
	}
	return false
}

// IsForbidden checks if an error is a forbidden error
func IsForbidden(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusForbidden
	}
	return false
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusUnprocessableEntity
	}
	return false
}

// IsBadRequest checks if an error is a bad request error
func IsBadRequest(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusBadRequest
	}
	return false
}

// IsConflict checks if an error is a conflict error
func IsConflict(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusConflict
	}
	return false
}

// IsInternalError checks if an error is an internal error
func IsInternalError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.StatusCode == http.StatusInternalServerError
	}
	return false
}
