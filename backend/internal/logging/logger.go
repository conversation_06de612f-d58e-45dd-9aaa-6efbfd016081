package logging

import (
	"fmt"
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger is the application's logger instance.
var Logger *zap.Logger

// LogLevel represents the severity of a log entry
type LogLevel string

const (
	// DEBUG level for detailed troubleshooting
	DEBUG LogLevel = "DEBUG"
	// INFO level for general operational information
	INFO LogLevel = "INFO"
	// WARN level for potentially harmful situations
	WARN LogLevel = "WARN"
	// ERROR level for error events
	ERROR LogLevel = "ERROR"
	// FATAL level for very severe error events that will lead the application to abort
	FATAL LogLevel = "FATAL"
)

func init() {
	// Initialize a default logger (can be reconfigured later)
	SetLevel(INFO)
}

// SetLevel sets the log level for the global logger.
func SetLevel(level LogLevel) {
	var zapLevel zapcore.Level
	switch level {
	case DEBUG:
		zapLevel = zapcore.DebugLevel
	case INFO:
		zapLevel = zapcore.InfoLevel
	case WARN:
		zapLevel = zapcore.WarnLevel
	case ERROR:
		zapLevel = zapcore.ErrorLevel
	case FATAL:
		zapLevel = zapcore.FatalLevel
	default:
		zapLevel = zapcore.InfoLevel // Default to INFO
	}

	// Configure Zap to output JSON logs
	cfg := zap.Config{
		Level:       zap.NewAtomicLevelAt(zapLevel),
		Development: false, // Set to true for development-friendly output
		Sampling: &zap.SamplingConfig{
			Initial:    100,
			Thereafter: 100,
		},
		Encoding: "json", // Output logs in JSON format
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "timestamp",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "message",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.CapitalLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	var err error
	Logger, err = cfg.Build()
	if err != nil {
		// Fallback to a basic logger if Zap initialization fails
		log := zap.NewExample()
		Logger = log
		Logger.Error("Failed to initialize Zap logger, using example logger", zap.Error(err))
	}
}

// Debug logs a debug message with optional fields.
func Debug(message string, fields ...zap.Field) {
	Logger.Debug(message, fields...)
}

// Info logs an info message with optional fields.
func Info(message string, fields ...zap.Field) {
	Logger.Info(message, fields...)
}

// Warn logs a warning message with optional fields.
func Warn(message string, fields ...zap.Field) {
	Logger.Warn(message, fields...)
}

// Error logs an error message with optional fields.
func Error(message string, err error, fields ...zap.Field) {
	// Add the error as a field
	errorFields := append(fields, zap.Error(err))
	Logger.Error(message, errorFields...)
}

// Fatal logs a fatal message with optional fields and exits the application.
func Fatal(message string, err error, fields ...zap.Field) {
	// Add the error as a field
	errorFields := append(fields, zap.Error(err))
	Logger.Fatal(message, errorFields...)
	os.Exit(1) // Ensure application exits after fatal log
}

// WithFields adds fields to the logger for a specific context.
// This is a helper to maintain a similar pattern to the old logger,
// but it's generally better to pass fields directly to the log methods.
func WithFields(fields map[string]interface{}) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}
	return zapFields
}

// AddCorrelationID adds a correlation ID field to the log entry.
func AddCorrelationID(traceID string) zap.Field {
	return zap.String("trace_id", traceID)
}

// AddUserID adds a user ID field to the log entry.
func AddUserID(userID string) zap.Field {
	return zap.String("user_id", userID)
}

// AddCode adds a code field to the log entry.
func AddCode(code string) zap.Field {
	return zap.String("code", code)
}

// AddData adds a data field to the log entry.
func AddData(data interface{}) zap.Field {
	return zap.Any("data", data)
}

// AddError adds an error field to the log entry.
// This is an alternative to passing the error directly to the Error/Fatal methods.
func AddError(err error) zap.Field {
	return zap.Error(err)
}

// AddFileAndLine adds file and line number fields to the log entry.
// Zap's caller field often provides this, but this can be used for explicit control.
func AddFileAndLine(file string, line int) zap.Field {
	return zap.String("file", fmt.Sprintf("%s:%d", file, line))
}
