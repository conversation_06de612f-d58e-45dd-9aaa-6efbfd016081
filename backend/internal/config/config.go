package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig
	JWT      JWTConfig
	Database DatabaseConfig
	Redis    RedisConfig
	Datadog  DatadogConfig
}

// DatadogConfig holds Datadog-related configuration
type DatadogConfig struct {
	Host string
	Port string
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port string
	Env  string
}

// JWTConfig holds JWT-related configuration
type JWTConfig struct {
	Secret                       string
	ExpirationHours              time.Duration
	RefreshExpiration            time.Duration
	RecoverySessionTokenLifetime time.Duration // Added
	RefreshTokenPepper           string        // System-wide pepper for hashing refresh tokens. Should be a strong, randomly generated secret in production.
}

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	URL      string // Direct DATABASE_URL if provided
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// RedisConfig holds Redis-related configuration
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// LoadConfig loads configuration from environment variables
func LoadConfig(path string) (*Config, error) {
	// TODO: Use path to load config from a specific file if needed
	// For now, we'll continue to load from .env
	if err := godotenv.Load(); err != nil {
		// It's often okay if .env is not found, especially in production
		// where env vars are set directly. Log as warning or info.
		log.Printf("Warning: Failed to load .env file: %v. Relying on environment variables.", err)
	}

	config := &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8080"),
			Env:  getEnv("ENV", "development"),
		},
		JWT: JWTConfig{
			Secret:                       getEnv("JWT_SECRET", "your_jwt_secret_key"),
			ExpirationHours:              parseDuration(getEnv("JWT_EXPIRATION", "24h")),
			RefreshExpiration:            parseDuration(getEnv("JWT_REFRESH_EXPIRATION", "168h")),
			RecoverySessionTokenLifetime: parseDuration(getEnv("RECOVERY_SESSION_TOKEN_LIFETIME", "10m")), // Added
			RefreshTokenPepper:           getEnv("REFRESH_TOKEN_PEPPER", ""),                              // If empty, log a warning/error. For production, this MUST be set.
		},
		Database: DatabaseConfig{
			URL:      getEnv("DATABASE_URL", ""),
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			DBName:   getEnv("DB_NAME", "meena"),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       parseInt(getEnv("REDIS_DB", "0")),
		},
		Datadog: DatadogConfig{
			Host: getEnv("DD_AGENT_HOST", "localhost"),
			Port: getEnv("DD_AGENT_PORT", "8125"), // Default Statsd port
		},
	}

	if config.JWT.RefreshTokenPepper == "" {
		log.Println("WARNING: REFRESH_TOKEN_PEPPER is not set. This is insecure for production environments.")
		// Consider failing fast in production:
		// if config.Server.Env == "production" {
		// 	 return nil, errors.New("REFRESH_TOKEN_PEPPER must be set in production")
		// }
	}

	return config, nil
}

// Helper function to get environment variable with a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// Helper function to parse duration from string
func parseDuration(durationStr string) time.Duration {
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		log.Printf("Error parsing duration %s, using default 24h: %v", durationStr, err)
		return 24 * time.Hour
	}
	return duration
}

// Helper function to parse int from string
func parseInt(intStr string) int {
	intValue, err := strconv.Atoi(intStr)
	if err != nil {
		log.Printf("Error parsing int %s, using default 0: %v", intStr, err)
		return 0
	}
	return intValue
}
