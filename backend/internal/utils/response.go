package utils

import (
	"github.com/gin-gonic/gin"
)

// SendSuccess sends a success response
func SendSuccess(c *gin.Context, statusCode int, data interface{}) {
	c.<PERSON>(statusCode, data)
}

// SendCreated sends a created response
func SendCreated(c *gin.Context, data interface{}) {
	SendSuccess(c, 201, data)
}

// SendOK sends an OK response
func SendOK(c *gin.Context, data interface{}) {
	SendSuccess(c, 200, data)
}

// SendNoContent sends a no content response
func SendNoContent(c *gin.Context) {
	c.Status(204)
}

// SendAccepted sends an accepted response
func SendAccepted(c *gin.Context, data interface{}) {
	SendSuccess(c, 202, data)
}
