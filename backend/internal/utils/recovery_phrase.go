package utils

import (
	"crypto/rand"
	"math/big"
	"strings"
)

const (
	// RecoveryPhraseWordCount is the number of words in a recovery phrase
	RecoveryPhraseWordCount = 9
)

// Common English words for recovery phrases
var recoveryWords = []string{
	"apple", "banana", "orange", "grape", "lemon", "peach", "cherry", "melon", "kiwi", "pear",
	"dog", "cat", "bird", "fish", "lion", "tiger", "bear", "wolf", "fox", "deer",
	"red", "blue", "green", "yellow", "purple", "orange", "pink", "brown", "black", "white",
	"car", "bike", "train", "plane", "boat", "ship", "truck", "bus", "taxi", "van",
	"house", "home", "building", "office", "school", "store", "shop", "market", "hotel", "park",
	"book", "pen", "paper", "desk", "chair", "table", "door", "window", "wall", "floor",
	"sun", "moon", "star", "cloud", "rain", "snow", "wind", "storm", "thunder", "lightning",
	"water", "fire", "earth", "air", "metal", "wood", "stone", "rock", "sand", "soil",
	"river", "lake", "ocean", "sea", "mountain", "hill", "valley", "forest", "desert", "island",
	"time", "day", "night", "morning", "evening", "today", "tomorrow", "yesterday", "week", "month",
}

// GenerateRecoveryPhrase generates a random 9-word recovery phrase
func GenerateRecoveryPhrase() ([]string, error) {
	wordCount := len(recoveryWords)
	maxIndex := big.NewInt(int64(wordCount))
	phrase := make([]string, RecoveryPhraseWordCount)

	for i := 0; i < RecoveryPhraseWordCount; i++ {
		randomIndex, err := rand.Int(rand.Reader, maxIndex)
		if err != nil {
			return nil, err
		}
		phrase[i] = recoveryWords[randomIndex.Int64()]
	}

	return phrase, nil
}

// GenerateRecoveryPhraseString generates a random 9-word recovery phrase as a space-separated string
func GenerateRecoveryPhraseString() (string, error) {
	phrase, err := GenerateRecoveryPhrase()
	if err != nil {
		return "", err
	}
	return strings.Join(phrase, " "), nil
}
