package utils

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/meena/backend/internal/errors"
	"github.com/meena/backend/internal/models"
)

// SendError sends an error response
func SendError(c *gin.Context, statusCode int, code, message string, details []models.ErrorDetail, err error) {
	errors.SendError(c, statusCode, code, message, details, err)
}

// SendValidationError sends a validation error response
func SendValidationError(c *gin.Context, details []models.ErrorDetail) {
	errors.SendValidationError(c, details)
}

// SendNotFoundError sends a not found error response
func SendNotFoundError(c *gin.Context, resource, id string) {
	errors.SendNotFoundError(c, resource, id)
}

// SendUnauthorizedError sends an unauthorized error response
func SendUnauthorizedError(c *gin.Context) {
	errors.SendUnauthorizedError(c)
}

// SendForbiddenError sends a forbidden error response
func SendForbiddenError(c *gin.Context) {
	errors.SendForbiddenError(c)
}

// SendInternalError sends an internal error response
func SendInternalError(c *gin.Context, err error) {
	errors.SendInternalError(c, err)
}

// SendBadRequestError sends a bad request error response
func SendBadRequestError(c *gin.Context, message string) {
	errors.SendBadRequestError(c, message)
}

// SendConflictError sends a conflict error response
func SendConflictError(c *gin.Context, message string) {
	errors.SendConflictError(c, message)
}

// FormatBindingError converts gin's binding errors into a slice of ErrorDetail.
func FormatBindingError(err error) []models.ErrorDetail {
	var details []models.ErrorDetail
	if validationErrs, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrs {
			details = append(details, models.ErrorDetail{
				Field:   e.Field(),
				Message: fmt.Sprintf("Field validation for '%s' failed on the '%s' tag", e.Field(), e.Tag()),
				// Value:   e.Value(), // Value might be sensitive, consider omitting
			})
		}
	} else {
		details = append(details, models.ErrorDetail{Message: err.Error()})
	}
	return details
}
