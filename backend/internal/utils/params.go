package utils

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// ParseIntParam parses an integer parameter from the request with a default value.
func ParseIntParam(c *gin.Context, paramName string, defaultValue int) int {
	paramStr := c.Param(paramName)
	if paramStr == "" {
		// Try to get from query string
		paramStr = c.Query(paramName)
		if paramStr == "" {
			return defaultValue
		}
	}

	value, err := strconv.Atoi(paramStr)
	if err != nil {
		return defaultValue
	}

	return value
}
