package utils

import (
	"crypto/rand"
	"math/big"
	"strings"
)

const (
	// MeenaIDLength is the length of a Meena ID
	MeenaIDLength = 9

	// MeenaIDCharset is the character set used for Meena IDs
	MeenaIDCharset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
)

// GenerateMeenaID generates a random 9-character Meena ID
func GenerateMeenaID() (string, error) {
	return generateRandomString(MeenaIDLength, MeenaIDCharset)
}

// generateRandomString generates a random string of the specified length using the specified character set
func generateRandomString(length int, charset string) (string, error) {
	charsetLength := big.NewInt(int64(len(charset)))
	result := strings.Builder{}
	result.Grow(length)

	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			return "", err
		}
		result.WriteByte(charset[randomIndex.Int64()])
	}

	return result.String(), nil
}
