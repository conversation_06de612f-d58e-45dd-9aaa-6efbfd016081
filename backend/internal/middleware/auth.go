package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/meena/backend/internal/errors"
)

// JWTClaims represents the claims in a JWT
type JWTClaims struct {
	UserID     string `json:"user_id"`
	UserHandle string `json:"user_handle"`
	jwt.RegisteredClaims
}

// AuthMiddleware returns a middleware that checks for a valid JWT token
func AuthMiddleware(jwtSecret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			errors.SendUnauthorizedError(c)
			c.Abort()
			return
		}

		// Check if the Authorization header has the correct format
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			errors.SendError(c, http.StatusUnauthorized, "invalid_token", "Authorization header format must be Bearer {token}", nil, nil)
			c.Abort()
			return
		}

		// Get the token
		tokenString := parts[1]

		// Parse the token
		token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
			// Validate the signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}

			// Return the secret key
			return []byte(jwtSecret), nil
		})

		if err != nil {
			errors.SendError(c, http.StatusUnauthorized, "invalid_token", "Invalid or expired token", nil, err)
			c.Abort()
			return
		}

		// Check if the token is valid
		if !token.Valid {
			errors.SendError(c, http.StatusUnauthorized, "invalid_token", "Invalid token", nil, nil)
			c.Abort()
			return
		}

		// Get the claims
		claims, ok := token.Claims.(*JWTClaims)
		if !ok {
			errors.SendError(c, http.StatusUnauthorized, "invalid_token", "Invalid token claims", nil, nil)
			c.Abort()
			return
		}

		// Set the user ID and user handle in the context
		c.Set("user_id", claims.UserID)
		c.Set("user_handle", claims.UserHandle)

		c.Next()
	}
}

// ParseRefreshToken parses and validates a refresh token
func ParseRefreshToken(tokenString string, jwtSecret string) (*JWTClaims, error) {
	return ValidateToken(tokenString, jwtSecret)
}

// ValidateToken validates a JWT token and returns the claims
func ValidateToken(tokenString string, jwtSecret string) (*JWTClaims, error) {
	// Parse the token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Return the secret key
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	// Check if the token is valid
	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	// Get the claims
	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// GenerateTokens generates access and refresh tokens for a user
func GenerateTokens(userID, userHandle string, jwtSecret string, expirationHours time.Duration, refreshExpiration time.Duration) (string, string, int64, time.Time, error) {
	// Create access token claims
	expirationTime := time.Now().Add(expirationHours)
	claims := &JWTClaims{
		UserID:     userID,
		UserHandle: userHandle,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "meena-api",
			Subject:   userID,
		},
	}

	// Create the access token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	accessToken, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", "", 0, time.Time{}, err
	}

	// Create refresh token claims
	refreshExpirationTime := time.Now().Add(refreshExpiration)
	refreshClaims := &JWTClaims{
		UserID:     userID,
		UserHandle: userHandle,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(refreshExpirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "meena-api",
			Subject:   userID,
		},
	}

	// Create the refresh token
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", "", 0, time.Time{}, err
	}

	// Calculate expiration in seconds
	expiresIn := int64(expirationHours.Seconds())

	return accessToken, refreshTokenString, expiresIn, refreshExpirationTime, nil
}
