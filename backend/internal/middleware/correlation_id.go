package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CorrelationIDMiddleware generates and propagates a correlation ID for each request.
func CorrelationIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if a correlation ID is already present in the request header
		correlationID := c.<PERSON>eader("X-Correlation-ID")

		// If not present, generate a new one
		if correlationID == "" {
			correlationID = uuid.New().String()
		}

		// Set the correlation ID in the context for access in handlers and other middleware
		c.Set("correlation_id", correlationID)

		// Add the correlation ID to the response header
		c.Writer.Header().Set("X-Correlation-ID", correlationID)

		// Process the next middleware/handler
		c.Next()
	}
}
