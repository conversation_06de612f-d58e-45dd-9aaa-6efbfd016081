package middleware

import (
	stderrors "errors"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/meena/backend/internal/errors"
	"github.com/meena/backend/internal/logging"
	"github.com/meena/backend/internal/models"
	"go.uber.org/zap"
)

// ErrorMiddleware returns a middleware that handles errors
func ErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate trace ID
		traceID := uuid.New().String()
		c.Set("trace_id", traceID)

		// Process request
		c.Next()

		// Check if there are any errors
		if len(c.Errors) > 0 {
			// Get the first error
			err := c.Errors[0].Err

			// Default error values
			statusCode := http.StatusInternalServerError
			errorCode := "internal_error"
			errorMessage := "An unexpected error occurred"
			var details []models.ErrorDetail

			// Check for custom error types
			var appErr *errors.AppError
			if stderrors.As(err, &appErr) {
				statusCode = appErr.StatusCode
				errorCode = appErr.Code
				errorMessage = appErr.Message
				details = appErr.Details

				// Log error with trace ID
				fields := []zap.Field{
					zap.String("trace_id", traceID),
					zap.String("code", errorCode),
					zap.Int("status", statusCode),
				}

				// Add user ID if available
				if userID, exists := c.Get("user_id"); exists {
					if idStr, ok := userID.(string); ok {
						fields = append(fields, zap.String("user_id", idStr))
					}
				}

				// Add request information
				fields = append(fields, zap.Any("request", map[string]interface{}{
					"method": c.Request.Method,
					"path":   c.Request.URL.Path,
					"ip":     c.ClientIP(),
				}))

				if appErr.Err != nil {
					logging.Error(errorMessage, appErr.Err, fields...)
				} else {
					logging.Warn(errorMessage, fields...)
				}
			} else {
				// Handle unexpected errors
				fields := []zap.Field{
					zap.String("trace_id", traceID),
					zap.String("code", errorCode),
					zap.Int("status", statusCode),
					zap.Any("request", map[string]interface{}{
						"method": c.Request.Method,
						"path":   c.Request.URL.Path,
						"ip":     c.ClientIP(),
					}),
				}

				// Add user ID if available
				if userID, exists := c.Get("user_id"); exists {
					if idStr, ok := userID.(string); ok {
						fields = append(fields, zap.String("user_id", idStr))
					}
				}

				// Add stack trace in development mode
				if gin.Mode() != gin.ReleaseMode {
					fields = append(fields, zap.String("stack", string(debug.Stack())))
				}

				logging.Error("Unexpected error", err, fields...)
			}

			// Create error response
			errorResponse := models.ErrorResponse{
				Error: models.ErrorDetails{
					Code:    errorCode,
					Message: errorMessage,
					Details: details,
					TraceID: traceID,
				},
			}

			// Add documentation URL for non-internal errors
			if errorCode != "internal_error" {
				errorResponse.Error.DocumentationURL = "https://docs.meena.com/errors/" + errorCode
			}

			// Return error response
			c.JSON(statusCode, errorResponse)
			c.Abort()
		}
	}
}
