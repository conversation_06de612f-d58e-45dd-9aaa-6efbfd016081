package services

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/meena/backend/internal/models"
)

// ConversationService provides conversation-related functionality
type ConversationService struct{}

// NewConversationService creates a new ConversationService
func NewConversationService() *ConversationService {
	return &ConversationService{}
}

// GetConversations gets a user's conversations
func (s *ConversationService) GetConversations(ctx context.Context, userID string) ([]models.ConversationListItem, error) {
	// In a real implementation, this would fetch conversations from the database
	// For now, we'll just return mock data
	conversations := []models.ConversationListItem{
		{
			ID:               uuid.New().String(),
			Type:             models.OneToOne,
			Name:             "<PERSON>",
			AvatarURL:        "https://via.placeholder.com/150",
			ParticipantCount: 2,
			LastMessage: &models.MessagePreview{
				ID:         uuid.New().String(),
				SenderID:   uuid.New().String(),
				SenderName: "<PERSON>",
				Type:       models.TextMessage,
				Content:    "Hello, how are you?",
				CreatedAt:  time.Now().Add(-5 * time.Minute),
			},
			LastMessageTime: time.Now().Add(-5 * time.Minute),
			UnreadCount:     2,
			IsEncrypted:     true,
			IsArchived:      false,
			IsMuted:         false,
			IsPinned:        true,
		},
		{
			ID:               uuid.New().String(),
			Type:             models.Group,
			Name:             "Team Chat",
			AvatarURL:        "https://via.placeholder.com/150",
			ParticipantCount: 5,
			LastMessage: &models.MessagePreview{
				ID:           uuid.New().String(),
				SenderID:     uuid.New().String(),
				SenderName:   "Jane Smith",
				Type:         models.ImageMessage,
				Content:      "Check out this image!",
				ThumbnailURL: "https://via.placeholder.com/150",
				CreatedAt:    time.Now().Add(-1 * time.Hour),
			},
			LastMessageTime: time.Now().Add(-1 * time.Hour),
			UnreadCount:     10,
			IsEncrypted:     false,
			IsArchived:      false,
			IsMuted:         true,
			IsPinned:        false,
		},
		{
			ID:               uuid.New().String(),
			Type:             models.Channel,
			Name:             "Announcements",
			AvatarURL:        "https://via.placeholder.com/150",
			ParticipantCount: 100,
			LastMessage: &models.MessagePreview{
				ID:         uuid.New().String(),
				SenderID:   uuid.New().String(),
				SenderName: "Admin",
				Type:       models.TextMessage,
				Content:    "Important announcement: The system will be down for maintenance tomorrow.",
				CreatedAt:  time.Now().Add(-24 * time.Hour),
			},
			LastMessageTime: time.Now().Add(-24 * time.Hour),
			UnreadCount:     1,
			IsEncrypted:     false,
			IsArchived:      false,
			IsMuted:         false,
			IsPinned:        false,
		},
	}

	return conversations, nil
}

// GetConversation gets a specific conversation
func (s *ConversationService) GetConversation(ctx context.Context, conversationID string) (*models.Conversation, error) {
	// In a real implementation, this would fetch the conversation from the database
	// For now, we'll just return mock data
	conversation := &models.Conversation{
		ID:             conversationID,
		Type:           models.Group,
		Name:           "Team Chat",
		AvatarURL:      "https://via.placeholder.com/150",
		ParticipantIDs: []string{uuid.New().String(), uuid.New().String(), uuid.New().String()},
		CreatorID:      uuid.New().String(),
		LastMessage: &models.Message{
			ID:             uuid.New().String(),
			ConversationID: conversationID,
			SenderID:       uuid.New().String(),
			Type:           models.TextMessage,
			Content:        "Hello, team!",
			Status:         models.Delivered,
			CreatedAt:      time.Now().Add(-1 * time.Hour),
			UpdatedAt:      time.Now().Add(-1 * time.Hour),
		},
		LastMessageTime: time.Now().Add(-1 * time.Hour),
		UnreadCount:     5,
		IsEncrypted:     false,
		IsArchived:      false,
		IsMuted:         false,
		IsPinned:        true,
		CreatedAt:       time.Now().Add(-7 * 24 * time.Hour),
		UpdatedAt:       time.Now().Add(-1 * time.Hour),
	}

	return conversation, nil
}

// CreateConversation creates a new conversation
func (s *ConversationService) CreateConversation(ctx context.Context, userID string, req models.CreateConversationRequest) (*models.Conversation, error) {
	// In a real implementation, this would create a conversation in the database
	// For now, we'll just return mock data
	conversationID := uuid.New().String()

	// Add the creator to the participants if not already included
	participantIDs := req.ParticipantIDs
	hasCreator := false
	for _, id := range participantIDs {
		if id == userID {
			hasCreator = true
			break
		}
	}
	if !hasCreator {
		participantIDs = append(participantIDs, userID)
	}

	conversation := &models.Conversation{
		ID:             conversationID,
		Type:           req.Type,
		Name:           req.Name,
		AvatarURL:      req.AvatarURL,
		ParticipantIDs: participantIDs,
		CreatorID:      userID,
		UnreadCount:    0,
		IsEncrypted:    req.IsEncrypted,
		IsArchived:     false,
		IsMuted:        false,
		IsPinned:       false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	return conversation, nil
}

// UpdateConversation updates a conversation
func (s *ConversationService) UpdateConversation(ctx context.Context, conversationID string, req models.UpdateConversationRequest) (*models.Conversation, error) {
	// In a real implementation, this would update the conversation in the database
	// For now, we'll just return mock data

	// Get the conversation first
	conversation, err := s.GetConversation(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	// Update the conversation
	if req.Name != "" {
		conversation.Name = req.Name
	}
	if req.AvatarURL != "" {
		conversation.AvatarURL = req.AvatarURL
	}
	if req.IsMuted != nil {
		conversation.IsMuted = *req.IsMuted
	}
	if req.IsPinned != nil {
		conversation.IsPinned = *req.IsPinned
	}
	conversation.UpdatedAt = time.Now()

	return conversation, nil
}

// AddParticipants adds participants to a conversation
func (s *ConversationService) AddParticipants(ctx context.Context, conversationID string, participantIDs []string) error {
	// In a real implementation, this would add participants to the conversation in the database
	// For now, we'll just return success
	return nil
}

// RemoveParticipant removes a participant from a conversation
func (s *ConversationService) RemoveParticipant(ctx context.Context, conversationID, participantID string) error {
	// In a real implementation, this would remove a participant from the conversation in the database
	// For now, we'll just return success
	return nil
}

// LeaveConversation removes the current user from a conversation
func (s *ConversationService) LeaveConversation(ctx context.Context, userID, conversationID string) error {
	// In a real implementation, this would remove the user from the conversation in the database
	// For now, we'll just return success
	return nil
}

// ArchiveConversation archives a conversation
func (s *ConversationService) ArchiveConversation(ctx context.Context, userID, conversationID string) error {
	// In a real implementation, this would archive the conversation in the database
	// For now, we'll just return success
	return nil
}

// UnarchiveConversation unarchives a conversation
func (s *ConversationService) UnarchiveConversation(ctx context.Context, userID, conversationID string) error {
	// In a real implementation, this would unarchive the conversation in the database
	// For now, we'll just return success
	return nil
}
