package services

import (
	"context"

	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/models"
)

// ContactGroupService handles contact group-related business logic
type ContactGroupService struct {
	contactGroupRepo *database.ContactGroupRepository
}

// NewContactGroupService creates a new ContactGroupService
func NewContactGroupService(contactGroupRepo *database.ContactGroupRepository) *ContactGroupService {
	return &ContactGroupService{contactGroupRepo: contactGroupRepo}
}

// GetContactGroups gets the user's contact groups
func (s *ContactGroupService) GetContactGroups(ctx context.Context, userID string, limit, offset int) (*models.ContactGroupListResponse, error) {
	// Fetch contact groups from database
	groups, err := s.contactGroupRepo.GetContactGroups(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert to ContactGroup slice
	resultGroups := make([]models.ContactGroup, len(groups))
	for i, group := range groups {
		// Extract member IDs
		memberIDs := make([]string, 0)
		if group.Members != nil {
			for _, member := range group.Members {
				memberIDs = append(memberIDs, member.ID)
			}
		}

		resultGroups[i] = models.ContactGroup{
			ID:          group.ID,
			UserID:      group.UserID,
			Name:        group.Name,
			Description: group.Description,
			MemberIDs:   memberIDs,
			MemberCount: group.MemberCount,
			CreatedAt:   group.CreatedAt,
			UpdatedAt:   group.UpdatedAt,
		}
	}

	return &models.ContactGroupListResponse{
		Groups:     resultGroups,
		TotalCount: len(resultGroups),
	}, nil
}

// GetContactGroup gets a contact group by ID
func (s *ContactGroupService) GetContactGroup(ctx context.Context, userID, groupID string) (*models.ContactGroup, error) {
	// Fetch contact group from database
	group, err := s.contactGroupRepo.GetContactGroup(ctx, userID, groupID)
	if err != nil {
		return nil, err
	}

	// Extract member IDs
	memberIDs := make([]string, 0)
	if group.Members != nil {
		for _, member := range group.Members {
			memberIDs = append(memberIDs, member.ID)
		}
	}

	// Convert to ContactGroup
	result := &models.ContactGroup{
		ID:          group.ID,
		UserID:      group.UserID,
		Name:        group.Name,
		Description: group.Description,
		MemberIDs:   memberIDs,
		MemberCount: group.MemberCount,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}

	return result, nil
}

// CreateContactGroup creates a new contact group
func (s *ContactGroupService) CreateContactGroup(ctx context.Context, userID string, req models.CreateContactGroupRequest) (*models.ContactGroup, error) {
	// Create contact group in database
	group, err := s.contactGroupRepo.CreateContactGroup(ctx, userID, req)
	if err != nil {
		return nil, err
	}

	// Extract member IDs
	memberIDs := make([]string, 0)
	if group.Members != nil {
		for _, member := range group.Members {
			memberIDs = append(memberIDs, member.ID)
		}
	}

	// Convert to ContactGroup
	result := &models.ContactGroup{
		ID:          group.ID,
		UserID:      group.UserID,
		Name:        group.Name,
		Description: group.Description,
		MemberIDs:   memberIDs,
		MemberCount: group.MemberCount,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}

	// Add members if specified
	if req.MemberIDs != nil && len(req.MemberIDs) > 0 {
		for _, memberID := range req.MemberIDs {
			err := s.contactGroupRepo.AddContactToGroup(ctx, userID, group.ID, memberID)
			if err != nil {
				// Log error but continue
				// log.Printf("Error adding contact to group: %v", err)
			}
		}

		// Refresh the group to get updated members
		group, err = s.contactGroupRepo.GetContactGroup(ctx, userID, group.ID)
		if err == nil {
			memberIDs = make([]string, 0)
			if group.Members != nil {
				for _, member := range group.Members {
					memberIDs = append(memberIDs, member.ID)
				}
			}
			result.MemberIDs = memberIDs
		}
	}

	return result, nil
}

// UpdateContactGroup updates a contact group
func (s *ContactGroupService) UpdateContactGroup(ctx context.Context, userID, groupID string, req models.UpdateContactGroupRequest) (*models.ContactGroup, error) {
	// Update contact group in database
	group, err := s.contactGroupRepo.UpdateContactGroup(ctx, userID, groupID, req)
	if err != nil {
		return nil, err
	}

	// Extract member IDs
	memberIDs := make([]string, 0)
	if group.Members != nil {
		for _, member := range group.Members {
			memberIDs = append(memberIDs, member.ID)
		}
	}

	// Convert to ContactGroup
	result := &models.ContactGroup{
		ID:          group.ID,
		UserID:      group.UserID,
		Name:        group.Name,
		Description: group.Description,
		MemberIDs:   memberIDs,
		MemberCount: group.MemberCount,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}

	return result, nil
}

// DeleteContactGroup deletes a contact group
func (s *ContactGroupService) DeleteContactGroup(ctx context.Context, userID, groupID string) error {
	// Delete contact group from database
	return s.contactGroupRepo.DeleteContactGroup(ctx, userID, groupID)
}

// AddContactToGroup adds a contact to a group
func (s *ContactGroupService) AddContactToGroup(ctx context.Context, userID, groupID, contactID string) (*models.ContactGroup, error) {
	// Add contact to group in database
	err := s.contactGroupRepo.AddContactToGroup(ctx, userID, groupID, contactID)
	if err != nil {
		return nil, err
	}

	// Get updated group
	return s.GetContactGroup(ctx, userID, groupID)
}

// RemoveContactFromGroup removes a contact from a group
func (s *ContactGroupService) RemoveContactFromGroup(ctx context.Context, userID, groupID, contactID string) (*models.ContactGroup, error) {
	// Remove contact from group in database
	err := s.contactGroupRepo.RemoveContactFromGroup(ctx, userID, groupID, contactID)
	if err != nil {
		return nil, err
	}

	// Get updated group
	return s.GetContactGroup(ctx, userID, groupID)
}
