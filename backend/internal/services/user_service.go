package services

import (
	"context"

	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/models"
)

// UserService provides user-related functionality
type UserService struct {
	userRepo *database.UserRepository
}

// NewUserService creates a new UserService
func NewUserService(userRepo *database.UserRepository) *UserService {
	return &UserService{userRepo: userRepo}
}

// GetProfile gets a user's profile
func (s *UserService) GetProfile(ctx context.Context, userID string) (*models.UserProfile, error) {
	// Fetch user from database
	user, err := s.userRepo.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert to UserProfile
	profile := &models.UserProfile{
		ID:                 user.ID,
		UserHandle:         user.UserHandle,
		DisplayName:        user.DisplayName,
		Bio:                user.Bio,
		AvatarURL:          user.AvatarURL,
		VerificationStatus: user.VerificationStatus,
		IsVerified:         user.IsVerified,
		IsGoldMember:       user.IsGoldMember,
		LastActive:         user.LastActive,
		CreatedAt:          user.CreatedAt,
		FollowerCount:      user.FollowerCount,
		FollowingCount:     user.FollowingCount,
	}

	return profile, nil
}

// GetUserByHandle gets a user by their handle
func (s *UserService) GetUserByHandle(ctx context.Context, userHandle string) (*models.UserProfile, error) {
	// Fetch user from database
	user, err := s.userRepo.GetUserByHandle(ctx, userHandle)
	if err != nil {
		return nil, err
	}

	// Convert to UserProfile
	profile := &models.UserProfile{
		ID:                 user.ID,
		UserHandle:         user.UserHandle,
		DisplayName:        user.DisplayName,
		Bio:                user.Bio,
		AvatarURL:          user.AvatarURL,
		VerificationStatus: user.VerificationStatus,
		IsVerified:         user.IsVerified,
		IsGoldMember:       user.IsGoldMember,
		LastActive:         user.LastActive,
		CreatedAt:          user.CreatedAt,
		FollowerCount:      user.FollowerCount,
		FollowingCount:     user.FollowingCount,
	}

	return profile, nil
}

// UpdateProfile updates a user's profile
func (s *UserService) UpdateProfile(ctx context.Context, userID string, req models.UpdateProfileRequest) (*models.UserProfile, error) {
	// Update user in database
	user, err := s.userRepo.UpdateProfile(ctx, userID, req)
	if err != nil {
		return nil, err
	}

	// Convert to UserProfile
	profile := &models.UserProfile{
		ID:                 user.ID,
		UserHandle:         user.UserHandle,
		DisplayName:        user.DisplayName,
		Bio:                user.Bio,
		AvatarURL:          user.AvatarURL,
		VerificationStatus: user.VerificationStatus,
		IsVerified:         user.IsVerified,
		IsGoldMember:       user.IsGoldMember,
		LastActive:         user.LastActive,
		CreatedAt:          user.CreatedAt,
		FollowerCount:      user.FollowerCount,
		FollowingCount:     user.FollowingCount,
	}

	return profile, nil
}

// SearchUsers searches for users
func (s *UserService) SearchUsers(ctx context.Context, query string, limit, offset int) ([]models.UserProfile, int, error) {
	// Search users in database
	users, err := s.userRepo.SearchUsers(ctx, query, limit, offset)
	if err != nil {
		return nil, 0, err
	}

	// Convert to UserProfile slice
	profiles := make([]models.UserProfile, len(users))
	for i, user := range users {
		profiles[i] = models.UserProfile{
			ID:                 user.ID,
			UserHandle:         user.UserHandle,
			DisplayName:        user.DisplayName,
			Bio:                user.Bio,
			AvatarURL:          user.AvatarURL,
			VerificationStatus: user.VerificationStatus,
			IsVerified:         user.IsVerified,
			IsGoldMember:       user.IsGoldMember,
			LastActive:         user.LastActive,
			CreatedAt:          user.CreatedAt,
			FollowerCount:      user.FollowerCount,
			FollowingCount:     user.FollowingCount,
		}
	}

	return profiles, len(profiles), nil
}
