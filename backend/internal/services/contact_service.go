package services

import (
	"context"
	"sort"
	"time"

	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/models"
)

// ContactService provides contact-related functionality
type ContactService struct {
	userService *UserService
	contactRepo *database.ContactRepository
}

// NewContactService creates a new ContactService
func NewContactService(userService *UserService, contactRepo *database.ContactRepository) *ContactService {
	return &ContactService{
		userService: userService,
		contactRepo: contactRepo,
	}
}

// GetContacts gets a user's contacts
func (s *ContactService) GetContacts(ctx context.Context, userID string) ([]models.ContactListItem, error) {
	// Fetch contacts from database
	contacts, err := s.contactRepo.GetContacts(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert to ContactListItem slice
	contactItems := make([]models.ContactListItem, len(contacts))
	for i, contact := range contacts {
		// Determine if user is online (last active within 5 minutes)
		isOnline := false
		if contact.User != nil {
			isOnline = contact.User.LastActive.After(contact.User.LastActive.Add(-5 * time.Minute))
		}

		contactItems[i] = models.ContactListItem{
			ID:           contact.ID,
			UserID:       contact.UserID,
			ContactID:    contact.ContactID,
			DisplayName:  contact.DisplayName,
			UserHandle:   contact.User.UserHandle,
			AvatarURL:    contact.User.AvatarURL,
			Relationship: contact.Relationship,
			LastActive:   contact.User.LastActive,
			IsOnline:     isOnline,
		}
	}

	return contactItems, nil
}

// GetContact gets a specific contact
func (s *ContactService) GetContact(ctx context.Context, userID, contactID string) (*models.Contact, error) {
	// Fetch contact from database
	contact, err := s.contactRepo.GetContact(ctx, userID, contactID)
	if err != nil {
		return nil, err
	}

	return contact, nil
}

// AddContact adds a contact
func (s *ContactService) AddContact(ctx context.Context, userID string, req models.AddContactRequest) (*models.Contact, error) {
	// Add contact to database
	contact, err := s.contactRepo.AddContact(ctx, userID, req)
	if err != nil {
		return nil, err
	}

	return contact, nil
}

// UpdateContact updates a contact
func (s *ContactService) UpdateContact(ctx context.Context, userID, contactID string, req models.UpdateContactRequest) (*models.Contact, error) {
	// Update contact in database
	contact, err := s.contactRepo.UpdateContact(ctx, userID, contactID, req)
	if err != nil {
		return nil, err
	}

	return contact, nil
}

// BlockContact blocks a contact
func (s *ContactService) BlockContact(ctx context.Context, userID, contactID string) error {
	// Block contact in database
	return s.contactRepo.BlockContact(ctx, userID, contactID)
}

// UnblockContact unblocks a contact
func (s *ContactService) UnblockContact(ctx context.Context, userID, contactID string) error {
	// Unblock contact in database
	return s.contactRepo.UnblockContact(ctx, userID, contactID)
}

// RemoveContact removes a contact
func (s *ContactService) RemoveContact(ctx context.Context, userID, contactID string) error {
	// Remove contact from database
	return s.contactRepo.RemoveContact(ctx, userID, contactID)
}

// GetFavoriteContacts gets a user's favorite contacts
func (s *ContactService) GetFavoriteContacts(ctx context.Context, userID string) ([]models.ContactListItem, error) {
	// Fetch favorite contacts from database
	contacts, err := s.contactRepo.GetFavoriteContacts(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert to ContactListItem slice
	contactItems := make([]models.ContactListItem, len(contacts))
	for i, contact := range contacts {
		// Determine if user is online (last active within 5 minutes)
		isOnline := false
		if contact.User != nil {
			isOnline = contact.User.LastActive.After(contact.User.LastActive.Add(-5 * time.Minute))
		}

		contactItems[i] = models.ContactListItem{
			ID:           contact.ID,
			UserID:       contact.UserID,
			ContactID:    contact.ContactID,
			DisplayName:  contact.DisplayName,
			UserHandle:   contact.User.UserHandle,
			AvatarURL:    contact.User.AvatarURL,
			Relationship: contact.Relationship,
			LastActive:   contact.User.LastActive,
			IsOnline:     isOnline,
		}
	}

	return contactItems, nil
}

// GetRecentContacts gets a user's recent contacts
func (s *ContactService) GetRecentContacts(ctx context.Context, userID string, limit int) ([]models.ContactListItem, error) {
	// For now, we'll just return all contacts
	// In a real implementation, this would fetch contacts sorted by last interaction
	contacts, err := s.contactRepo.GetContacts(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Sort contacts by last interaction time (most recent first)
	// This is a simplified implementation - in a real app, this would be done in the database query
	sort.Slice(contacts, func(i, j int) bool {
		// If either contact doesn't have LastInteractionAt, put the one that does have it first
		if contacts[i].LastInteractionAt == nil {
			return false
		}
		if contacts[j].LastInteractionAt == nil {
			return true
		}
		// Otherwise, sort by LastInteractionAt (most recent first)
		return contacts[i].LastInteractionAt.After(*contacts[j].LastInteractionAt)
	})

	// Limit the number of contacts
	if len(contacts) > limit {
		contacts = contacts[:limit]
	}

	// Convert to ContactListItem slice
	contactItems := make([]models.ContactListItem, len(contacts))
	for i, contact := range contacts {
		// Determine if user is online (last active within 5 minutes)
		isOnline := false
		if contact.User != nil {
			isOnline = contact.User.LastActive.After(contact.User.LastActive.Add(-5 * time.Minute))
		}

		contactItems[i] = models.ContactListItem{
			ID:           contact.ID,
			UserID:       contact.UserID,
			ContactID:    contact.ContactID,
			DisplayName:  contact.DisplayName,
			UserHandle:   contact.User.UserHandle,
			AvatarURL:    contact.User.AvatarURL,
			Relationship: contact.Relationship,
			LastActive:   contact.User.LastActive,
			IsOnline:     isOnline,
		}
	}

	return contactItems, nil
}
