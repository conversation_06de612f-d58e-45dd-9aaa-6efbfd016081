package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/meena/backend/internal/config"
	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/middleware"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/utils"
	"golang.org/x/crypto/bcrypt"
)

// ErrInvalidRecoveryCredentials is returned when the provided recovery credentials are not valid.
var ErrInvalidRecoveryCredentials = errors.New("invalid recovery credentials")

// ErrInvalidCredentials is returned when the provided login credentials are not valid.
var ErrInvalidCredentials = errors.New("invalid credentials")

// AuthService provides authentication-related functionality
type AuthService struct {
	userRepo         *database.UserRepository
	refreshTokenRepo *database.RefreshTokenRepository
	config           *config.Config
}

// NewAuthService creates a new AuthService
func NewAuthService(userRepo *database.UserRepository, refreshTokenRepo *database.RefreshTokenRepository, cfg *config.Config) *AuthService {
	return &AuthService{userRepo: userRepo, refreshTokenRepo: refreshTokenRepo, config: cfg}
}

// Login authenticates a user and returns tokens
func (s *AuthService) Login(ctx context.Context, req models.LoginRequest) (*models.AuthResponse, error) {
	// Get user by identifier (handle, email, or phone)
	user, err := s.userRepo.GetUserByIdentifier(ctx, req.Identifier)
	if err != nil {
		if errors.Is(err, database.ErrUserNotFound) {
			return nil, ErrInvalidCredentials
		}
		// For other errors, return as is
		return nil, err
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		// Check if the error is due to mismatched hash and password (incorrect password)
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return nil, ErrInvalidCredentials
		}
		// For other bcrypt errors, return the original error
		return nil, err
	}

	// Update last seen
	err = s.userRepo.UpdateLastSeen(ctx, user.ID)
	if err != nil {
		// Log error but continue
		log.Printf("Error updating last seen for user %s: %v", user.ID, err)
	}

	// Generate tokens
	accessToken, refreshToken, expiresIn, refreshTokenExpiresAt, err := middleware.GenerateTokens(user.ID, user.UserHandle, s.config.JWT.Secret, s.config.JWT.ExpirationHours, s.config.JWT.RefreshExpiration)
	if err != nil {
		return nil, err
	}

	// Hash and store refresh token
	tokenHash := utils.HashToken(refreshToken, s.config.JWT.RefreshTokenPepper)
	if err := s.refreshTokenRepo.StoreToken(ctx, user.ID, tokenHash, refreshTokenExpiresAt); err != nil {
		return nil, err
	}

	// Create response
	response := &models.AuthResponse{
		User: models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		},
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    expiresIn,
	}

	return response, nil
}

// Register creates a new user account
func (s *AuthService) Register(ctx context.Context, req models.RegisterRequest) (*models.AuthResponse, error) {
	// Generate a recovery phrase if not provided
	recoveryPhrase := req.RecoveryPhrase
	var err error
	if recoveryPhrase == "" {
		recoveryPhrase, err = utils.GenerateRecoveryPhraseString()
		if err != nil {
			return nil, err
		}
	}

	// Create user in database
	user, err := s.userRepo.CreateUser(ctx, req, recoveryPhrase)
	if err != nil {
		return nil, err
	}

	// Generate tokens
	accessToken, refreshToken, expiresIn, refreshTokenExpiresAt, err := middleware.GenerateTokens(user.ID, user.UserHandle, s.config.JWT.Secret, s.config.JWT.ExpirationHours, s.config.JWT.RefreshExpiration)
	if err != nil {
		return nil, err
	}

	// Hash and store refresh token
	tokenHash := utils.HashToken(refreshToken, s.config.JWT.RefreshTokenPepper)
	if err := s.refreshTokenRepo.StoreToken(ctx, user.ID, tokenHash, refreshTokenExpiresAt); err != nil {
		return nil, err
	}

	// Create response
	response := &models.AuthResponse{
		User: models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		},
		AccessToken:    accessToken,
		RefreshToken:   refreshToken,
		ExpiresIn:      expiresIn,
		RecoveryPhrase: recoveryPhrase,
	}

	return response, nil
}

// RefreshToken refreshes an access token
func (s *AuthService) RefreshToken(ctx context.Context, refreshToken string) (*models.AuthResponse, error) {
	// Parse and validate the refresh token
	claims, err := middleware.ParseRefreshToken(refreshToken, s.config.JWT.Secret)
	if err != nil {
		return nil, errors.New("invalid refresh token")
	}

	// Get and validate refresh token from database
	tokenHash := utils.HashToken(refreshToken, s.config.JWT.RefreshTokenPepper)
	token, err := s.refreshTokenRepo.GetToken(ctx, tokenHash)
	if err != nil {
		return nil, errors.New("invalid refresh token")
	}

	// Check if token is valid
	if !token.IsValid() {
		return nil, errors.New("refresh token is expired or has been used")
	}

	// Mark the current token as used
	if err := s.refreshTokenRepo.MarkTokenAsUsed(ctx, tokenHash); err != nil {
		return nil, err
	}

	// Get user from database
	user, err := s.userRepo.GetUserByID(ctx, claims.UserID)
	if err != nil {
		return nil, err
	}

	// Update last seen
	err = s.userRepo.UpdateLastSeen(ctx, user.ID)
	if err != nil {
		// Log error but continue
		// log.Printf("Error updating last seen: %v", err)
	}

	// Generate new tokens
	accessToken, newRefreshToken, expiresIn, refreshTokenExpiresAt, err := middleware.GenerateTokens(user.ID, user.UserHandle, s.config.JWT.Secret, s.config.JWT.ExpirationHours, s.config.JWT.RefreshExpiration)
	if err != nil {
		return nil, err
	}

	// Store new refresh token
	newTokenHash := utils.HashToken(newRefreshToken, s.config.JWT.RefreshTokenPepper)
	if err := s.refreshTokenRepo.StoreToken(ctx, user.ID, newTokenHash, refreshTokenExpiresAt); err != nil {
		return nil, err
	}

	// Create response
	response := &models.AuthResponse{
		User: models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		},
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    expiresIn,
	}

	return response, nil
}

// InitiateAccountRecovery initiates the account recovery process using user handle, recovery phrase, and PIN.
func (s *AuthService) InitiateAccountRecovery(ctx context.Context, userHandle string, recoveryPhrase string, recoveryPin string) (string, error) {
	user, err := s.userRepo.GetUserByHandle(ctx, userHandle) // Changed from FindUserByUserHandle as GetUserByHandle is available
	if err != nil {
		if errors.Is(err, database.ErrUserNotFound) { // Assuming ErrUserNotFound is defined in database package
			return "", database.ErrUserNotFound
		}
		return "", err
	}

	valid, err := s.userRepo.VerifyRecoveryInfo(ctx, user.ID, recoveryPhrase, recoveryPin)
	if err != nil {
		// This could be a bcrypt processing error or other unexpected issue
		return "", err
	}
	if !valid {
		return "", ErrInvalidRecoveryCredentials
	}

	// Generate a secure random plaintext string for recoverySessionToken
	tokenBytes := make([]byte, 32) // 32 bytes = 256 bits
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", err
	}
	recoverySessionToken := hex.EncodeToString(tokenBytes)

	// Get RecoverySessionTokenLifetime from config
	recoverySessionTokenLifetime := s.config.JWT.RecoverySessionTokenLifetime
	if recoverySessionTokenLifetime == 0 { // Default if not set or zero
		recoverySessionTokenLifetime = 10 * time.Minute
	}

	expiresAt := time.Now().Add(recoverySessionTokenLifetime)
	hashedToken := utils.HashToken(recoverySessionToken, s.config.JWT.RefreshTokenPepper)

	err = s.userRepo.StoreRecoveryToken(ctx, user.ID, hashedToken, expiresAt)
	if err != nil {
		return "", err
	}

	return recoverySessionToken, nil
}

// ResetPassword resets a user's password
func (s *AuthService) ResetPassword(ctx context.Context, token string, newPassword string) error {
	hashedToken := utils.HashToken(token, s.config.JWT.RefreshTokenPepper)

	userID, err := s.userRepo.VerifyRecoveryToken(ctx, hashedToken)
	if err != nil {
		return err // e.g., token not found, expired, or already used
	}

	newPasswordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	err = s.userRepo.UpdatePassword(ctx, userID, string(newPasswordHash))
	if err != nil {
		return err
	}

	err = s.userRepo.MarkRecoveryTokenAsUsed(ctx, hashedToken)
	if err != nil {
		// Log this error and return, as password reset should fail if token cannot be marked as used.
		log.Printf("Error marking recovery token as used for user %s: %v", userID, err)
		return fmt.Errorf("failed to mark recovery token as used: %w", err)
	}

	err = s.refreshTokenRepo.RevokeAllUserTokens(ctx, userID)
	if err != nil {
		// Log this error as well, but the primary operation (password reset) succeeded
		// log.Printf("Error revoking all user refresh tokens: %v", err)
	}

	return nil
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(ctx context.Context, userID string, req models.PasswordChangeRequest) error {
	// Verify current password
	verified, err := s.userRepo.VerifyPassword(ctx, userID, req.CurrentPassword)
	if err != nil {
		return err
	}

	if !verified {
		return errors.New("invalid current password")
	}

	// Change password
	err = s.userRepo.ChangePassword(ctx, userID, req.NewPassword)
	if err != nil {
		return err
	}

	return nil
}

// Logout revokes a refresh token
func (s *AuthService) Logout(ctx context.Context, refreshTokenString string) error {
	hashedToken := utils.HashToken(refreshTokenString, s.config.JWT.RefreshTokenPepper)

	// Before revoking, check if the token exists and is not already revoked.
	// This prevents errors if a client tries to logout multiple times with the same token.
	token, err := s.refreshTokenRepo.GetToken(ctx, hashedToken)
	if err != nil {
		// If token not found, it's effectively logged out from the server's perspective.
		// Depending on strictness, could return nil or an error.
		// For now, let's consider it an issue if we can't find it to revoke.
		return errors.New("refresh token not found")
	}

	if token.IsRevoked {
		// Token is already revoked, no action needed.
		// Return nil to indicate success as the desired state (logged out) is met.
		return nil // Or return an error like errors.New("token already revoked") if strictness is required
	}

	err = s.refreshTokenRepo.RevokeToken(ctx, hashedToken)
	if err != nil {
		return err // Propagate repository errors
	}

	return nil
}
