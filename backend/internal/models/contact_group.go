package models

import "time"

// ContactGroup represents a contact group
type ContactGroup struct {
	ID          string     `json:"id"`
	UserID      string     `json:"user_id"`
	Name        string     `json:"name"`
	Description string     `json:"description,omitempty"`
	MemberIDs   []string   `json:"member_ids"`
	MemberCount int        `json:"member_count"`
	Members     []*Contact `json:"members,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ContactGroupListResponse represents a response containing a list of contact groups
type ContactGroupListResponse struct {
	Groups     []ContactGroup `json:"groups"`
	TotalCount int            `json:"total_count"`
}

// CreateContactGroupRequest represents a request to create a contact group
type CreateContactGroupRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	MemberIDs   []string `json:"member_ids"`
}

// UpdateContactGroupRequest represents a request to update a contact group
type UpdateContactGroupRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}
