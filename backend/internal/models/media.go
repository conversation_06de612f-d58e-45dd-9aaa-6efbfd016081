package models

import "time"

// MediaType represents the type of media
type MediaType string

const (
	ImageMedia MediaType = "image"
	VideoMedia MediaType = "video"
	AudioMedia MediaType = "audio"
	FileMedia  MediaType = "file"
)

// Media represents a media file
type Media struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	Type        MediaType `json:"type"`
	URL         string    `json:"url"`
	ThumbnailURL string   `json:"thumbnail_url,omitempty"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	MimeType    string    `json:"mime_type"`
	Width       int       `json:"width,omitempty"`
	Height      int       `json:"height,omitempty"`
	Duration    int       `json:"duration,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
}

// UploadMediaRequest represents a request to upload media
type UploadMediaRequest struct {
	Type     MediaType `json:"type" binding:"required"`
	FileName string    `json:"file_name" binding:"required"`
	FileSize int64     `json:"file_size" binding:"required"`
	MimeType string    `json:"mime_type" binding:"required"`
	Width    int       `json:"width,omitempty"`
	Height   int       `json:"height,omitempty"`
	Duration int       `json:"duration,omitempty"`
}

// UploadMediaResponse represents a response to an upload media request
type UploadMediaResponse struct {
	Media       Media  `json:"media"`
	UploadURL   string `json:"upload_url"`
	DownloadURL string `json:"download_url"`
}

// GetMediaRequest represents a request to get media
type GetMediaRequest struct {
	MediaID string `json:"media_id" binding:"required"`
}

// DeleteMediaRequest represents a request to delete media
type DeleteMediaRequest struct {
	MediaID string `json:"media_id" binding:"required"`
}
