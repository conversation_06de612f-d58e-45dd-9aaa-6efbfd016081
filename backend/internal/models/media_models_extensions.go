package models

// EncryptedMediaUploadRequest represents a request to upload an encrypted media file
type EncryptedMediaUploadRequest struct {
	FileName    string `json:"file_name" binding:"required"`
	ContentType string `json:"content_type" binding:"required"`
	FileSize    int64  `json:"file_size" binding:"required"`
	IsEncrypted bool   `json:"is_encrypted" binding:"required"`
}

// EncryptedMediaUploadResponse represents a response to an encrypted media upload request
type EncryptedMediaUploadResponse struct {
	MediaID   string `json:"media_id"`
	UploadURL string `json:"upload_url"`
	ExpiresAt string `json:"expires_at"`
}

// ChunkedUploadInitRequest represents a request to initialize a chunked upload
type ChunkedUploadInitRequest struct {
	FileName    string `json:"file_name" binding:"required"`
	ContentType string `json:"content_type" binding:"required"`
	TotalSize   int64  `json:"total_size" binding:"required"`
	ChunkSize   int    `json:"chunk_size" binding:"required"`
	IsEncrypted bool   `json:"is_encrypted"`
}

// ChunkedUploadInitResponse represents a response to a chunked upload initialization request
type ChunkedUploadInitResponse struct {
	UploadID  string `json:"upload_id"`
	ExpiresAt string `json:"expires_at"`
}

// ChunkUploadResponse represents a response to a chunk upload request
type ChunkUploadResponse struct {
	ChunkIndex    int   `json:"chunk_index"`
	ReceivedSize  int64 `json:"received_size"`
	TotalReceived int64 `json:"total_received"`
}

// ChunkedUploadStatusResponse represents a response to a chunked upload status request
type ChunkedUploadStatusResponse struct {
	UploadID       string `json:"upload_id"`
	FileName       string `json:"file_name"`
	ContentType    string `json:"content_type"`
	TotalSize      int64  `json:"total_size"`
	UploadedChunks []int  `json:"uploaded_chunks"`
	TotalReceived  int64  `json:"total_received"`
	ExpiresAt      string `json:"expires_at"`
}
