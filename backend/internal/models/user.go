package models

import "time"

// User represents a user in the system
type User struct {
	ID               string    `json:"id"`
	UserHandle       string    `json:"user_handle"`
	Email            string    `json:"email,omitempty"`
	PhoneNumber      string    `json:"phone_number,omitempty"`
	DisplayName      string    `json:"display_name"`
	Bio              string    `json:"bio"`
	AvatarURL        string    `json:"avatar_url"`
	VerificationStatus string    `json:"verification_status"`
	IsVerified       bool      `json:"is_verified"` // Derived from VerificationStatus
	IsGoldMember     bool      `json:"is_gold_member"`
	LastActive       time.Time `json:"last_active"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	FollowerCount    int       `json:"follower_count"`
	FollowingCount   int       `json:"following_count"`
	PasswordHash     string    `json:"-"` // Not exposed in JSON
}

// UserProfile represents a user profile with public information
type UserProfile struct {
	ID                string    `json:"id"`
	UserHandle        string    `json:"user_handle"`
	DisplayName       string    `json:"display_name"`
	Bio               string    `json:"bio"`
	AvatarURL         string    `json:"avatar_url"`
	VerificationStatus string    `json:"verification_status"`
	IsVerified        bool      `json:"is_verified"` // Derived from VerificationStatus
	IsGoldMember      bool      `json:"is_gold_member"`
	LastActive        time.Time `json:"last_active"`
	CreatedAt         time.Time `json:"created_at"`
	FollowerCount     int       `json:"follower_count"`
	FollowingCount    int       `json:"following_count"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Identifier string `json:"identifier" binding:"required"` // Email, phone, or username
	Password   string `json:"password" binding:"required"`
}

// AuthResponse represents a response for authentication operations (login, register, token refresh)
type AuthResponse struct {
	User           UserProfile `json:"user"`
	AccessToken    string      `json:"access_token"`
	RefreshToken   string      `json:"refresh_token"`
	ExpiresIn      int64       `json:"expires_in"` // Seconds until token expires
	RecoveryPhrase string      `json:"recovery_phrase,omitempty"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	UserHandle     string `json:"user_handle"`
	Email          string `json:"email"`
	PhoneNumber    string `json:"phone_number"`
	Password       string `json:"password" binding:"required"`
	RecoveryPin    string `json:"recovery_pin"`
	RecoveryPhrase string `json:"recovery_phrase"`
	DisplayName    string `json:"display_name"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UpdateProfileRequest represents a profile update request
type UpdateProfileRequest struct {
	DisplayName string `json:"display_name"`
	Bio         string `json:"bio"`
	AvatarURL   string `json:"avatar_url"`
}

// PasswordResetRequest represents a password reset request
type PasswordResetRequest struct {
	Email string `json:"email" binding:"required"`
}

// PasswordResetConfirmRequest represents a password reset confirmation request
type PasswordResetConfirmRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// RecoveryRequest represents an account recovery request
type RecoveryRequest struct {
	UserHandle     string `json:"user_handle" binding:"required"`
	RecoveryPhrase string `json:"recovery_phrase" binding:"required"`
	RecoveryPin    string `json:"recovery_pin" binding:"required"`
	NewPassword    string `json:"new_password" binding:"required"`
}

// PasswordChangeRequest represents a password change request
type PasswordChangeRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required"`
}

// RecoveryResponse represents an account recovery response
type RecoveryResponse struct {
	RecoveryToken string `json:"recovery_token"`
}

// RecoveryConfirmRequest represents an account recovery confirmation request
type RecoveryConfirmRequest struct {
	RecoveryToken string `json:"recovery_token" binding:"required"`
	NewPassword   string `json:"new_password" binding:"required"`
}
