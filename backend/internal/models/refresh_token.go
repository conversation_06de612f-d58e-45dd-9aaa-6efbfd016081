package models

import (
	"time"

	"github.com/google/uuid"
)

// RefreshToken represents a refresh token in the database
type RefreshToken struct {
	TokenID   uuid.UUID  `json:"token_id"`
	UserID    string     `json:"user_id"`
	TokenHash string     `json:"token_hash"`
	IssuedAt  time.Time  `json:"issued_at"`
	ExpiresAt time.Time  `json:"expires_at"`
	IsRevoked bool       `json:"is_revoked"`
	CreatedAt time.Time  `json:"created_at"`
	UsedAt    *time.Time `json:"used_at,omitempty"`
	RevokedAt *time.Time `json:"revoked_at,omitempty"`
}

// IsValid checks if the refresh token is valid
func (rt *RefreshToken) IsValid() bool {
	return !rt.IsRevoked && rt.ExpiresAt.After(time.Now()) && rt.UsedAt == nil
}

// IsExpired checks if the refresh token is expired
func (rt *RefreshToken) IsExpired() bool {
	return time.Now().After(rt.ExpiresAt)
}

// IsUsed checks if the refresh token has been used
func (rt *RefreshToken) IsUsed() bool {
	return rt.UsedAt != nil
}
