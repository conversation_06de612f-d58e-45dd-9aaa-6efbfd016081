package models

import "time"

// MessageType represents the type of message
type MessageType string

const (
	TextMessage     MessageType = "text"
	ImageMessage    MessageType = "image"
	VideoMessage    MessageType = "video"
	AudioMessage    MessageType = "audio"
	FileMessage     MessageType = "file"
	LocationMessage MessageType = "location"
	SystemMessage   MessageType = "system"
)

// MessageStatus represents the status of a message
type MessageStatus string

const (
	Sending   MessageStatus = "sending"
	Sent      MessageStatus = "sent"
	Delivered MessageStatus = "delivered"
	Read      MessageStatus = "read"
	Failed    MessageStatus = "failed"
)

// Message represents a message in a conversation
type Message struct {
	ID             string        `json:"id"`
	ConversationID string        `json:"conversation_id"`
	SenderID       string        `json:"sender_id"`
	Type           MessageType   `json:"type"`
	Content        string        `json:"content"`
	MediaURL       string        `json:"media_url,omitempty"`
	ThumbnailURL   string        `json:"thumbnail_url,omitempty"`
	MediaSize      int64         `json:"media_size,omitempty"`
	MediaDuration  int           `json:"media_duration,omitempty"`
	MediaWidth     int           `json:"media_width,omitempty"`
	MediaHeight    int           `json:"media_height,omitempty"`
	Latitude       float64       `json:"latitude,omitempty"`
	Longitude      float64       `json:"longitude,omitempty"`
	ReplyToID      string        `json:"reply_to_id,omitempty"`
	ForwardedFrom  string        `json:"forwarded_from,omitempty"`
	Reactions      []Reaction    `json:"reactions,omitempty"`
	Status         MessageStatus `json:"status"`
	IsEdited       bool          `json:"is_edited"`
	IsDeleted      bool          `json:"is_deleted"`
	DeliveredAt    time.Time     `json:"delivered_at,omitempty"`
	ReadAt         time.Time     `json:"read_at,omitempty"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`
}

// MessagePreview represents a preview of a message
type MessagePreview struct {
	ID           string      `json:"id"`
	SenderID     string      `json:"sender_id"`
	SenderName   string      `json:"sender_name"`
	Type         MessageType `json:"type"`
	Content      string      `json:"content"`
	ThumbnailURL string      `json:"thumbnail_url,omitempty"`
	IsDeleted    bool        `json:"is_deleted"`
	CreatedAt    time.Time   `json:"created_at"`
}

// Reaction represents a reaction to a message
type Reaction struct {
	UserID    string    `json:"user_id"`
	Emoji     string    `json:"emoji"`
	CreatedAt time.Time `json:"created_at"`
}

// SendMessageRequest represents a request to send a message
type SendMessageRequest struct {
	ConversationID string      `json:"conversation_id" binding:"required"`
	Type           MessageType `json:"type" binding:"required"`
	Content        string      `json:"content"`
	MediaURL       string      `json:"media_url,omitempty"`
	ThumbnailURL   string      `json:"thumbnail_url,omitempty"`
	MediaSize      int64       `json:"media_size,omitempty"`
	MediaDuration  int         `json:"media_duration,omitempty"`
	MediaWidth     int         `json:"media_width,omitempty"`
	MediaHeight    int         `json:"media_height,omitempty"`
	Latitude       float64     `json:"latitude,omitempty"`
	Longitude      float64     `json:"longitude,omitempty"`
	ReplyToID      string      `json:"reply_to_id,omitempty"`
	ForwardedFrom  string      `json:"forwarded_from,omitempty"`
}

// EditMessageRequest represents a request to edit a message
type EditMessageRequest struct {
	Content string `json:"content" binding:"required"`
}

// DeleteMessageRequest represents a request to delete a message
type DeleteMessageRequest struct {
	MessageID string `json:"message_id" binding:"required"`
}

// AddReactionRequest represents a request to add a reaction to a message
type AddReactionRequest struct {
	MessageID string `json:"message_id" binding:"required"`
	Emoji     string `json:"emoji" binding:"required"`
}

// RemoveReactionRequest represents a request to remove a reaction from a message
type RemoveReactionRequest struct {
	MessageID string `json:"message_id" binding:"required"`
	Emoji     string `json:"emoji" binding:"required"`
}

// MarkAsReadRequest represents a request to mark messages as read
type MarkAsReadRequest struct {
	ConversationID string   `json:"conversation_id" binding:"required"`
	MessageIDs     []string `json:"message_ids" binding:"required"`
}
