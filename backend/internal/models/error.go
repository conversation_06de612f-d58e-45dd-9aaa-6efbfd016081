package models

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error ErrorDetails `json:"error"`
}

// ErrorDetails contains the details of an error
type ErrorDetails struct {
	Code            string        `json:"code"`
	Message         string        `json:"message"`
	Details         []ErrorDetail `json:"details,omitempty"`
	TraceID         string        `json:"trace_id"`
	DocumentationURL string       `json:"documentation_url,omitempty"`
}

// ErrorDetail contains specific details about an error
type ErrorDetail struct {
	Field   string `json:"field,omitempty"`
	Issue   string `json:"issue,omitempty"`
	Message string `json:"message"`
}
