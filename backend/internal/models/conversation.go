package models

import "time"

// ConversationType represents the type of conversation
type ConversationType string

const (
	OneToOne ConversationType = "one_to_one"
	Group    ConversationType = "group"
	Channel  ConversationType = "channel"
)

// Conversation represents a conversation between users
type Conversation struct {
	ID                string           `json:"id"`
	Type              ConversationType `json:"type"`
	Name              string           `json:"name,omitempty"`
	AvatarURL         string           `json:"avatar_url,omitempty"`
	ParticipantIDs    []string         `json:"participant_ids"`
	CreatorID         string           `json:"creator_id"`
	LastMessage       *Message         `json:"last_message,omitempty"`
	LastMessageTime   time.Time        `json:"last_message_time"`
	UnreadCount       int              `json:"unread_count"`
	IsEncrypted       bool             `json:"is_encrypted"`
	IsArchived        bool             `json:"is_archived"`
	IsMuted           bool             `json:"is_muted"`
	IsPinned          bool             `json:"is_pinned"`
	CreatedAt         time.Time        `json:"created_at"`
	UpdatedAt         time.Time        `json:"updated_at"`
}

// ConversationListItem represents a conversation in a list
type ConversationListItem struct {
	ID                string           `json:"id"`
	Type              ConversationType `json:"type"`
	Name              string           `json:"name,omitempty"`
	AvatarURL         string           `json:"avatar_url,omitempty"`
	ParticipantCount  int              `json:"participant_count"`
	LastMessage       *MessagePreview  `json:"last_message,omitempty"`
	LastMessageTime   time.Time        `json:"last_message_time"`
	UnreadCount       int              `json:"unread_count"`
	IsEncrypted       bool             `json:"is_encrypted"`
	IsArchived        bool             `json:"is_archived"`
	IsMuted           bool             `json:"is_muted"`
	IsPinned          bool             `json:"is_pinned"`
}

// CreateConversationRequest represents a request to create a new conversation
type CreateConversationRequest struct {
	Type           ConversationType `json:"type" binding:"required"`
	Name           string           `json:"name,omitempty"`
	AvatarURL      string           `json:"avatar_url,omitempty"`
	ParticipantIDs []string         `json:"participant_ids" binding:"required"`
	IsEncrypted    bool             `json:"is_encrypted"`
}

// UpdateConversationRequest represents a request to update a conversation
type UpdateConversationRequest struct {
	Name      string `json:"name,omitempty"`
	AvatarURL string `json:"avatar_url,omitempty"`
	IsMuted   *bool  `json:"is_muted,omitempty"`
	IsPinned  *bool  `json:"is_pinned,omitempty"`
}

// AddParticipantsRequest represents a request to add participants to a conversation
type AddParticipantsRequest struct {
	ParticipantIDs []string `json:"participant_ids" binding:"required"`
}

// RemoveParticipantRequest represents a request to remove a participant from a conversation
type RemoveParticipantRequest struct {
	ParticipantID string `json:"participant_id" binding:"required"`
}

// LeaveConversationRequest represents a request to leave a conversation
type LeaveConversationRequest struct {
	ConversationID string `json:"conversation_id" binding:"required"`
}

// ArchiveConversationRequest represents a request to archive a conversation
type ArchiveConversationRequest struct {
	ConversationID string `json:"conversation_id" binding:"required"`
}

// UnarchiveConversationRequest represents a request to unarchive a conversation
type UnarchiveConversationRequest struct {
	ConversationID string `json:"conversation_id" binding:"required"`
}
