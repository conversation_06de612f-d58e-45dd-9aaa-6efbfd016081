# Database Migrations

This directory contains database migration files for the Meena application. These migrations are used to evolve the database schema over time in a controlled and reversible manner.

## Migration File Naming Convention

Migration files follow this naming convention:

```
YYYYMMDDHHMMSS_descriptive_name.sql
```

For example:
```
20250506120000_create_followers_table.sql
```

## Migration File Structure

Each migration file should contain both "up" and "down" migrations, separated by a special comment:

```sql
-- Up migration
CREATE TABLE example (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);

-- Down migration
DROP TABLE example;
```

## Creating a New Migration

To create a new migration:

1. Follow the naming convention described above
2. Include both "up" and "down" migrations
3. Make the migration idempotent (can be run multiple times without error)
4. Test the migration locally before committing

## Applying Migrations

Migrations are applied using the golang-migrate tool or through the Railway CLI:

```bash
# Using golang-migrate
migrate -path backend/internal/database/migrations -database "postgres://user:password@localhost:5432/meena?sslmode=disable" up

# Using Railway CLI
railway run "psql \$DATABASE_URL -f backend/internal/database/migrations/YYYYMMDDHHMMSS_descriptive_name.sql"
```

## Best Practices

1. **Keep migrations small and focused**: Each migration should make a single, logical change to the schema
2. **Make migrations backward compatible**: Ensure that existing code can work with the new schema
3. **Test migrations thoroughly**: Test both "up" and "down" migrations
4. **Document migrations**: Include comments explaining the purpose of the migration
5. **Update the master schema**: Always update the master schema file (`docs/database/master_schema.sql`) when creating a new migration

## Troubleshooting

If a migration fails:

1. Check the error message to understand what went wrong
2. Fix the issue in a new migration file
3. Do not modify existing migration files that have been applied to any environment

## Related Documentation

For more information about database schema management, see:

- [Schema Management Guide](../../../docs/database/SCHEMA_MANAGEMENT.md)
- [Master Schema](../../../docs/database/master_schema.sql)
