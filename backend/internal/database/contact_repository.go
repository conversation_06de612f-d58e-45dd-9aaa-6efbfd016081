package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/meena/backend/internal/models"
)

// ContactRepository handles database operations for contacts
type ContactRepository struct {
	db *DB
}

// NewContactRepository creates a new contact repository
func NewContactRepository(db *DB) *ContactRepository {
	return &ContactRepository{db: db}
}

// GetContacts gets all contacts for a user
func (r *ContactRepository) GetContacts(ctx context.Context, userID string) ([]*models.Contact, error) {
	// Use the get_contacts database function with a default limit of 100 and offset of 0
	query := `SELECT * FROM get_contacts($1, 100, 0)`

	rows, err := r.db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("error getting contacts: %w", err)
	}
	defer rows.Close()

	contacts := []*models.Contact{}
	for rows.Next() {
		var contact models.Contact
		var user models.User
		var lastInteraction, contactCreatedAt, contactUpdatedAt, userLastSeen, userCreatedAt time.Time
		var lastInteractionNull bool

		err := rows.Scan(
			&contact.ID,
			&contact.UserID,
			&contact.ContactID,
			&contact.DisplayName,
			&contact.Relationship,
			&contact.Notes,
			&contactCreatedAt,
			&contactUpdatedAt,
			&contact.IsFavorite,
			&lastInteraction,
			&user.ID,
			&user.UserHandle,
			&user.DisplayName,
			&user.Bio,
			&user.AvatarURL,
			&user.IsVerified,
			&user.IsGoldMember,
			&userLastSeen,
			&userCreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning contact row: %w", err)
		}

		contact.CreatedAt = contactCreatedAt
		contact.UpdatedAt = contactUpdatedAt
		if !lastInteractionNull {
			contact.LastInteractionAt = &lastInteraction
		}

		user.LastActive = userLastSeen
		user.CreatedAt = userCreatedAt

		// Convert User to UserProfile
		userProfile := &models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		}

		contact.User = userProfile

		contacts = append(contacts, &contact)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contact rows: %w", err)
	}

	return contacts, nil
}

// GetContact gets a contact by ID
func (r *ContactRepository) GetContact(ctx context.Context, userID, contactID string) (*models.Contact, error) {
	// Use the get_contact_by_id database function
	query := `SELECT * FROM get_contact_by_id($1, $2)`

	var contact models.Contact
	var user models.User
	var lastInteraction, contactCreatedAt, contactUpdatedAt, userLastSeen, userCreatedAt time.Time
	var lastInteractionNull bool

	err := r.db.QueryRowContext(ctx, query, userID, contactID).Scan(
		&contact.ID,
		&contact.UserID,
		&contact.ContactID,
		&contact.DisplayName,
		&contact.Relationship,
		&contact.Notes,
		&contactCreatedAt,
		&contactUpdatedAt,
		&contact.IsFavorite,
		&lastInteraction,
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.IsVerified,
		&user.IsGoldMember,
		&userLastSeen,
		&userCreatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("contact not found")
		}
		return nil, fmt.Errorf("error getting contact: %w", err)
	}

	contact.CreatedAt = contactCreatedAt
	contact.UpdatedAt = contactUpdatedAt
	if !lastInteractionNull {
		contact.LastInteractionAt = &lastInteraction
	}

	user.LastActive = userLastSeen
	user.CreatedAt = userCreatedAt

	// Convert User to UserProfile
	userProfile := &models.UserProfile{
		ID:             user.ID,
		UserHandle:     user.UserHandle,
		DisplayName:    user.DisplayName,
		Bio:            user.Bio,
		AvatarURL:      user.AvatarURL,
		IsVerified:     user.IsVerified,
		IsGoldMember:   user.IsGoldMember,
		LastActive:     user.LastActive,
		CreatedAt:      user.CreatedAt,
		FollowerCount:  user.FollowerCount,
		FollowingCount: user.FollowingCount,
	}

	contact.User = userProfile

	return &contact, nil
}

// AddContact adds a new contact
func (r *ContactRepository) AddContact(ctx context.Context, userID string, req models.AddContactRequest) (*models.Contact, error) {
	// Check if the contact exists
	var contactUserID string
	err := r.db.QueryRowContext(ctx, "SELECT user_id FROM users WHERE user_handle = $1", req.UserHandle).Scan(&contactUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("error checking if user exists: %w", err)
	}

	// Check if the contact already exists
	var exists bool
	err = r.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM contacts WHERE user_id = $1 AND contact_id = $2)", userID, contactUserID).Scan(&exists)
	if err != nil {
		return nil, fmt.Errorf("error checking if contact exists: %w", err)
	}
	if exists {
		return nil, errors.New("contact already exists")
	}

	// Insert the contact
	contactID := uuid.New().String()
	now := time.Now()

	// Use the provided display name or fall back to the user handle
	displayName := req.DisplayName
	if displayName == "" {
		err = r.db.QueryRowContext(ctx, "SELECT display_name FROM users WHERE user_id = $1", contactUserID).Scan(&displayName)
		if err != nil {
			return nil, fmt.Errorf("error getting user display name: %w", err)
		}
	}

	query := `
		INSERT INTO contacts (
			id, user_id, contact_id, display_name, relationship, notes,
			created_at, updated_at, is_favorite, is_blocked
		) VALUES (
			$1, $2, $3, $4, $5, $6,
			$7, $8, $9, $10
		)
		RETURNING id
	`

	_, err = r.db.ExecContext(
		ctx,
		query,
		contactID,
		userID,
		contactUserID,
		displayName,
		"friend", // Default relationship
		req.Notes,
		now,
		now,
		false, // Not favorite by default
		false, // Not blocked by default
	)
	if err != nil {
		return nil, fmt.Errorf("error adding contact: %w", err)
	}

	// Get the newly created contact
	return r.GetContact(ctx, userID, contactID)
}

// UpdateContact updates a contact
func (r *ContactRepository) UpdateContact(ctx context.Context, userID, contactID string, req models.UpdateContactRequest) (*models.Contact, error) {
	query := `
		UPDATE contacts
		SET
			display_name = COALESCE($3, display_name),
			relationship = COALESCE($4, relationship),
			notes = COALESCE($5, notes),
			is_favorite = COALESCE($6, is_favorite),
			updated_at = NOW()
		WHERE user_id = $1 AND id = $2
		RETURNING id
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		userID,
		contactID,
		req.DisplayName,
		req.Relationship,
		req.Notes,
		req.IsFavorite,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("contact not found")
		}
		return nil, fmt.Errorf("error updating contact: %w", err)
	}

	// Get the updated contact
	return r.GetContact(ctx, userID, contactID)
}

// RemoveContact removes a contact
func (r *ContactRepository) RemoveContact(ctx context.Context, userID, contactID string) error {
	query := `DELETE FROM contacts WHERE user_id = $1 AND id = $2`
	_, err := r.db.ExecContext(ctx, query, userID, contactID)
	if err != nil {
		return fmt.Errorf("error removing contact: %w", err)
	}
	return nil
}

// BlockContact blocks a contact
func (r *ContactRepository) BlockContact(ctx context.Context, userID, contactID string) error {
	query := `
		UPDATE contacts
		SET is_blocked = true, updated_at = NOW()
		WHERE user_id = $1 AND id = $2
	`
	_, err := r.db.ExecContext(ctx, query, userID, contactID)
	if err != nil {
		return fmt.Errorf("error blocking contact: %w", err)
	}
	return nil
}

// UnblockContact unblocks a contact
func (r *ContactRepository) UnblockContact(ctx context.Context, userID, contactID string) error {
	query := `
		UPDATE contacts
		SET is_blocked = false, updated_at = NOW()
		WHERE user_id = $1 AND id = $2
	`
	_, err := r.db.ExecContext(ctx, query, userID, contactID)
	if err != nil {
		return fmt.Errorf("error unblocking contact: %w", err)
	}
	return nil
}

// UpdateLastInteraction updates the last interaction timestamp for a contact
func (r *ContactRepository) UpdateLastInteraction(ctx context.Context, userID, contactID string) error {
	query := `
		UPDATE contacts
		SET last_interaction_at = NOW(), updated_at = NOW()
		WHERE user_id = $1 AND contact_id = $2
	`
	_, err := r.db.ExecContext(ctx, query, userID, contactID)
	if err != nil {
		return fmt.Errorf("error updating last interaction: %w", err)
	}
	return nil
}

// GetFavoriteContacts gets all favorite contacts for a user
func (r *ContactRepository) GetFavoriteContacts(ctx context.Context, userID string) ([]*models.Contact, error) {
	// Use the get_favorite_contacts database function with a default limit of 100 and offset of 0
	query := `SELECT * FROM get_favorite_contacts($1, 100, 0)`

	rows, err := r.db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("error getting favorite contacts: %w", err)
	}
	defer rows.Close()

	contacts := []*models.Contact{}
	for rows.Next() {
		var contact models.Contact
		var user models.User
		var lastInteraction, contactCreatedAt, contactUpdatedAt, userLastSeen, userCreatedAt time.Time
		var lastInteractionNull bool

		err := rows.Scan(
			&contact.ID,
			&contact.UserID,
			&contact.ContactID,
			&contact.DisplayName,
			&contact.Relationship,
			&contact.Notes,
			&contactCreatedAt,
			&contactUpdatedAt,
			&contact.IsFavorite,
			&lastInteraction,
			&user.ID,
			&user.UserHandle,
			&user.DisplayName,
			&user.Bio,
			&user.AvatarURL,
			&user.IsVerified,
			&user.IsGoldMember,
			&userLastSeen,
			&userCreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning favorite contact row: %w", err)
		}

		contact.CreatedAt = contactCreatedAt
		contact.UpdatedAt = contactUpdatedAt
		if !lastInteractionNull {
			contact.LastInteractionAt = &lastInteraction
		}

		user.LastActive = userLastSeen
		user.CreatedAt = userCreatedAt

		// Convert User to UserProfile
		userProfile := &models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		}

		contact.User = userProfile

		contacts = append(contacts, &contact)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating favorite contact rows: %w", err)
	}

	return contacts, nil
}
