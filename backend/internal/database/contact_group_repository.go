package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/meena/backend/internal/models"
)

// ContactGroupRepository handles database operations for contact groups
type ContactGroupRepository struct {
	db *DB
}

// NewContactGroupRepository creates a new contact group repository
func NewContactGroupRepository(db *DB) *ContactGroupRepository {
	return &ContactGroupRepository{db: db}
}

// GetContactGroups gets all contact groups for a user
func (r *ContactGroupRepository) GetContactGroups(ctx context.Context, userID string) ([]*models.ContactGroup, error) {
	query := `
		SELECT
			id, user_id, name, description, created_at, updated_at,
			(SELECT COUNT(*) FROM contact_group_members WHERE group_id = contact_groups.id) as member_count
		FROM contact_groups
		WHERE user_id = $1
		ORDER BY name
	`

	rows, err := r.db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("error getting contact groups: %w", err)
	}
	defer rows.Close()

	groups := []*models.ContactGroup{}
	for rows.Next() {
		var group models.ContactGroup
		var createdAt, updatedAt time.Time
		err := rows.Scan(
			&group.ID,
			&group.UserID,
			&group.Name,
			&group.Description,
			&createdAt,
			&updatedAt,
			&group.MemberCount,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning contact group row: %w", err)
		}

		group.CreatedAt = createdAt
		group.UpdatedAt = updatedAt

		groups = append(groups, &group)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contact group rows: %w", err)
	}

	return groups, nil
}

// GetContactGroup gets a contact group by ID
func (r *ContactGroupRepository) GetContactGroup(ctx context.Context, userID, groupID string) (*models.ContactGroup, error) {
	query := `
		SELECT
			id, user_id, name, description, created_at, updated_at,
			(SELECT COUNT(*) FROM contact_group_members WHERE group_id = contact_groups.id) as member_count
		FROM contact_groups
		WHERE user_id = $1 AND id = $2
	`

	var group models.ContactGroup
	var createdAt, updatedAt time.Time
	err := r.db.QueryRowContext(ctx, query, userID, groupID).Scan(
		&group.ID,
		&group.UserID,
		&group.Name,
		&group.Description,
		&createdAt,
		&updatedAt,
		&group.MemberCount,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("contact group not found")
		}
		return nil, fmt.Errorf("error getting contact group: %w", err)
	}

	group.CreatedAt = createdAt
	group.UpdatedAt = updatedAt

	// Get group members
	membersQuery := `
		SELECT
			c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
			c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
			u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
			u.is_verified, u.subscription_tier = 'gold' as is_gold_member,
			u.last_seen_at, u.created_at
		FROM contact_group_members cgm
		JOIN contacts c ON cgm.contact_id = c.id
		JOIN users u ON c.contact_id = u.user_id
		WHERE cgm.group_id = $1
		ORDER BY c.display_name
	`

	rows, err := r.db.QueryContext(ctx, membersQuery, groupID)
	if err != nil {
		return nil, fmt.Errorf("error getting contact group members: %w", err)
	}
	defer rows.Close()

	members := []*models.Contact{}
	for rows.Next() {
		var contact models.Contact
		var user models.User
		var lastInteraction, contactCreatedAt, contactUpdatedAt, userLastSeen, userCreatedAt time.Time
		var lastInteractionNull bool

		err := rows.Scan(
			&contact.ID,
			&contact.UserID,
			&contact.ContactID,
			&contact.DisplayName,
			&contact.Relationship,
			&contact.Notes,
			&contactCreatedAt,
			&contactUpdatedAt,
			&contact.IsFavorite,
			&lastInteraction,
			&user.ID,
			&user.UserHandle,
			&user.DisplayName,
			&user.Bio,
			&user.AvatarURL,
			&user.IsVerified,
			&user.IsGoldMember,
			&userLastSeen,
			&userCreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning contact group member row: %w", err)
		}

		contact.CreatedAt = contactCreatedAt
		contact.UpdatedAt = contactUpdatedAt
		if !lastInteractionNull {
			contact.LastInteractionAt = &lastInteraction
		}

		user.LastActive = userLastSeen
		user.CreatedAt = userCreatedAt

		// Convert User to UserProfile
		userProfile := &models.UserProfile{
			ID:             user.ID,
			UserHandle:     user.UserHandle,
			DisplayName:    user.DisplayName,
			Bio:            user.Bio,
			AvatarURL:      user.AvatarURL,
			IsVerified:     user.IsVerified,
			IsGoldMember:   user.IsGoldMember,
			LastActive:     user.LastActive,
			CreatedAt:      user.CreatedAt,
			FollowerCount:  user.FollowerCount,
			FollowingCount: user.FollowingCount,
		}

		contact.User = userProfile

		members = append(members, &contact)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contact group member rows: %w", err)
	}

	group.Members = members

	return &group, nil
}

// CreateContactGroup creates a new contact group
func (r *ContactGroupRepository) CreateContactGroup(ctx context.Context, userID string, req models.CreateContactGroupRequest) (*models.ContactGroup, error) {
	groupID := uuid.New().String()
	now := time.Now()

	query := `
		INSERT INTO contact_groups (id, user_id, name, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		groupID,
		userID,
		req.Name,
		req.Description,
		now,
		now,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating contact group: %w", err)
	}

	// Get the newly created group
	return r.GetContactGroup(ctx, userID, groupID)
}

// UpdateContactGroup updates a contact group
func (r *ContactGroupRepository) UpdateContactGroup(ctx context.Context, userID, groupID string, req models.UpdateContactGroupRequest) (*models.ContactGroup, error) {
	query := `
		UPDATE contact_groups
		SET
			name = COALESCE($3, name),
			description = COALESCE($4, description),
			updated_at = NOW()
		WHERE user_id = $1 AND id = $2
		RETURNING id
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		userID,
		groupID,
		req.Name,
		req.Description,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("contact group not found")
		}
		return nil, fmt.Errorf("error updating contact group: %w", err)
	}

	// Get the updated group
	return r.GetContactGroup(ctx, userID, groupID)
}

// DeleteContactGroup deletes a contact group
func (r *ContactGroupRepository) DeleteContactGroup(ctx context.Context, userID, groupID string) error {
	query := `DELETE FROM contact_groups WHERE user_id = $1 AND id = $2`
	_, err := r.db.ExecContext(ctx, query, userID, groupID)
	if err != nil {
		return fmt.Errorf("error deleting contact group: %w", err)
	}
	return nil
}

// AddContactToGroup adds a contact to a group
func (r *ContactGroupRepository) AddContactToGroup(ctx context.Context, userID, groupID, contactID string) error {
	// Check if the group exists and belongs to the user
	var exists bool
	err := r.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM contact_groups WHERE id = $1 AND user_id = $2)", groupID, userID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking if group exists: %w", err)
	}
	if !exists {
		return errors.New("contact group not found")
	}

	// Check if the contact exists and belongs to the user
	err = r.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1 AND user_id = $2)", contactID, userID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking if contact exists: %w", err)
	}
	if !exists {
		return errors.New("contact not found")
	}

	// Check if the contact is already in the group
	err = r.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM contact_group_members WHERE group_id = $1 AND contact_id = $2)", groupID, contactID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking if contact is already in group: %w", err)
	}
	if exists {
		return errors.New("contact is already in group")
	}

	// Add the contact to the group
	query := `INSERT INTO contact_group_members (group_id, contact_id) VALUES ($1, $2)`
	_, err = r.db.ExecContext(ctx, query, groupID, contactID)
	if err != nil {
		return fmt.Errorf("error adding contact to group: %w", err)
	}

	return nil
}

// RemoveContactFromGroup removes a contact from a group
func (r *ContactGroupRepository) RemoveContactFromGroup(ctx context.Context, userID, groupID, contactID string) error {
	// Check if the group exists and belongs to the user
	var exists bool
	err := r.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM contact_groups WHERE id = $1 AND user_id = $2)", groupID, userID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking if group exists: %w", err)
	}
	if !exists {
		return errors.New("contact group not found")
	}

	// Remove the contact from the group
	query := `DELETE FROM contact_group_members WHERE group_id = $1 AND contact_id = $2`
	_, err = r.db.ExecContext(ctx, query, groupID, contactID)
	if err != nil {
		return fmt.Errorf("error removing contact from group: %w", err)
	}

	return nil
}
