package database

import (
	"context"
	"time"

	"github.com/meena/backend/internal/models"
)

// RefreshTokenRepository handles database operations for refresh tokens
type RefreshTokenRepository struct {
	db *DB
}

// NewRefreshTokenRepository creates a new RefreshTokenRepository
func NewRefreshTokenRepository(db *DB) *RefreshTokenRepository {
	return &RefreshTokenRepository{db: db}
}

// StoreToken stores a new refresh token in the database
func (r *RefreshTokenRepository) StoreToken(ctx context.Context, userID string, tokenHash string, expiresAt time.Time) error {
	query := `
		INSERT INTO refresh_tokens (user_id, token_hash, expires_at, issued_at, is_revoked)
		VALUES ($1, $2, $3, CURRENT_TIMESTAMP, FALSE)
	`
	_, err := r.db.ExecContext(ctx, query, userID, tokenHash, expiresAt)
	return err
}

// GetToken retrieves a refresh token from the database
func (r *RefreshTokenRepository) GetToken(ctx context.Context, tokenHash string) (*models.RefreshToken, error) {
	query := `
		SELECT token_id, user_id, token_hash, issued_at, expires_at, is_revoked, created_at, used_at, revoked_at
		FROM refresh_tokens
		WHERE token_hash = $1
	`

	token := &models.RefreshToken{}
	err := r.db.QueryRowContext(ctx, query, tokenHash).Scan(
		&token.TokenID,
		&token.UserID,
		&token.TokenHash,
		&token.IssuedAt,
		&token.ExpiresAt,
		&token.IsRevoked,
		&token.CreatedAt,
		&token.UsedAt,
		&token.RevokedAt,
	)

	if err != nil {
		return nil, err
	}
	return token, nil
}

// MarkTokenAsUsed marks a refresh token as used
func (r *RefreshTokenRepository) MarkTokenAsUsed(ctx context.Context, tokenHash string) error {
	query := `
		UPDATE refresh_tokens
		SET used_at = CURRENT_TIMESTAMP
		WHERE token_hash = $1
	`
	_, err := r.db.ExecContext(ctx, query, tokenHash)
	return err
}

// RevokeToken revokes a refresh token
func (r *RefreshTokenRepository) RevokeToken(ctx context.Context, tokenHash string) error {
	query := `
		UPDATE refresh_tokens
		SET is_revoked = TRUE, revoked_at = CURRENT_TIMESTAMP
		WHERE token_hash = $1
	`
	_, err := r.db.ExecContext(ctx, query, tokenHash)
	return err
}

// RevokeAllUserTokens revokes all refresh tokens for a user
func (r *RefreshTokenRepository) RevokeAllUserTokens(ctx context.Context, userID string) error {
	query := `
		UPDATE refresh_tokens
		SET is_revoked = TRUE, revoked_at = CURRENT_TIMESTAMP
		WHERE user_id = $1 AND is_revoked = FALSE
	`
	_, err := r.db.ExecContext(ctx, query, userID)
	return err
}

// CleanupExpiredTokens removes expired and used tokens
func (r *RefreshTokenRepository) CleanupExpiredTokens(ctx context.Context) error {
	query := `
		DELETE FROM refresh_tokens
		WHERE expires_at < CURRENT_TIMESTAMP
		OR (used_at IS NOT NULL AND used_at < CURRENT_TIMESTAMP - INTERVAL '24 hours')
		OR (revoked_at IS NOT NULL AND revoked_at < CURRENT_TIMESTAMP - INTERVAL '24 hours')
	`
	_, err := r.db.ExecContext(ctx, query)
	return err
}
