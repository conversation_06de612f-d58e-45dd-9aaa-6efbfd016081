package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/utils"
	"golang.org/x/crypto/bcrypt"
)

// ErrUserNotFound is returned when a user is not found in the database.
var ErrUserNotFound = errors.New("user not found")

// UserRepository handles database operations for users
type UserRepository struct {
	db *DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *DB) *UserRepository {
	return &UserRepository{db: db}
}

// <PERSON><PERSON><PERSON><PERSON> creates a new user in the database
func (r *UserRepository) CreateUser(ctx context.Context, req models.RegisterRequest, recoveryPhrase string) (*models.User, error) {
	// Generate a user handle if not provided (anonymous sign-in)
	userHandle := req.UserHandle
	if userHandle == "" {
		var err error
		userHandle, err = utils.GenerateMeenaID()
		if err != nil {
			return nil, fmt.Errorf("error generating Meena ID: %w", err)
		}
	}

	// Check if user handle already exists
	exists, err := r.UserHandleExists(ctx, userHandle)
	if err != nil {
		return nil, fmt.Errorf("error checking if user handle exists: %w", err)
	}
	if exists {
		return nil, errors.New("user handle already exists")
	}

	// Hash password
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("error hashing password: %w", err)
	}

	// Hash recovery phrase
	recoveryPhraseHash, err := bcrypt.GenerateFromPassword([]byte(recoveryPhrase), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("error hashing recovery phrase: %w", err)
	}

	// Use the provided display name or fall back to the user handle
	displayName := req.DisplayName
	if displayName == "" {
		displayName = userHandle
	}

	// Generate user ID
	userID := uuid.New().String()

	// Insert user into database
	query := `
		INSERT INTO users (
			user_id, user_handle, email, phone_number, password_hash,
			recovery_phrase_hash, recovery_pin, display_name, bio,
			profile_picture_url, verification_status, is_active, created_at, last_seen_at
		) VALUES (
			$1, $2, $3, $4, $5,
			$6, $7, $8, $9,
			$10, $11, $12, $13, $14
		) RETURNING user_id, user_handle, display_name, bio, profile_picture_url,
		verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member, last_seen_at, created_at
	`

	now := time.Now()
	// Use NULL for empty email and phone values to avoid unique constraint violations
	var email, phoneNumber interface{}
	if req.Email != "" {
		email = req.Email
	} else {
		email = nil // Use NULL for empty email
	}

	if req.PhoneNumber != "" {
		phoneNumber = req.PhoneNumber
	} else {
		phoneNumber = nil // Use NULL for empty phone number
	}

	// Hash the recovery PIN
	recoveryPinHash, err := bcrypt.GenerateFromPassword([]byte(req.RecoveryPin), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("error hashing recovery pin: %w", err)
	}

	row := r.db.QueryRowContext(
		ctx,
		query,
		userID,
		userHandle,
		email,
		phoneNumber,
		passwordHash,
		recoveryPhraseHash,
		recoveryPinHash, // Store the hashed PIN
		displayName,
		"",                                // bio
		"https://via.placeholder.com/150", // profile_picture_url
		"none",                            // verification_status
		true,                              // is_active
		now,                               // created_at
		now,                               // last_seen_at
	)

	var user models.User
	var lastSeen, createdAt time.Time
	err = row.Scan(
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating user: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = createdAt
	user.FollowerCount = 0
	user.FollowingCount = 0

	return &user, nil
}

// GetUserByID gets a user by ID
func (r *UserRepository) GetUserByID(ctx context.Context, userID string) (*models.User, error) {
	query := `
		SELECT
			user_id, user_handle, display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, created_at as updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count
		FROM users
		WHERE user_id = $1 AND is_active = true
	`

	row := r.db.QueryRowContext(ctx, query, userID)

	var user models.User
	var lastSeen, createdAt, updatedAt time.Time
	err := row.Scan(
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
		&updatedAt,
		&user.FollowerCount,
		&user.FollowingCount,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("error getting user: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = updatedAt

	return &user, nil
}

// UpdateProfile updates a user's profile
func (r *UserRepository) UpdateProfile(ctx context.Context, userID string, profile models.UpdateProfileRequest) (*models.User, error) {
	query := `
		UPDATE users
		SET
			display_name = COALESCE($2, display_name),
			bio = COALESCE($3, bio),
			profile_picture_url = COALESCE($4, profile_picture_url)
		WHERE user_id = $1
		RETURNING
			user_id, user_handle, display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, NOW() as updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count
	`

	row := r.db.QueryRowContext(
		ctx,
		query,
		userID,
		profile.DisplayName,
		profile.Bio,
		profile.AvatarURL,
	)

	var user models.User
	var lastSeen, createdAt, updatedAt time.Time
	err := row.Scan(
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
		&updatedAt,
		&user.FollowerCount,
		&user.FollowingCount,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("error updating user profile: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = updatedAt

	return &user, nil
}

// SearchUsers searches for users by handle or display name
func (r *UserRepository) SearchUsers(ctx context.Context, query string, limit, offset int) ([]*models.User, error) {
	sqlQuery := `
		SELECT
			user_id, user_handle, display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, created_at as updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count
		FROM users
		WHERE (user_handle ILIKE $1 OR display_name ILIKE $1) AND is_active = true
		ORDER BY
			CASE WHEN user_handle ILIKE $2 THEN 0 ELSE 1 END,
			CASE WHEN display_name ILIKE $2 THEN 0 ELSE 1 END,
			last_seen_at DESC
		LIMIT $3 OFFSET $4
	`

	rows, err := r.db.QueryContext(
		ctx,
		sqlQuery,
		"%"+query+"%", // For ILIKE partial matching
		query+"%",     // For prefix matching (higher priority)
		limit,
		offset,
	)
	if err != nil {
		return nil, fmt.Errorf("error searching users: %w", err)
	}
	defer rows.Close()

	users := []*models.User{}
	for rows.Next() {
		var user models.User
		var lastSeen, createdAt, updatedAt time.Time
		err := rows.Scan(
			&user.ID,
			&user.UserHandle,
			&user.DisplayName,
			&user.Bio,
			&user.AvatarURL,
			&user.VerificationStatus,
			&user.IsVerified,
			&user.IsGoldMember,
			&lastSeen,
			&createdAt,
			&updatedAt,
			&user.FollowerCount,
			&user.FollowingCount,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning user row: %w", err)
		}

		user.LastActive = lastSeen
		user.CreatedAt = createdAt
		user.UpdatedAt = updatedAt

		users = append(users, &user)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating user rows: %w", err)
	}

	return users, nil
}

// GetUserByHandle gets a user by handle
func (r *UserRepository) GetUserByHandle(ctx context.Context, userHandle string) (*models.User, error) {
	query := `
		SELECT
			user_id, user_handle, display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, created_at as updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count
		FROM users
		WHERE user_handle = $1 AND is_active = true
	`

	row := r.db.QueryRowContext(ctx, query, userHandle)

	var user models.User
	var lastSeen, createdAt, updatedAt time.Time
	err := row.Scan(
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
		&updatedAt,
		&user.FollowerCount,
		&user.FollowingCount,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("error getting user: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = updatedAt

	return &user, nil
}

// GetUserByIdentifier gets a user by identifier (handle, email, or phone)
func (r *UserRepository) GetUserByIdentifier(ctx context.Context, identifier string) (*models.User, error) {
	query := `
		SELECT
			user_id, user_handle, display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified, subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, created_at as updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count,
			password_hash
		FROM users
		WHERE (user_handle = $1 OR email = $1 OR phone_number = $1) AND is_active = true
	`

	row := r.db.QueryRowContext(ctx, query, identifier)

	var user models.User
	var lastSeen, createdAt, updatedAt time.Time
	var passwordHash string
	err := row.Scan(
		&user.ID,
		&user.UserHandle,
		&user.DisplayName,
		&user.Bio,
		&user.AvatarURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
		&updatedAt,
		&user.FollowerCount,
		&user.FollowingCount,
		&passwordHash,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("error getting user: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = updatedAt
	user.PasswordHash = passwordHash

	return &user, nil
}

// UserHandleExists checks if a user handle already exists
func (r *UserRepository) UserHandleExists(ctx context.Context, userHandle string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE user_handle = $1)`
	var exists bool
	err := r.db.QueryRowContext(ctx, query, userHandle).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("error checking if user handle exists: %w", err)
	}
	return exists, nil
}

// UpdateLastSeen updates the last seen timestamp for a user
func (r *UserRepository) UpdateLastSeen(ctx context.Context, userID string) error {
	query := `UPDATE users SET last_seen_at = $1 WHERE user_id = $2`
	commandTag, err := r.db.ExecContext(ctx, query, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("error updating last seen: %w", err)
	}
	if commandTag.RowsAffected() == 0 {
		// This could mean the user_id didn't exist, though UpdatePassword handles this more strictly.
		// For last_seen, it might be acceptable for it to silently fail if user_id is gone.
		// Depending on requirements, you might return ErrUserNotFound here too.
	}
	return nil
}

// VerifyPassword verifies a user's password
func (r *UserRepository) VerifyPassword(ctx context.Context, userID, password string) (bool, error) {
	query := `SELECT password_hash FROM users WHERE user_id = $1`
	var passwordHash string
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&passwordHash)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, errors.New("user not found")
		}
		return false, fmt.Errorf("error getting password hash: %w", err)
	}

	err = bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(password))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return false, nil
		}
		return false, fmt.Errorf("error comparing password hash: %w", err)
	}

	return true, nil
}

// ChangePassword changes a user's password
func (r *UserRepository) ChangePassword(ctx context.Context, userID, newPassword string) error {
	// Hash new password
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("error hashing password: %w", err)
	}

	query := `UPDATE users SET password_hash = $1 WHERE user_id = $2`
	commandTag, err := r.db.ExecContext(ctx, query, passwordHash, userID)
	if err != nil {
		return fmt.Errorf("error changing password: %w", err)
	}
	if commandTag.RowsAffected() == 0 {
		return ErrUserNotFound
	}

	return nil
}

// VerifyRecoveryInfo verifies a user's recovery information
func (r *UserRepository) VerifyRecoveryInfo(ctx context.Context, userID string, providedRecoveryPhrase string, providedRecoveryPin string) (bool, error) {
	query := `SELECT recovery_phrase_hash, recovery_pin_hash FROM users WHERE user_id = $1 AND is_active = true`
	var recoveryPhraseHash, recoveryPinHash string
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&recoveryPhraseHash, &recoveryPinHash)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, ErrUserNotFound
		}
		return false, fmt.Errorf("error getting recovery info for user %s: %w", userID, err)
	}

	// Verify recovery phrase
	err = bcrypt.CompareHashAndPassword([]byte(recoveryPhraseHash), []byte(providedRecoveryPhrase))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return false, nil // Phrase mismatch
		}
		// For other bcrypt errors, we might want to return the error
		return false, fmt.Errorf("error comparing recovery phrase for user %s: %w", userID, err)
	}

	// Verify recovery PIN
	err = bcrypt.CompareHashAndPassword([]byte(recoveryPinHash), []byte(providedRecoveryPin))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return false, nil // PIN mismatch
		}
		// For other bcrypt errors, we might want to return the error
		return false, fmt.Errorf("error comparing recovery pin for user %s: %w", userID, err)
	}

	return true, nil
}

// StoreRecoveryToken stores a recovery token for a user (now expects hashed token)
func (r *UserRepository) StoreRecoveryToken(ctx context.Context, userID, tokenHash string, expiresAt time.Time) error {
	// The token parameter is already a hash
	query := `
		INSERT INTO recovery_tokens (user_id, token_hash, expires_at, created_at, updated_at)
		VALUES ($1, $2, $3, NOW(), NOW())
	`
	_, err := r.db.ExecContext(ctx, query, userID, tokenHash, expiresAt) // Result not strictly needed for INSERT if not checking RowsAffected
	if err != nil {
		return fmt.Errorf("error storing recovery token: %w", err)
	}
	return nil
}

// VerifyRecoveryToken verifies a recovery token (now expects hashed token)
func (r *UserRepository) VerifyRecoveryToken(ctx context.Context, tokenHash string) (string, error) {
	// The token parameter is already a hash
	query := `
		SELECT user_id FROM recovery_tokens
		WHERE token_hash = $1 AND expires_at > $2 AND used = false
	`
	var userID string
	err := r.db.QueryRowContext(ctx, query, tokenHash, time.Now()).Scan(&userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", errors.New("invalid or expired token")
		}
		return "", fmt.Errorf("error verifying recovery token: %w", err)
	}
	return userID, nil
}

// MarkRecoveryTokenAsUsed marks a recovery token as used (now expects hashed token)
func (r *UserRepository) MarkRecoveryTokenAsUsed(ctx context.Context, tokenHash string) error {
	// The token parameter is already a hash
	query := `UPDATE recovery_tokens SET used = true, updated_at = NOW() WHERE token_hash = $1`
	commandTag, err := r.db.ExecContext(ctx, query, tokenHash)
	if err != nil {
		return fmt.Errorf("error marking recovery token as used: %w", err)
	}
	if commandTag.RowsAffected() == 0 {
		// This implies the token didn't exist or was already marked (though `used=true` is idempotent)
		// Depending on strictness, could return an error like "token not found to mark as used"
		// For now, not returning an error if 0 rows affected, as the state (used=true) might already be met.
	}
	return nil
}

// FindUserByEmail finds a user by their email address
func (r *UserRepository) FindUserByEmail(ctx context.Context, email string) (*models.User, error) {
	query := `
		SELECT
			user_id, user_handle, email, phone_number, password_hash,
			display_name, bio, profile_picture_url,
			verification_status, verification_status != 'none' as is_verified,
			subscription_tier = 'gold' as is_gold_member,
			last_seen_at, created_at, updated_at,
			(SELECT COUNT(*) FROM followers WHERE followed_id = users.user_id) as follower_count,
			(SELECT COUNT(*) FROM followers WHERE follower_id = users.user_id) as following_count
		FROM users
		WHERE email = $1 AND is_active = true
	`
	var user models.User
	var lastSeen, createdAt, updatedAt time.Time
	// Need to handle nullable fields from the database properly
	var phoneNumber, bio, profilePictureURL *string

	err := r.db.QueryRowContext(ctx, query, email).Scan(
		&user.ID,
		&user.UserHandle,
		&user.Email, // Assuming Email is not nullable in User model if it's a primary lookup
		&phoneNumber,
		&user.PasswordHash,
		&user.DisplayName,
		&bio,
		&profilePictureURL,
		&user.VerificationStatus,
		&user.IsVerified,
		&user.IsGoldMember,
		&lastSeen,
		&createdAt,
		&updatedAt,
		&user.FollowerCount,
		&user.FollowingCount,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrUserNotFound // Use a specific error
		}
		return nil, fmt.Errorf("error finding user by email: %w", err)
	}

	user.LastActive = lastSeen
	user.CreatedAt = createdAt
	user.UpdatedAt = updatedAt
	if phoneNumber != nil {
		user.PhoneNumber = *phoneNumber
	}
	if bio != nil {
		user.Bio = *bio
	}
	if profilePictureURL != nil {
		user.AvatarURL = *profilePictureURL
	}

	return &user, nil
}

// UpdatePassword updates a user's password hash
func (r *UserRepository) UpdatePassword(ctx context.Context, userID string, newPasswordHash string) error {
	query := `UPDATE users SET password_hash = $1, updated_at = NOW() WHERE user_id = $2`
	commandTag, err := r.db.ExecContext(ctx, query, newPasswordHash, userID)
	if err != nil {
		return fmt.Errorf("error updating password: %w", err)
	}
	if commandTag.RowsAffected() == 0 {
		return ErrUserNotFound // Or a more specific "failed to update" error
	}
	return nil
}
