-- Create story_elements table
CREATE TABLE story_elements (
    element_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES stories(story_id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(media_id) ON DELETE CASCADE,
    caption TEXT,
    position INTEGER NOT NULL, -- Order in the story
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
