-- Create call_logs table
CREATE TABLE call_logs (
    call_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    caller_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    callee_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- In seconds
    call_type VARCHAR(20) NOT NULL, -- 'audio', 'video'
    status VARCHAR(20) NOT NULL, -- 'answered', 'missed', 'declined', 'failed'
    quality_score INTEGER, -- 1-5
    notes TEXT
);

-- Create indexes for faster lookups
CREATE INDEX idx_call_logs_user_id ON call_logs(user_id);
