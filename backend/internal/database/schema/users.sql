-- Create users table
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_handle VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    recovery_phrase_hash VARCHAR(255) NOT NULL,
    recovery_pin_hash VARCHAR(255), -- Changed from recovery_pin and increased length for hash
    display_name VARCHAR(100),
    profile_picture_url VARCHAR(255),
    bio TEXT,
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    verification_status verification_status_enum NOT NULL DEFAULT 'none',
    requires_2fa BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE,
    scheduled_deletion_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for faster lookups
CREATE INDEX idx_users_user_handle ON users(user_handle);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_is_active ON users(is_active);
