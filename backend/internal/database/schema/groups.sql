-- Create groups table
CREATE TABLE groups (
    group_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    profile_picture_url VARCHAR(255),
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    privacy_type privacy_type_enum NOT NULL DEFAULT 'private',
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create indexes for faster lookups
