-- Create chats table
CREATE TABLE chats (
    chat_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL, -- 'one_to_one', 'group'
    group_id UUID REFERENCES groups(group_id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_message_id UUID,
    last_message_time TIMESTAMP WITH TIME ZONE
);

-- Create indexes for faster lookups
