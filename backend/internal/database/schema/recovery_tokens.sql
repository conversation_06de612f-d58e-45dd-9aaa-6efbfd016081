-- Create recovery_tokens table
CREATE TABLE recovery_tokens (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- Renamed from token
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX idx_recovery_tokens_token_hash ON recovery_tokens(token_hash); -- Renamed from idx_recovery_tokens_token and updated column
CREATE INDEX idx_recovery_tokens_user_id ON recovery_tokens(user_id);
