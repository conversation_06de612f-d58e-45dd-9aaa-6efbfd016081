-- Create media table
CREATE TABLE media (
    media_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    media_type VARCHAR(20) NOT NULL, -- 'image', 'video', 'audio', 'document'
    url VARCHAR(255) NOT NULL,
    thumbnail_url VARCHAR(255),
    file_name VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(100),
    duration INTEGER, -- For audio/video, in seconds
    width INTEGER, -- For images/videos
    height INTEGER, -- For images/videos
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Create indexes for faster lookups
CREATE INDEX idx_media_user_id ON media(user_id);
