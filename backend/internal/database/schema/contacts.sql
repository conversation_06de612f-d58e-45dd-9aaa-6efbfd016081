-- Create contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    display_name VA<PERSON>HAR(255) NOT NULL,
    relationship VARCHAR(50) NOT NULL DEFAULT 'friend',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_favorite BOOLEAN NOT NULL DEFAULT FALSE,
    is_blocked BOOLEAN NOT NULL DEFAULT FALSE,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, contact_id)
);

-- Create indexes for faster lookups
CREATE INDEX idx_contacts_user_id ON contacts(user_id);
CREATE INDEX idx_contacts_contact_id ON contacts(contact_id);
CREATE INDEX idx_contacts_is_favorite ON contacts(is_favorite);
CREATE INDEX idx_contacts_last_interaction ON contacts(last_interaction_at);
