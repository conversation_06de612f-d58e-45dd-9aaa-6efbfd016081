package database

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/DataDog/datadog-go/statsd"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/meena/backend/internal/config"
	"github.com/sony/gobreaker"
)

var dbCircuitBreaker *gobreaker.CircuitBreaker
var ddStatsdClient *statsd.Client

// DB represents a database connection pool
type DB struct {
	Pool *pgxpool.Pool
}

// New creates a new database connection pool
func New(cfg *config.Config) (*DB, error) {
	var connString string
	var err error

	// Use DATABASE_URL if provided, otherwise build from individual parameters
	if cfg.Database.URL != "" {
		connString = cfg.Database.URL
		log.Println("Using DATABASE_URL environment variable for database connection")
	} else {
		// Create connection string from individual parameters
		connString = fmt.Sprintf(
			"postgres://%s:%s@%s:%s/%s?sslmode=%s",
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.DBName,
			cfg.Database.SSLMode,
		)
		log.Println("Using individual database parameters for connection")
	}

	// Create connection pool configuration
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, fmt.Errorf("error parsing connection string: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = 20                      // Increase max connections
	poolConfig.MinConns = 5                       // Increase min connections for better availability
	poolConfig.MaxConnLifetime = time.Hour        // Maximum lifetime of a connection
	poolConfig.MaxConnIdleTime = 15 * time.Minute // Reduce idle time to recycle connections more frequently

	// Configure connection health checks
	poolConfig.HealthCheckPeriod = 30 * time.Second // Check connection health every 30 seconds

	// Configure connection retry parameters
	poolConfig.ConnConfig.ConnectTimeout = 5 * time.Second // Connection timeout
	poolConfig.BeforeConnect = func(ctx context.Context, config *pgx.ConnConfig) error {
		log.Println("Attempting to establish database connection...")
		return nil
	}
	poolConfig.AfterConnect = func(ctx context.Context, conn *pgx.Conn) error {
		log.Println("Database connection established successfully")
		// Set session parameters for better stability
		_, err := conn.Exec(ctx, "SET statement_timeout = '30s'")
		if err != nil {
			log.Printf("Warning: Failed to set statement_timeout: %v", err)
		}
		return nil
	}

	// Create connection pool
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("error creating connection pool: %w", err)
	}

	// Test connection
	if err := pool.Ping(ctx); err != nil {
		return nil, fmt.Errorf("error connecting to database: %w", err)
	}

	log.Println("Successfully connected to database")

	// Initialize circuit breaker
	dbCircuitBreaker = gobreaker.NewCircuitBreaker(gobreaker.Settings{
		Name:        "database",
		MaxRequests: 100,              // The maximum number of concurrent requests allowed when the circuit breaker is half-open.
		Interval:    60 * time.Second, // The period of time to measure the number of failures and calculate the failure rate.
		Timeout:     30 * time.Second, // The period of time during which the circuit breaker is open.
		ReadyToTrip: func(counts gobreaker.Counts) bool { // Function to determine when to trip the circuit breaker to the open state.
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 10 && failureRatio >= 0.6 // Trip if at least 10 requests and 60% failure rate
		},
		OnStateChange: func(name string, from, to gobreaker.State) { // Callback function to be called when the state changes.
			log.Printf("Circuit Breaker '%s' changed from '%s' to '%s'", name, from, to)
		},
	})
	log.Println("Database circuit breaker initialized")

	// Initialize Datadog Statsd client
	statsdAddr := fmt.Sprintf("%s:%s", cfg.Datadog.Host, cfg.Datadog.Port)
	client, err := statsd.New(statsdAddr)
	if err != nil {
		log.Printf("Error initializing Datadog statsd client: %v", err)
		// Continue without statsd if initialization fails
	} else {
		ddStatsdClient = client
		log.Printf("Datadog statsd client initialized for address: %s", statsdAddr)

		// Register pgxpool metrics
		go reportPoolMetrics(pool)
	}

	return &DB{Pool: pool}, nil
}

// reportPoolMetrics reports pgxpool metrics to Datadog at regular intervals
func reportPoolMetrics(pool *pgxpool.Pool) {
	if ddStatsdClient == nil {
		return // Statds client not initialized
	}

	ticker := time.NewTicker(15 * time.Second) // Report every 15 seconds
	defer ticker.Stop()

	for range ticker.C {
		stats := pool.Stat()
		tags := []string{"db:meena"} // Add relevant tags

		ddStatsdClient.Gauge("db.pool.total_connections", float64(stats.TotalConns()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.idle_connections", float64(stats.IdleConns()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.acquired_connections", float64(stats.AcquiredConns()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.constructing_connections", float64(stats.ConstructingConns()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.empty_acquire_count", float64(stats.EmptyAcquireCount()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.canceled_acquire_count", float64(stats.CanceledAcquireCount()), tags, 1.0)
		ddStatsdClient.Gauge("db.pool.acquire_duration_nanoseconds", float64(stats.AcquireDuration().Nanoseconds()), tags, 1.0)

		// Log metrics being reported (optional)
		// log.Printf("Reporting DB pool metrics: Total=%d, Idle=%d, Acquired=%d",
		// 	stats.TotalConns(), stats.IdleConns(), stats.AcquiredConns())
	}
}

// Close closes the database connection pool
func (db *DB) Close() {
	if db.Pool != nil {
		db.Pool.Close()
	}
}

// Ping checks if the database connection is alive
func (db *DB) Ping(ctx context.Context) error {
	return db.Pool.Ping(ctx)
}

// ExecContext executes a query that doesn't return rows with retry logic and instruments it
func (db *DB) ExecContext(ctx context.Context, sql string, args ...interface{}) (pgconn.CommandTag, error) {
	start := time.Now()
	var cmdTag pgconn.CommandTag
	var err error

	res, cbErr := dbCircuitBreaker.Execute(func() (interface{}, error) {
		// The operation for ExecContext should use Pool.Exec, not Pool.Query
		return db.Pool.Exec(ctx, sql, args...)
	})

	duration := time.Since(start)

	if cbErr != nil {
		err = cbErr // Circuit breaker error
	} else if res != nil {
		cmdTag = res.(pgconn.CommandTag)
	}
	// Note: pgxpool.Pool.Exec itself doesn't have a direct retry wrapper in this structure,
	// Retries are generally more complex for non-idempotent Exec operations.
	// The current withRetry is for Query operations returning pgx.Rows.
	// For simplicity, direct Exec is used here. If retries are needed for Exec,
	// a separate withExecRetry or similar would be required, considering idempotency.

	if ddStatsdClient != nil {
		tags := []string{"db:meena", "query_type:exec"}
		if err != nil {
			tags = append(tags, "error:true")
			ddStatsdClient.Incr("db.query.errors", tags, 1.0)
		} else {
			tags = append(tags, "error:false")
		}
		ddStatsdClient.Timing("db.query.duration", duration, tags, 1.0)
	}

	return cmdTag, err
}

// QueryContext executes a query that returns rows with retry logic and instruments it
func (db *DB) QueryContext(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	start := time.Now()
	result, err := dbCircuitBreaker.Execute(func() (interface{}, error) {
		rows, err := db.withRetry(ctx, func() (pgx.Rows, error) {
			return db.Pool.Query(ctx, sql, args...)
		})
		return rows, err
	})
	duration := time.Since(start)

	if ddStatsdClient != nil {
		tags := []string{"db:meena", "query_type:query"}
		if err != nil {
			tags = append(tags, "error:true")
			ddStatsdClient.Incr("db.query.errors", tags, 1.0)
		} else {
			tags = append(tags, "error:false")
		}
		ddStatsdClient.Timing("db.query.duration", duration, tags, 1.0)
	}

	if err != nil {
		return nil, err
	}

	return result.(pgx.Rows), nil
}

// QueryRowContext executes a query that returns a single row and instruments it
// Note: We don't use retry for QueryRow because it defers error handling until Scan is called
func (db *DB) QueryRowContext(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	start := time.Now()
	// QueryRowContext defers error handling until Scan(), so we can't easily wrap it with the circuit breaker
	// without significant changes or wrapping the pgx.Row type.
	// For now, we will not apply the circuit breaker to QueryRowContext to avoid complexity.
	// Monitoring will still capture metrics and errors for this operation.
	row := db.Pool.QueryRow(ctx, sql, args...)
	duration := time.Since(start)

	if ddStatsdClient != nil {
		tags := []string{"db:meena", "query_type:query_row"}
		// We can't reliably tag with error:true/false here without wrapping pgx.Row
		ddStatsdClient.Timing("db.query.duration", duration, tags, 1.0)
	}

	return row
}

// withRetry implements retry logic for database operations
func (db *DB) withRetry(ctx context.Context, operation func() (pgx.Rows, error)) (pgx.Rows, error) {
	var rows pgx.Rows
	var err error
	var retryCount int
	const maxRetries = 3
	const retryDelay = 100 * time.Millisecond

	for retryCount = 0; retryCount < maxRetries; retryCount++ {
		rows, err = operation()
		if err == nil {
			return rows, nil
		}

		// Check if the error is retryable
		if isRetryableError(err) {
			log.Printf("Retryable database error (attempt %d/%d): %v", retryCount+1, maxRetries, err)

			// Wait before retrying with exponential backoff
			backoff := retryDelay * time.Duration(1<<uint(retryCount))
			time.Sleep(backoff)
			continue
		}

		// Non-retryable error, return immediately
		return nil, err
	}

	log.Printf("Database operation failed after %d retries: %v", maxRetries, err)
	return nil, err
}

// isRetryableError determines if an error is retryable
func isRetryableError(err error) bool {
	// Check for connection-related errors that are typically transient
	if err == nil {
		return false
	}

	errStr := err.Error()
	retryableErrors := []string{
		"connection reset by peer",
		"broken pipe",
		"connection refused",
		"i/o timeout",
		"EOF",
		"connection closed",
		"connection terminated",
		"server closed the connection",
		"unexpected EOF",
		"connection reset",
		"no connection",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(strings.ToLower(errStr), retryableErr) {
			return true
		}
	}

	return false
}
