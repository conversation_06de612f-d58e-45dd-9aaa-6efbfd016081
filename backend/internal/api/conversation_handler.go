package api

import (
	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// ConversationHandler handles conversation-related requests
type ConversationHandler struct {
	conversationService *services.ConversationService
}

// NewConversationHandler creates a new ConversationHandler
func NewConversationHandler(conversationService *services.ConversationService) *ConversationHandler {
	return &ConversationHandler{
		conversationService: conversationService,
	}
}

// GetConversations handles get conversations requests
func (h *ConversationHandler) GetConversations(c *gin.Context) {
	userID := c.GetString("user_id")

	conversations, err := h.conversationService.GetConversations(c.Request.Context(), userID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"conversations": conversations,
	})
}

// GetConversation handles get conversation requests
func (h *ConversationHandler) GetConversation(c *gin.Context) {
	conversationID := c.Param("conversation_id")

	conversation, err := h.conversationService.GetConversation(c.Request.Context(), conversationID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, conversation)
}

// CreateConversation handles create conversation requests
func (h *ConversationHandler) CreateConversation(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.CreateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	conversation, err := h.conversationService.CreateConversation(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, conversation)
}

// UpdateConversation handles update conversation requests
func (h *ConversationHandler) UpdateConversation(c *gin.Context) {
	conversationID := c.Param("conversation_id")

	var req models.UpdateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	conversation, err := h.conversationService.UpdateConversation(c.Request.Context(), conversationID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, conversation)
}

// AddParticipants handles add participants requests
func (h *ConversationHandler) AddParticipants(c *gin.Context) {
	conversationID := c.Param("conversation_id")

	var req models.AddParticipantsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	err := h.conversationService.AddParticipants(c.Request.Context(), conversationID, req.ParticipantIDs)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Participants added successfully",
	})
}

// RemoveParticipant handles remove participant requests
func (h *ConversationHandler) RemoveParticipant(c *gin.Context) {
	conversationID := c.Param("conversation_id")
	participantID := c.Param("participant_id")

	err := h.conversationService.RemoveParticipant(c.Request.Context(), conversationID, participantID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Participant removed successfully",
	})
}

// LeaveConversation handles leave conversation requests
func (h *ConversationHandler) LeaveConversation(c *gin.Context) {
	userID := c.GetString("user_id")
	conversationID := c.Param("conversation_id")

	err := h.conversationService.LeaveConversation(c.Request.Context(), userID, conversationID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Left conversation successfully",
	})
}

// ArchiveConversation handles archive conversation requests
func (h *ConversationHandler) ArchiveConversation(c *gin.Context) {
	userID := c.GetString("user_id")
	conversationID := c.Param("conversation_id")

	err := h.conversationService.ArchiveConversation(c.Request.Context(), userID, conversationID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Conversation archived successfully",
	})
}

// UnarchiveConversation handles unarchive conversation requests
func (h *ConversationHandler) UnarchiveConversation(c *gin.Context) {
	userID := c.GetString("user_id")
	conversationID := c.Param("conversation_id")

	err := h.conversationService.UnarchiveConversation(c.Request.Context(), userID, conversationID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Conversation unarchived successfully",
	})
}
