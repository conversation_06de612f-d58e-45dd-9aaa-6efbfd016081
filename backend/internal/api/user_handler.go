package api

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// UserHandler handles user-related requests
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler creates a new UserHandler
func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetProfile handles get profile requests
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := c.GetString("user_id")

	profile, err := h.userService.GetProfile(c.Request.Context(), userID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, profile)
}

// UpdateProfile handles update profile requests
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	profile, err := h.userService.UpdateProfile(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, profile)
}

// GetUserByHandle handles get user by handle requests
func (h *UserHandler) GetUserByHandle(c *gin.Context) {
	userHandle := c.Param("user_handle")

	profile, err := h.userService.GetUserByHandle(c.Request.Context(), userHandle)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, profile)
}

// SearchUsers handles search users requests
func (h *UserHandler) SearchUsers(c *gin.Context) {
	query := c.Query("q")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	users, total, err := h.userService.SearchUsers(c.Request.Context(), query, limit, offset)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"users": users,
		"total": total,
	})
}
