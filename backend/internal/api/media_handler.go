package api

import (
	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// MediaHandler handles media-related requests
type MediaHandler struct {
	mediaService *services.MediaService
}

// NewMediaHandler creates a new MediaHandler
func NewMediaHandler(mediaService *services.MediaService) *MediaHandler {
	return &MediaHandler{
		mediaService: mediaService,
	}
}

// UploadMedia handles upload media requests
func (h *MediaHandler) UploadMedia(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.UploadMediaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	response, err := h.mediaService.UploadMedia(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, response)
}

// GetMedia handles get media requests
func (h *MediaHandler) GetMedia(c *gin.Context) {
	mediaID := c.Param("media_id")

	media, err := h.mediaService.GetMedia(c.Request.Context(), mediaID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, media)
}

// DeleteMedia handles delete media requests
func (h *MediaHandler) DeleteMedia(c *gin.Context) {
	userID := c.GetString("user_id")
	mediaID := c.Param("media_id")

	err := h.mediaService.DeleteMedia(c.Request.Context(), userID, mediaID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendNoContent(c)
}
