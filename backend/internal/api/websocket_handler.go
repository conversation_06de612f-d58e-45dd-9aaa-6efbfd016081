package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/meena/backend/internal/config"
	"github.com/meena/backend/internal/logging"
	"github.com/meena/backend/internal/middleware"
	"go.uber.org/zap"

	"github.com/DataDog/datadog-go/statsd"
)

// WebSocketHandler handles WebSocket connections
//
// This handler uses the WebSocket protocol's built-in ping/pong mechanism for connection
// health monitoring. It sends protocol-level ping frames every 30 seconds and expects
// clients to respond with pong frames. The protocol-level ping/pong mechanism is more
// efficient than application-level messages for connection health monitoring.
type WebSocketHandler struct {
	clients      map[string]*Client
	register     chan *Client
	unregister   chan *Client
	broadcast    chan []byte
	statsdClient *statsd.Client // Add statsd client
	config       *config.Config // Add config
}

// Client represents a WebSocket client
type Client struct {
	ID            string
	UserID        string
	CorrelationID string // Add CorrelationID field
	Conn          *websocket.Conn
	Send          chan []byte
}

// Message represents a WebSocket message
type Message struct {
	Type      string          `json:"type"`
	ID        string          `json:"id,omitempty"`
	Payload   json.RawMessage `json:"payload"`
	Timestamp int64           `json:"timestamp"`
}

// NewWebSocketHandler creates a new WebSocketHandler
func NewWebSocketHandler(statsdClient *statsd.Client, cfg *config.Config) *WebSocketHandler {
	handler := &WebSocketHandler{
		clients:      make(map[string]*Client),
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		broadcast:    make(chan []byte),
		statsdClient: statsdClient, // Assign statsd client
		config:       cfg,          // Assign config
	}

	// Start the handler
	go handler.run()

	return handler
}

// run runs the WebSocket handler
func (h *WebSocketHandler) run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client.ID] = client
			logging.Info("Client registered",
				zap.String("client_id", client.ID),
				zap.String("user_id", client.UserID),
				logging.AddCorrelationID(client.CorrelationID),
			)

			// Increment WebSocket connection metric
			if h.statsdClient != nil {
				h.statsdClient.Incr("websocket.connections.established", []string{"status:success"}, 1)
			}

			// Send welcome message
			welcomeMsg := Message{
				Type: "welcome",
				Payload: json.RawMessage(fmt.Sprintf(`{
					"session_id": "%s",
					"user_id": "%s",
					"server_time": %d
				}`, client.ID, client.UserID, time.Now().Unix())),
				Timestamp: time.Now().Unix(),
			}
			welcomeBytes, _ := json.Marshal(welcomeMsg)
			client.Send <- welcomeBytes

		case client := <-h.unregister:
			if _, ok := h.clients[client.ID]; ok {
				delete(h.clients, client.ID)
				close(client.Send)
				logging.Info("Client unregistered",
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			}
			// Decrement WebSocket connection metric
			if h.statsdClient != nil {
				h.statsdClient.Decr("websocket.connections.established", []string{"status:success"}, 1)
			}

		case message := <-h.broadcast:
			for _, client := range h.clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(h.clients, client.ID)
				}
			}
		}
	}
}

// HandleConnection handles WebSocket connection requests
func (h *WebSocketHandler) HandleConnection(c *gin.Context) {
	// Get correlation ID from context
	correlationID, exists := c.Get("correlation_id")
	correlationIDStr := ""
	if exists {
		correlationIDStr, _ = correlationID.(string)
	}

	// Check if this is a WebSocket request by looking for the required headers
	connection := c.GetHeader("Connection")
	upgrade := c.GetHeader("Upgrade")

	// If this is not a WebSocket request (e.g., from a web crawler), return a friendly message
	if !strings.Contains(strings.ToLower(connection), "upgrade") || strings.ToLower(upgrade) != "websocket" {
		logging.Warn("WebSocket connection attempt with invalid headers",
			zap.String("connection_header", connection),
			zap.String("upgrade_header", upgrade),
			zap.String("ip", c.ClientIP()),
			logging.AddCorrelationID(correlationIDStr),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "This endpoint requires a WebSocket connection",
			"message": "This is a WebSocket endpoint for real-time communication. Please use a WebSocket client to connect.",
			"details": fmt.Sprintf("Expected 'Connection: Upgrade' and 'Upgrade: websocket' headers. Got Connection: '%s', Upgrade: '%s'", connection, upgrade),
		})
		return
	}

	// Get the token from the query string
	token := c.Query("token")
	if token == "" {
		// Try to get the token from the Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			parts := strings.Split(authHeader, " ")
			if len(parts) == 2 && parts[0] == "Bearer" {
				token = parts[1]
			} else {
				logging.Warn("WebSocket connection attempt with malformed Authorization header",
					zap.String("auth_header", authHeader),
					zap.String("ip", c.ClientIP()),
					logging.AddCorrelationID(correlationIDStr),
				)
			}
		}
	}

	// Validate the token
	if token == "" {
		logging.Warn("WebSocket connection attempt without authentication token",
			zap.String("ip", c.ClientIP()),
			logging.AddCorrelationID(correlationIDStr),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Missing authentication token",
			"message": "Please provide a valid authentication token via the 'token' query parameter or 'Authorization: Bearer <token>' header.",
		})
		return
	}

	// Parse the token
	parsedToken, err := jwt.ParseWithClaims(token, &middleware.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(h.config.JWT.Secret), nil
	})

	if err != nil || !parsedToken.Valid {
		logging.Warn("WebSocket connection attempt with invalid token",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
			logging.AddCorrelationID(correlationIDStr),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Invalid authentication token",
			"message": "The provided authentication token is invalid or expired. Please obtain a new token by logging in.",
		})
		return
	}

	// Get the claims
	claims, ok := parsedToken.Claims.(*middleware.JWTClaims)
	if !ok {
		logging.Warn("WebSocket connection attempt with invalid token claims",
			zap.String("ip", c.ClientIP()),
			logging.AddCorrelationID(correlationIDStr),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Invalid token claims",
			"message": "The provided authentication token has invalid claims. Please obtain a new token by logging in.",
		})
		return
	}

	// Upgrade the HTTP connection to a WebSocket connection
	upgrader := websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins in development
		},
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logging.Error("Error upgrading WebSocket connection",
			err,
			zap.String("user_id", claims.UserID),
			// Increment WebSocket connection error metric
			func() zap.Field {
				if h.statsdClient != nil {
					h.statsdClient.Incr("websocket.connections.errors", []string{"type:upgrade"}, 1)
				}
				return zap.Skip() // Skip this field in the log output
			}(),
			zap.String("ip", c.ClientIP()),
			logging.AddCorrelationID(correlationIDStr),
		)
		// Don't attempt to write to the response here as Gin may have already written headers
		return
	}

	logging.Info("WebSocket connection successfully established",
		zap.String("user_id", claims.UserID),
		zap.String("ip", c.ClientIP()),
		logging.AddCorrelationID(correlationIDStr),
	)

	// Create a new client
	client := &Client{
		ID:            uuid.New().String(),
		UserID:        claims.UserID,
		CorrelationID: correlationIDStr, // Assign correlation ID to client
		Conn:          conn,
		Send:          make(chan []byte, 256),
	}

	// Register the client
	h.register <- client

	// Start goroutines for reading and writing
	go h.readPump(client)
	go h.writePump(client)
}

// readPump pumps messages from the WebSocket connection to the hub
func (h *WebSocketHandler) readPump(client *Client) {
	defer func() {
		h.unregister <- client
		client.Conn.Close()
	}()

	client.Conn.SetReadLimit(512 * 1024) // 512KB max message size
	client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))

	// Set handlers for WebSocket protocol ping/pong (control frames)
	client.Conn.SetPongHandler(func(appData string) error {
		// Reduce log spam by not logging every pong
		// logging.Debug("Received WebSocket protocol-level PONG control frame", zap.String("client_id", client.ID), zap.String("user_id", client.UserID), logging.AddCorrelationID(client.CorrelationID))
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	client.Conn.SetPingHandler(func(appData string) error {
		// Reduce log spam by not logging every ping
		// logging.Debug("Received WebSocket protocol-level PING control frame", zap.String("client_id", client.ID), zap.String("user_id", client.UserID), logging.AddCorrelationID(client.CorrelationID))
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		// Respond with a protocol-level PONG control frame
		err := client.Conn.WriteControl(websocket.PongMessage, []byte{}, time.Now().Add(10*time.Second))
		if err != nil {
			logging.Error("Error sending WebSocket protocol-level PONG control frame",
				err,
				zap.String("client_id", client.ID),
				zap.String("user_id", client.UserID),
				logging.AddCorrelationID(client.CorrelationID),
			)
		}
		return nil
	})

	for {
		_, message, err := client.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logging.Error("Error reading WebSocket message",
					err,
					zap.String("client_id", client.ID),
					// Increment WebSocket read error metric
					func() zap.Field {
						if h.statsdClient != nil {
							h.statsdClient.Incr("websocket.messages.read.errors", []string{"type:unexpected_close"}, 1)
						}
						return zap.Skip() // Skip this field in the log output
					}(),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			} else if strings.Contains(err.Error(), "websocket: close") {
				// Normal closure, log with the close code if available
				logging.Info("Client disconnected",
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			}
			break
		}

		// Parse the message
		var msg Message
		if err := json.Unmarshal(message, &msg); err != nil {
			logging.Error("Error parsing WebSocket message",
				err,
				zap.String("client_id", client.ID),
				// Increment WebSocket message parse error metric
				func() zap.Field {
					if h.statsdClient != nil {
						h.statsdClient.Incr("websocket.messages.parse.errors", nil, 1)
					}
					return zap.Skip() // Skip this field in the log output
				}(),
				zap.String("user_id", client.UserID),
				logging.AddCorrelationID(client.CorrelationID),
			)
			continue
		}

		// Handle the message based on its type (case-insensitive)
		switch strings.ToLower(msg.Type) {

		case "ping", "PING":
			// Respond with a pong message (for backward compatibility)
			// Only log occasional pings to reduce spam
			if time.Now().Unix()%60 == 0 { // Log approximately once per minute
				logging.Debug("Received application-level PING",
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			}
			pongMsg := Message{
				Type:      "pong",
				ID:        msg.ID,
				Timestamp: time.Now().Unix(),
			}
			pongBytes, _ := json.Marshal(pongMsg)
			client.Send <- pongBytes
			// Also update the read deadline
			client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))

		case "pong", "PONG":
			// Just acknowledge the pong message by updating the read deadline
			// This is for backward compatibility with older clients
			// Only log occasional pongs to reduce spam
			if time.Now().Unix()%60 == 0 { // Log approximately once per minute
				logging.Debug("Received application-level PONG",
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			}
			client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))

		case "message":
			// In a real implementation, this would process the message and store it in the database
			// For now, we'll just echo it back
			msg.Timestamp = time.Now().Unix()
			msgBytes, _ := json.Marshal(msg)
			h.broadcast <- msgBytes
			// Increment WebSocket message received metric
			if h.statsdClient != nil {
				h.statsdClient.Incr("websocket.messages.received", []string{"type:" + strings.ToLower(msg.Type)}, 1)
			}

		default:
			// Unknown message type
			logging.Warn("Unknown WebSocket message type",
				zap.String("message_type", msg.Type),
				zap.String("message_id", msg.ID),
				zap.String("client_id", client.ID),
				zap.String("user_id", client.UserID),
				logging.AddCorrelationID(client.CorrelationID),
			)

			// Send an error message back to the client
			errorMsg := Message{
				Type:      "error",
				ID:        msg.ID,
				Timestamp: time.Now().Unix(),
				Payload:   json.RawMessage(`{"code": 400, "message": "Unknown message type"}`),
			}
			errorBytes, _ := json.Marshal(errorMsg)
			client.Send <- errorBytes
			// Increment WebSocket unknown message type metric
			if h.statsdClient != nil {
				h.statsdClient.Incr("websocket.messages.unknown_type", []string{"type:" + strings.ToLower(msg.Type)}, 1)
			}
		}
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (h *WebSocketHandler) writePump(client *Client) {
	ticker := time.NewTicker(30 * time.Second)
	defer func() {
		ticker.Stop()
		client.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// The hub closed the channel
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := client.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				// Increment WebSocket write error metric
				if h.statsdClient != nil {
					h.statsdClient.Incr("websocket.messages.write.errors", []string{"type:next_writer"}, 1)
				}
				return
			}
			w.Write(message)

			// Add queued messages to the current websocket message
			n := len(client.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-client.Send)
			}

			if err := w.Close(); err != nil {
				// Increment WebSocket write error metric
				if h.statsdClient != nil {
					h.statsdClient.Incr("websocket.messages.write.errors", []string{"type:close_writer"}, 1)
				}
				return
			}
			// Increment WebSocket message sent metric
			if h.statsdClient != nil {
				h.statsdClient.Incr("websocket.messages.sent", nil, 1)
			}

		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			// Only log occasional pings to reduce spam
			if time.Now().Unix()%60 == 0 { // Log approximately once per minute
				logging.Debug("Sending WebSocket protocol-level PING control frame",
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
			}
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				logging.Error("Error sending protocol-level PING control frame",
					err,
					// Increment WebSocket ping error metric
					func() zap.Field {
						if h.statsdClient != nil {
							h.statsdClient.Incr("websocket.ping.errors", nil, 1)
						}
						return zap.Skip() // Skip this field in the log output
					}(),
					zap.String("client_id", client.ID),
					zap.String("user_id", client.UserID),
					logging.AddCorrelationID(client.CorrelationID),
				)
				return
			}
		}
	}
}
