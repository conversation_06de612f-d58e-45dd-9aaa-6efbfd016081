package api

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// MessageHandler handles message-related requests
type MessageHandler struct {
	messageService *services.MessageService
}

// NewMessageHandler creates a new MessageHandler
func NewMessageHandler(messageService *services.MessageService) *MessageHandler {
	return &MessageHandler{
		messageService: messageService,
	}
}

// GetMessages handles get messages requests
func (h *MessageHandler) GetMessages(c *gin.Context) {
	conversationID := c.Query("conversation_id")
	if conversationID == "" {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Field:   "conversation_id",
				Issue:   "required",
				Message: "Conversation ID is required",
			},
		})
		return
	}

	limit, _ := strconv.Atoi(c.<PERSON>("limit", "50"))
	offset, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("offset", "0"))

	messages, total, err := h.messageService.GetMessages(c.Request.Context(), conversationID, limit, offset)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"messages": messages,
		"total":    total,
	})
}

// GetMessage handles get message requests
func (h *MessageHandler) GetMessage(c *gin.Context) {
	messageID := c.Param("message_id")

	message, err := h.messageService.GetMessage(c.Request.Context(), messageID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, message)
}

// SendMessage handles send message requests
func (h *MessageHandler) SendMessage(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	message, err := h.messageService.SendMessage(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, message)
}

// EditMessage handles edit message requests
func (h *MessageHandler) EditMessage(c *gin.Context) {
	userID := c.GetString("user_id")
	messageID := c.Param("message_id")

	var req models.EditMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	message, err := h.messageService.EditMessage(c.Request.Context(), userID, messageID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, message)
}

// DeleteMessage handles delete message requests
func (h *MessageHandler) DeleteMessage(c *gin.Context) {
	userID := c.GetString("user_id")
	messageID := c.Param("message_id")

	err := h.messageService.DeleteMessage(c.Request.Context(), userID, messageID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendNoContent(c)
}

// AddReaction handles add reaction requests
func (h *MessageHandler) AddReaction(c *gin.Context) {
	userID := c.GetString("user_id")
	messageID := c.Param("message_id")

	var req models.AddReactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	err := h.messageService.AddReaction(c.Request.Context(), userID, messageID, req.Emoji)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Reaction added successfully",
	})
}

// RemoveReaction handles remove reaction requests
func (h *MessageHandler) RemoveReaction(c *gin.Context) {
	userID := c.GetString("user_id")
	messageID := c.Param("message_id")
	emoji := c.Param("emoji")

	err := h.messageService.RemoveReaction(c.Request.Context(), userID, messageID, emoji)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Reaction removed successfully",
	})
}

// MarkAsRead handles mark as read requests
func (h *MessageHandler) MarkAsRead(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.MarkAsReadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	err := h.messageService.MarkAsRead(c.Request.Context(), userID, req.ConversationID, req.MessageIDs)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Messages marked as read successfully",
	})
}
