package api

import (
	"github.com/DataDog/datadog-go/statsd"
	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/config"
	"github.com/meena/backend/internal/database"
	"github.com/meena/backend/internal/middleware"
	"github.com/meena/backend/internal/services"
)

// SetupRoutes sets up the API routes
func SetupRoutes(router *gin.Engine, userRepo *database.UserRepository, contactRepo *database.ContactRepository, contactGroupRepo *database.ContactGroupRepository, refreshTokenRepo *database.RefreshTokenRepository, statsdClient *statsd.Client, cfg *config.Config) {
	// Serve static files
	router.Static("/static", "./static")

	// Serve robots.txt at the root
	router.StaticFile("/robots.txt", "./static/robots.txt")
	// Create services
	authService := services.NewAuthService(userRepo, refreshTokenRepo, cfg)
	userService := services.NewUserService(userRepo)
	contactService := services.NewContactService(userService, contactRepo)
	contactGroupService := services.NewContactGroupService(contactGroupRepo)
	conversationService := services.NewConversationService()
	messageService := services.NewMessageService()
	mediaService := services.NewMediaService()

	// Create handlers
	authHandler := NewAuthHandler(authService)
	userHandler := NewUserHandler(userService)
	contactHandler := NewContactHandler(contactService)
	contactGroupHandler := NewContactGroupHandler(contactGroupService)
	conversationHandler := NewConversationHandler(conversationService)
	messageHandler := NewMessageHandler(messageService)
	mediaHandler := NewMediaHandler(mediaService)
	websocketHandler := NewWebSocketHandler(statsdClient, cfg)

	// API version group
	v1 := router.Group("/api/v1")

	// Health check
	v1.GET("/health", HealthCheckHandler)

	// Auth routes
	auth := v1.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/initiate-recovery", authHandler.InitiateAccountRecoveryHandler) // Added
		auth.POST("/reset-password", authHandler.ResetPasswordHandler)              // Ensured
		auth.POST("/logout", authHandler.LogoutHandler)
	}

	// Protected auth routes
	protectedAuth := v1.Group("/auth")
	protectedAuth.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
	{
		protectedAuth.POST("/password", authHandler.ChangePassword)
	}

	// Protected routes
	protected := v1.Group("")
	protected.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
	{
		// User routes
		user := protected.Group("/users")
		{
			user.GET("/me", userHandler.GetProfile)
			user.PUT("/me", userHandler.UpdateProfile)
			user.GET("/:user_handle", userHandler.GetUserByHandle)
			user.GET("/search", userHandler.SearchUsers)
		}

		// Contact routes
		contacts := protected.Group("/contacts")
		{
			contacts.GET("", contactHandler.GetContacts)
			contacts.GET("/favorites", contactHandler.GetFavoriteContacts)
			contacts.GET("/recent", contactHandler.GetRecentContacts)
			contacts.GET("/:contact_id", contactHandler.GetContact)
			contacts.POST("", contactHandler.AddContact)
			contacts.PUT("/:contact_id", contactHandler.UpdateContact)
			contacts.POST("/:contact_id/block", contactHandler.BlockContact)
			contacts.POST("/:contact_id/unblock", contactHandler.UnblockContact)
			contacts.DELETE("/:contact_id", contactHandler.RemoveContact)
		}

		// Contact group routes
		contactGroups := protected.Group("/contact-groups")
		{
			contactGroups.GET("", contactGroupHandler.GetContactGroups)
			contactGroups.POST("", contactGroupHandler.CreateContactGroup)
			contactGroups.GET("/:group_id", contactGroupHandler.GetContactGroup)
			contactGroups.PUT("/:group_id", contactGroupHandler.UpdateContactGroup)
			contactGroups.DELETE("/:group_id", contactGroupHandler.DeleteContactGroup)
			contactGroups.POST("/:group_id/members/:contact_id", contactGroupHandler.AddContactToGroup)
			contactGroups.DELETE("/:group_id/members/:contact_id", contactGroupHandler.RemoveContactFromGroup)
		}

		// Conversation routes
		conversations := protected.Group("/conversations")
		{
			conversations.GET("", conversationHandler.GetConversations)
			conversations.GET("/:conversation_id", conversationHandler.GetConversation)
			conversations.POST("", conversationHandler.CreateConversation)
			conversations.PUT("/:conversation_id", conversationHandler.UpdateConversation)
			conversations.POST("/:conversation_id/participants", conversationHandler.AddParticipants)
			conversations.DELETE("/:conversation_id/participants/:participant_id", conversationHandler.RemoveParticipant)
			conversations.POST("/:conversation_id/leave", conversationHandler.LeaveConversation)
			conversations.POST("/:conversation_id/archive", conversationHandler.ArchiveConversation)
			conversations.POST("/:conversation_id/unarchive", conversationHandler.UnarchiveConversation)
		}

		// Message routes
		messages := protected.Group("/messages")
		{
			messages.GET("", messageHandler.GetMessages)
			messages.GET("/:message_id", messageHandler.GetMessage)
			messages.POST("", messageHandler.SendMessage)
			messages.PUT("/:message_id", messageHandler.EditMessage)
			messages.DELETE("/:message_id", messageHandler.DeleteMessage)
			messages.POST("/:message_id/reactions", messageHandler.AddReaction)
			messages.DELETE("/:message_id/reactions/:emoji", messageHandler.RemoveReaction)
			messages.POST("/read", messageHandler.MarkAsRead)
		}

		// Media routes
		media := protected.Group("/media")
		{
			media.POST("/upload", mediaHandler.UploadMedia)
			media.POST("/upload/encrypted", mediaHandler.UploadEncryptedMedia)
			media.POST("/upload/chunked/init", mediaHandler.InitiateChunkedUpload)
			media.PUT("/upload/chunked/:upload_id/:chunk_index", mediaHandler.UploadChunk)
			media.GET("/upload/chunked/:upload_id/status", mediaHandler.GetChunkedUploadStatus)
			media.POST("/upload/chunked/:upload_id/complete", mediaHandler.CompleteChunkedUpload)
			media.GET("/:media_id", mediaHandler.GetMedia)
			media.DELETE("/:media_id", mediaHandler.DeleteMedia)
		}
	}

	// WebSocket route
	v1.GET("/ws", websocketHandler.HandleConnection)
}
