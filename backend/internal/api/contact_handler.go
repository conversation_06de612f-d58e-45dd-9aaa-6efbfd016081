package api

import (
	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// ContactHandler handles contact-related requests
type ContactHandler struct {
	contactService *services.ContactService
}

// NewContactHandler creates a new ContactHandler
func NewContactHandler(contactService *services.ContactService) *ContactHandler {
	return &ContactHandler{
		contactService: contactService,
	}
}

// GetContacts handles get contacts requests
func (h *ContactHandler) GetContacts(c *gin.Context) {
	userID := c.GetString("user_id")

	contacts, err := h.contactService.GetContacts(c.Request.Context(), userID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"contacts": contacts,
	})
}

// GetContact handles get contact requests
func (h *ContactHandler) GetContact(c *gin.Context) {
	userID := c.GetString("user_id")
	contactID := c.Param("contact_id")

	contact, err := h.contactService.GetContact(c.Request.Context(), userID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, contact)
}

// AddContact handles add contact requests
func (h *ContactHandler) AddContact(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.AddContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	contact, err := h.contactService.AddContact(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, contact)
}

// UpdateContact handles update contact requests
func (h *ContactHandler) UpdateContact(c *gin.Context) {
	userID := c.GetString("user_id")
	contactID := c.Param("contact_id")

	var req models.UpdateContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	contact, err := h.contactService.UpdateContact(c.Request.Context(), userID, contactID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, contact)
}

// BlockContact handles block contact requests
func (h *ContactHandler) BlockContact(c *gin.Context) {
	userID := c.GetString("user_id")
	contactID := c.Param("contact_id")

	err := h.contactService.BlockContact(c.Request.Context(), userID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Contact blocked successfully",
	})
}

// UnblockContact handles unblock contact requests
func (h *ContactHandler) UnblockContact(c *gin.Context) {
	userID := c.GetString("user_id")
	contactID := c.Param("contact_id")

	err := h.contactService.UnblockContact(c.Request.Context(), userID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Contact unblocked successfully",
	})
}

// RemoveContact handles remove contact requests
func (h *ContactHandler) RemoveContact(c *gin.Context) {
	userID := c.GetString("user_id")
	contactID := c.Param("contact_id")

	err := h.contactService.RemoveContact(c.Request.Context(), userID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendNoContent(c)
}

// GetFavoriteContacts handles get favorite contacts requests
func (h *ContactHandler) GetFavoriteContacts(c *gin.Context) {
	userID := c.GetString("user_id")

	contacts, err := h.contactService.GetFavoriteContacts(c.Request.Context(), userID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"contacts": contacts,
	})
}

// GetRecentContacts handles get recent contacts requests
func (h *ContactHandler) GetRecentContacts(c *gin.Context) {
	userID := c.GetString("user_id")

	// Parse limit parameter with default value of 10
	limit := utils.ParseIntParam(c, "limit", 10)

	contacts, err := h.contactService.GetRecentContacts(c.Request.Context(), userID, limit)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"contacts": contacts,
	})
}
