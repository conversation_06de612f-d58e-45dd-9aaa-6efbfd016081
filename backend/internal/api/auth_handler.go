package api

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/database" // For database.ErrUserNotFound
	"github.com/meena/backend/internal/logging"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
	"go.uber.org/zap"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	authService *services.AuthService
}

// NewAuthHandler creates a new AuthHandler
func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login handles login requests
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	response, err := h.authService.Login(c.Request.Context(), req)
	if err != nil {
		// Check if the error is related to invalid credentials
		if strings.Contains(err.Error(), "invalid credentials") {
			logging.Error("Authentication failed: Invalid credentials", err, zap.String("identifier", req.Identifier), zap.String("client_ip", c.ClientIP()))
			logging.Error("Authentication failed: Invalid credentials", err, zap.String("identifier", req.Identifier), zap.String("client_ip", c.ClientIP()))
			// Send unauthorized error for invalid credentials
			utils.SendError(c, http.StatusUnauthorized, "invalid_credentials", err.Error(), nil, err)
		} else {
			logging.Error("Authentication failed: Internal error", err, zap.String("identifier", req.Identifier), zap.String("client_ip", c.ClientIP()))
			logging.Error("Authentication failed: Internal error", err, zap.String("identifier", req.Identifier), zap.String("client_ip", c.ClientIP()))
			// For other errors, send internal error
			utils.SendInternalError(c, err)
		}
		return
	}

	utils.SendOK(c, response)
}

// Register handles registration requests
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	response, err := h.authService.Register(c.Request.Context(), req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, response)
}

// RefreshToken handles token refresh requests
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	response, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, response)
}

// InitiateRecoveryPayload defines the request body for initiating account recovery
type InitiateRecoveryPayload struct {
	UserHandle     string `json:"user_handle" binding:"required"`
	RecoveryPhrase string `json:"recovery_phrase" binding:"required"`
	RecoveryPin    string `json:"recovery_pin" binding:"required"`
}

// InitiateAccountRecoveryHandler handles initiating account recovery
func (h *AuthHandler) InitiateAccountRecoveryHandler(c *gin.Context) {
	var payload InitiateRecoveryPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		utils.SendValidationError(c, utils.FormatBindingError(err))
		return
	}

	sessionToken, err := h.authService.InitiateAccountRecovery(c.Request.Context(), payload.UserHandle, payload.RecoveryPhrase, payload.RecoveryPin)
	if err != nil {
		if err == database.ErrUserNotFound || err == services.ErrInvalidRecoveryCredentials {
			logging.Warn("Initiate account recovery failed: User not found or invalid credentials",
				zap.String("user_handle", payload.UserHandle),
				zap.Error(err))
			utils.SendError(c, http.StatusUnauthorized, "unauthorized", "Invalid user handle or recovery credentials.", nil, err)
		} else {
			logging.Error("Error initiating account recovery", err, zap.String("user_handle", payload.UserHandle))
			utils.SendInternalError(c, err)
		}
		return
	}

	utils.SendOK(c, gin.H{"recovery_session_token": sessionToken})
}

// ResetPasswordPayload defines the request body for resetting a password
type ResetPasswordPayload struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// ResetPasswordHandler handles password reset confirmation requests
func (h *AuthHandler) ResetPasswordHandler(c *gin.Context) {
	var payload ResetPasswordPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		utils.SendValidationError(c, utils.FormatBindingError(err))
		return
	}

	err := h.authService.ResetPassword(c.Request.Context(), payload.Token, payload.NewPassword)
	if err != nil {
		// Check for specific errors like invalid/expired token
		// This requires AuthService.ResetPassword to return distinguishable errors
		if strings.Contains(err.Error(), "invalid or expired token") || strings.Contains(err.Error(), "token not found") { // Example check
			utils.SendError(c, http.StatusBadRequest, "invalid_token", "Invalid or expired password reset token.", nil, err)
		} else {
			logging.Error("Error resetting password", err)
			utils.SendInternalError(c, err)
		}
		return
	}

	utils.SendOK(c, gin.H{"message": "Password has been reset successfully."})
}

// ChangePassword handles password change requests
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		utils.SendUnauthorizedError(c)
		return
	}

	var req models.PasswordChangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	err := h.authService.ChangePassword(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, gin.H{
		"message": "Password changed successfully",
	})
}

// LogoutRequest defines the structure for the logout request
type LogoutRequest struct {
	RefreshToken string `json:"refresh_token"`
}

// LogoutHandler handles logout requests
func (h *AuthHandler) LogoutHandler(c *gin.Context) {
	var req LogoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	if req.RefreshToken == "" {
		utils.SendError(c, http.StatusBadRequest, "refresh_token_required", "Refresh token is required", nil, nil)
		return
	}

	err := h.authService.Logout(c.Request.Context(), req.RefreshToken)
	if err != nil {
		// Log the error for debugging purposes
		logging.Error("Logout failed", err, zap.String("client_ip", c.ClientIP()))
		// Check for specific error types if needed, otherwise send a generic internal error
		if strings.Contains(err.Error(), "token not found") || strings.Contains(err.Error(), "token already revoked") {
			utils.SendError(c, http.StatusBadRequest, "invalid_token", "Invalid or already revoked refresh token", nil, err)
		} else {
			utils.SendInternalError(c, err)
		}
		return
	}

	c.Status(http.StatusNoContent)
}
