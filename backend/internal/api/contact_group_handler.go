package api

import (
	"github.com/gin-gonic/gin"
	"github.com/meena/backend/internal/models"
	"github.com/meena/backend/internal/services"
	"github.com/meena/backend/internal/utils"
)

// ContactGroupHandler handles contact group-related requests
type ContactGroupHandler struct {
	contactGroupService *services.ContactGroupService
}

// NewContactGroupHandler creates a new ContactGroupHandler
func NewContactGroupHandler(contactGroupService *services.ContactGroupService) *ContactGroupHandler {
	return &ContactGroupHandler{
		contactGroupService: contactGroupService,
	}
}

// GetContactGroups handles get contact groups requests
func (h *ContactGroupHandler) GetContactGroups(c *gin.Context) {
	userID := c.GetString("user_id")

	// Parse query parameters
	limit := utils.ParseIntParam(c, "limit", 50)
	offset := utils.ParseIntParam(c, "offset", 0)

	response, err := h.contactGroupService.GetContactGroups(c.Request.Context(), userID, limit, offset)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, response)
}

// GetContactGroup handles get contact group requests
func (h *ContactGroupHandler) GetContactGroup(c *gin.Context) {
	userID := c.GetString("user_id")
	groupID := c.Param("group_id")

	group, err := h.contactGroupService.GetContactGroup(c.Request.Context(), userID, groupID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, group)
}

// CreateContactGroup handles create contact group requests
func (h *ContactGroupHandler) CreateContactGroup(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.CreateContactGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	group, err := h.contactGroupService.CreateContactGroup(c.Request.Context(), userID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendCreated(c, group)
}

// UpdateContactGroup handles update contact group requests
func (h *ContactGroupHandler) UpdateContactGroup(c *gin.Context) {
	userID := c.GetString("user_id")
	groupID := c.Param("group_id")

	var req models.UpdateContactGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.SendValidationError(c, []models.ErrorDetail{
			{
				Message: "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	group, err := h.contactGroupService.UpdateContactGroup(c.Request.Context(), userID, groupID, req)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, group)
}

// DeleteContactGroup handles delete contact group requests
func (h *ContactGroupHandler) DeleteContactGroup(c *gin.Context) {
	userID := c.GetString("user_id")
	groupID := c.Param("group_id")

	err := h.contactGroupService.DeleteContactGroup(c.Request.Context(), userID, groupID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendNoContent(c)
}

// AddContactToGroup handles add contact to group requests
func (h *ContactGroupHandler) AddContactToGroup(c *gin.Context) {
	userID := c.GetString("user_id")
	groupID := c.Param("group_id")
	contactID := c.Param("contact_id")

	group, err := h.contactGroupService.AddContactToGroup(c.Request.Context(), userID, groupID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, group)
}

// RemoveContactFromGroup handles remove contact from group requests
func (h *ContactGroupHandler) RemoveContactFromGroup(c *gin.Context) {
	userID := c.GetString("user_id")
	groupID := c.Param("group_id")
	contactID := c.Param("contact_id")

	group, err := h.contactGroupService.RemoveContactFromGroup(c.Request.Context(), userID, groupID, contactID)
	if err != nil {
		utils.SendInternalError(c, err)
		return
	}

	utils.SendOK(c, group)
}
