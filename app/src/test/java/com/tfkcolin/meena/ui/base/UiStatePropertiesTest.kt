package com.tfkcolin.meena.ui.base

import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Test

class UiStatePropertiesTest {

    @Test
    fun `default constructor creates properties with default values`() {
        val properties = UiStateProperties()
        
        assertFalse(properties.isLoading)
        assertNull(properties.error)
    }
    
    @Test
    fun `constructor with parameters sets values correctly`() {
        val isLoading = true
        val error = "Error message"
        
        val properties = UiStateProperties(
            isLoading = isLoading,
            error = error
        )
        
        assertTrue(properties.isLoading)
        assertEquals(error, properties.error)
    }
    
    @Test
    fun `copy() creates a new instance with updated values`() {
        val originalProperties = UiStateProperties(
            isLoading = true,
            error = "Original error"
        )
        
        val newError = "New error"
        val copiedProperties = originalProperties.copy(
            isLoading = false,
            error = newError
        )
        
        // Original properties should remain unchanged
        assertTrue(originalProperties.isLoading)
        assertEquals("Original error", originalProperties.error)
        
        // Copied properties should have the new values
        assertFalse(copiedProperties.isLoading)
        assertEquals(newError, copiedProperties.error)
    }
    
    @Test
    fun `copy() with partial parameters updates only specified values`() {
        val originalProperties = UiStateProperties(
            isLoading = true,
            error = "Original error"
        )
        
        // Only update isLoading
        val copiedProperties1 = originalProperties.copy(
            isLoading = false
        )
        
        assertFalse(copiedProperties1.isLoading)
        assertEquals("Original error", copiedProperties1.error)
        
        // Only update error
        val copiedProperties2 = originalProperties.copy(
            error = null
        )
        
        assertTrue(copiedProperties2.isLoading)
        assertNull(copiedProperties2.error)
    }
}
