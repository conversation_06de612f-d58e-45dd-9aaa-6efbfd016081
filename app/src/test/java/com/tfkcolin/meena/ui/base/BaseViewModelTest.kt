package com.tfkcolin.meena.ui.base

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import kotlin.time.Duration.Companion.seconds

@ExperimentalCoroutinesApi
class BaseViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    @Mock
    private lateinit var errorHandler: ErrorHandler

    @Mock
    private lateinit var context: Context

    private lateinit var viewModel: TestViewModel

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        Dispatchers.setMain(testDispatcher)
        
        `when`(errorHandler.getApplicationContext()).thenReturn(context)
        
        viewModel = TestViewModel(errorHandler)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `clearError sets error to null`() = runTest {
        // Given
        viewModel.setErrorForTest("Error message")
        
        // When
        viewModel.clearError()
        
        // Then
        viewModel.error.test {
            assertNull(awaitItem())
        }
    }

    @Test
    fun `setError sets error message`() = runTest {
        // Given
        val errorMessage = "Error message"
        
        // When
        viewModel.setErrorForTest(errorMessage)
        
        // Then
        viewModel.error.test {
            assertEquals(errorMessage, awaitItem())
        }
    }

    @Test
    fun `setError with throwable uses ErrorHandler to get error message`() = runTest {
        // Given
        val throwable = Exception("Error message")
        val errorMessage = "Formatted error message"
        `when`(errorHandler.getErrorMessage(throwable, null)).thenReturn(errorMessage)
        
        // When
        viewModel.setErrorForTest(throwable)
        
        // Then
        viewModel.error.test {
            assertEquals(errorMessage, awaitItem())
        }
    }

    @Test
    fun `setError with throwable and fallback uses ErrorHandler to get error message`() = runTest {
        // Given
        val throwable = Exception("Error message")
        val fallbackMessage = "Fallback message"
        val errorMessage = "Formatted error message"
        `when`(errorHandler.getErrorMessage(throwable, fallbackMessage)).thenReturn(errorMessage)
        
        // When
        viewModel.setErrorForTest(throwable, fallbackMessage)
        
        // Then
        viewModel.error.test {
            assertEquals(errorMessage, awaitItem())
        }
    }

    @Test
    fun `setLoading sets loading state`() = runTest {
        // Given
        val isLoading = true
        
        // When
        viewModel.setLoadingForTest(isLoading)
        
        // Then
        viewModel.isLoading.test {
            assertTrue(awaitItem())
        }
        
        // When
        viewModel.setLoadingForTest(false)
        
        // Then
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

    @Test
    fun `launchWithErrorHandling handles exceptions and sets error`() = runTest {
        // Given
        val throwable = Exception("Error message")
        val errorMessage = "Formatted error message"
        `when`(errorHandler.getErrorMessage(throwable, null)).thenReturn(errorMessage)
        
        // When
        viewModel.testLaunchWithErrorHandling {
            throw throwable
        }
        
        // Then
        testDispatcher.scheduler.advanceUntilIdle()
        
        viewModel.error.test {
            assertEquals(errorMessage, awaitItem())
        }
        
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

    @Test
    fun `executeUseCase with success calls onSuccess and updates loading state`() = runTest {
        // Given
        val result = Result.success("Success")
        var onSuccessCalled = false
        var onErrorCalled = false
        
        // When
        viewModel.testExecuteUseCase(
            useCase = { result },
            onSuccess = { onSuccessCalled = true },
            onError = { onErrorCalled = true }
        )
        
        // Then
        testDispatcher.scheduler.advanceUntilIdle()
        
        assertTrue(onSuccessCalled)
        assertFalse(onErrorCalled)
        
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

    @Test
    fun `executeUseCase with failure calls onError and updates loading state`() = runTest {
        // Given
        val throwable = Exception("Error message")
        val result = Result.failure<String>(throwable)
        var onSuccessCalled = false
        var onErrorCalled = false
        var errorThrowable: Throwable? = null
        
        // When
        viewModel.testExecuteUseCase(
            useCase = { result },
            onSuccess = { onSuccessCalled = true },
            onError = { 
                onErrorCalled = true
                errorThrowable = it
            }
        )
        
        // Then
        testDispatcher.scheduler.advanceUntilIdle()
        
        assertFalse(onSuccessCalled)
        assertTrue(onErrorCalled)
        assertEquals(throwable, errorThrowable)
        
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

    @Test
    fun `executeUseCase with failure and no onError sets error and updates loading state`() = runTest {
        // Given
        val throwable = Exception("Error message")
        val errorMessage = "Formatted error message"
        val result = Result.failure<String>(throwable)
        `when`(errorHandler.getErrorMessage(throwable, null)).thenReturn(errorMessage)
        var onSuccessCalled = false
        
        // When
        viewModel.testExecuteUseCase(
            useCase = { result },
            onSuccess = { onSuccessCalled = true },
            onError = null
        )
        
        // Then
        testDispatcher.scheduler.advanceUntilIdle()
        
        assertFalse(onSuccessCalled)
        
        viewModel.error.test {
            assertEquals(errorMessage, awaitItem())
        }
        
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

//    @Test
//    fun `executeUseCase with exception calls onError and updates loading state`() = runTest {
//        // Given
//        val throwable = Exception("Error message")
//        var onSuccessCalled = false
//        var onErrorCalled = false
//        var errorThrowable: Throwable? = null
//
//        // When
//        viewModel.testExecuteUseCase(
//            useCase = { throw throwable },
//            onSuccess = { onSuccessCalled = true },
//            onError = {
//                onErrorCalled = true
//                errorThrowable = it
//            }
//        )
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        assertFalse(onSuccessCalled)
//        assertTrue(onErrorCalled)
//        assertEquals(throwable, errorThrowable)
//
//        viewModel.isLoading.test {
//            assertFalse(awaitItem())
//        }
//    }
//
//    @Test
//    fun `executeUseCase with exception and no onError sets error and updates loading state`() = runTest {
//        // Given
//        val throwable = Exception("Error message")
//        val errorMessage = "Formatted error message"
//        `when`(errorHandler.getErrorMessage(throwable, null)).thenReturn(errorMessage)
//        var onSuccessCalled = false
//
//        // When
//        viewModel.testExecuteUseCase(
//            useCase = { throw throwable },
//            onSuccess = { onSuccessCalled = true },
//            onError = null
//        )
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        assertFalse(onSuccessCalled)
//
//        viewModel.error.test {
//            assertEquals(errorMessage, awaitItem())
//        }
//
//        viewModel.isLoading.test {
//            assertFalse(awaitItem())
//        }
//    }

    @Test
    fun `executeUseCase with showLoading false does not update loading state`() = runTest {
        // Given
        val result = Result.success("Success")
        var onSuccessCalled = false
        
        // When
        viewModel.testExecuteUseCase(
            useCase = { result },
            onSuccess = { onSuccessCalled = true },
            onError = null,
            showLoading = false
        )
        
        // Then
        testDispatcher.scheduler.advanceUntilIdle()
        
        assertTrue(onSuccessCalled)
        
        viewModel.isLoading.test {
            assertFalse(awaitItem())
        }
    }

    // Test ViewModel implementation that exposes protected methods for testing
    private class TestViewModel(errorHandler: ErrorHandler) : BaseViewModel(errorHandler) {
        
        fun setErrorForTest(message: String) {
            setError(message)
        }
        
        fun setErrorForTest(throwable: Throwable, fallbackMessage: String? = null) {
            setError(throwable, fallbackMessage)
        }
        
        fun setLoadingForTest(isLoading: Boolean) {
            setLoading(isLoading)
        }
        
        fun testLaunchWithErrorHandling(block: suspend () -> Unit) {
            launchWithErrorHandling { block() }
        }
        
        suspend fun <T> testExecuteUseCase(
            useCase: suspend () -> Result<T>,
            onSuccess: (T) -> Unit,
            onError: ((Throwable) -> Unit)? = null,
            showLoading: Boolean = true
        ) {
            executeUseCase(useCase, onSuccess, onError, showLoading)
        }
    }
}
