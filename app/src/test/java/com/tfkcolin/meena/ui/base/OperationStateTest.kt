package com.tfkcolin.meena.ui.base

import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Test

class OperationStateTest {

    @Test
    fun `default constructor creates idle state`() {
        val state = OperationState()
        
        assertFalse(state.isInProgress)
        assertFalse(state.isSuccessful)
        assertNull(state.error)
    }
    
    @Test
    fun `start() sets isInProgress to true and resets other fields`() {
        val initialState = OperationState(
            isInProgress = false,
            isSuccessful = true,
            error = "Error"
        )
        
        val startedState = initialState.start()
        
        assertTrue(startedState.isInProgress)
        assertFalse(startedState.isSuccessful)
        assertNull(startedState.error)
    }
    
    @Test
    fun `success() sets isSuccessful to true and resets other fields`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = false,
            error = "Error"
        )
        
        val successState = initialState.success()
        
        assertFalse(successState.isInProgress)
        assertTrue(successState.isSuccessful)
        assertNull(successState.error)
    }
    
    @Test
    fun `failure() sets error and resets other fields`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = true,
            error = null
        )
        
        val failureState = initialState.failure("Error message")
        
        assertFalse(failureState.isInProgress)
        assertFalse(failureState.isSuccessful)
        assertEquals("Error message", failureState.error)
    }
    
    @Test
    fun `reset() resets all fields to default values`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = true,
            error = "Error"
        )
        
        val resetState = initialState.reset()
        
        assertFalse(resetState.isInProgress)
        assertFalse(resetState.isSuccessful)
        assertNull(resetState.error)
    }
}
