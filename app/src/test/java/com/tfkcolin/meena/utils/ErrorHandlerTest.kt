package com.tfkcolin.meena.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import com.tfkcolin.meena.R
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

class ErrorHandlerTest {

    @Mock
    private lateinit var context: Context

    @Mock
    private lateinit var connectivityManager: ConnectivityManager

    @Mock
    private lateinit var network: Network

    @Mock
    private lateinit var networkCapabilities: NetworkCapabilities

    private lateinit var errorHandler: ErrorHandler

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // Mock string resources
        `when`(context.getString(R.string.error_no_internet)).thenReturn("No internet connection")
        `when`(context.getString(R.string.error_timeout)).thenReturn("Connection timed out")
        `when`(context.getString(R.string.error_network)).thenReturn("Network error")
        `when`(context.getString(R.string.error_file)).thenReturn("File error")
        `when`(context.getString(R.string.error_database)).thenReturn("Database error")
        `when`(context.getString(R.string.error_websocket)).thenReturn("WebSocket error")
        `when`(context.getString(R.string.error_unknown)).thenReturn("Unknown error")
        
        // Mock connectivity manager
        `when`(context.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(connectivityManager)
        `when`(context.applicationContext).thenReturn(context)
        
        errorHandler = ErrorHandler(context)
    }

    @Test
    fun `getErrorMessage returns correct message for NetworkError with UnknownHostException`() {
        val exception = UnknownHostException("Unknown host")
        val appError = AppError.NetworkError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("No internet connection", message)
    }

    @Test
    fun `getErrorMessage returns correct message for NetworkError with ConnectException`() {
        val exception = ConnectException("Connection refused")
        val appError = AppError.NetworkError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("No internet connection", message)
    }

    @Test
    fun `getErrorMessage returns correct message for NetworkError with SocketTimeoutException`() {
        val exception = SocketTimeoutException("Timeout")
        val appError = AppError.NetworkError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("Connection timed out", message)
    }

    @Test
    fun `getErrorMessage returns correct message for NetworkError with other exception`() {
        val exception = Exception("Network error")
        val appError = AppError.NetworkError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("Network error", message)
    }

    @Test
    fun `getErrorMessage returns correct message for ApiError`() {
        val code = 404
        val errorMessage = "Not found"
        val appError = AppError.ApiError(code, errorMessage)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals(errorMessage, message)
    }

    @Test
    fun `getErrorMessage returns correct message for AuthError`() {
        val errorMessage = "Unauthorized"
        val appError = AppError.AuthError(errorMessage)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals(errorMessage, message)
    }

    @Test
    fun `getErrorMessage returns correct message for ValidationError`() {
        val field = "email"
        val errorMessage = "Invalid email format"
        val appError = AppError.ValidationError(field, errorMessage)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("$field: $errorMessage", message)
    }

    @Test
    fun `getErrorMessage returns correct message for FileError`() {
        val exception = Exception("File not found")
        val appError = AppError.FileError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("File error", message)
    }

    @Test
    fun `getErrorMessage returns correct message for DatabaseError`() {
        val exception = Exception("Database error")
        val appError = AppError.DatabaseError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("Database error", message)
    }

    @Test
    fun `getErrorMessage returns correct message for WebSocketError`() {
        val exception = Exception("WebSocket error")
        val appError = AppError.WebSocketError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("WebSocket error", message)
    }

    @Test
    fun `getErrorMessage returns correct message for UnknownError with exception message`() {
        val errorMessage = "Unknown error occurred"
        val exception = Exception(errorMessage)
        val appError = AppError.UnknownError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals(errorMessage, message)
    }

    @Test
    fun `getErrorMessage returns fallback message for UnknownError without exception message`() {
        val fallbackMessage = "Fallback error message"
        val exception = Exception()
        val appError = AppError.UnknownError(exception)
        
        val message = errorHandler.getErrorMessage(appError, fallbackMessage)
        
        assertEquals(fallbackMessage, message)
    }

    @Test
    fun `getErrorMessage returns default unknown error message when no fallback or exception message`() {
        val exception = Exception()
        val appError = AppError.UnknownError(exception)
        
        val message = errorHandler.getErrorMessage(appError)
        
        assertEquals("Unknown error", message)
    }

    @Test
    fun `getErrorMessage with Throwable converts to AppError and returns correct message`() {
        val exception = UnknownHostException("Unknown host")
        
        val message = errorHandler.getErrorMessage(exception)
        
        assertEquals("No internet connection", message)
    }

    @Test
    fun `isNetworkAvailable returns true when internet is available`() {
        // Mock network availability
        `when`(connectivityManager.activeNetwork).thenReturn(network)
        `when`(connectivityManager.getNetworkCapabilities(network)).thenReturn(networkCapabilities)
        `when`(networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)).thenReturn(true)
        
        val isNetworkAvailable = errorHandler.isNetworkAvailable()
        
        assertTrue(isNetworkAvailable)
    }

    @Test
    fun `isNetworkAvailable returns false when no active network`() {
        // Mock no active network
        `when`(connectivityManager.activeNetwork).thenReturn(null)
        
        val isNetworkAvailable = errorHandler.isNetworkAvailable()
        
        assertFalse(isNetworkAvailable)
    }

    @Test
    fun `isNetworkAvailable returns false when no network capabilities`() {
        // Mock no network capabilities
        `when`(connectivityManager.activeNetwork).thenReturn(network)
        `when`(connectivityManager.getNetworkCapabilities(network)).thenReturn(null)
        
        val isNetworkAvailable = errorHandler.isNetworkAvailable()
        
        assertFalse(isNetworkAvailable)
    }

    @Test
    fun `isNetworkAvailable returns false when no internet capability`() {
        // Mock no internet capability
        `when`(connectivityManager.activeNetwork).thenReturn(network)
        `when`(connectivityManager.getNetworkCapabilities(network)).thenReturn(networkCapabilities)
        `when`(networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)).thenReturn(false)
        
        val isNetworkAvailable = errorHandler.isNetworkAvailable()
        
        assertFalse(isNetworkAvailable)
    }

    @Test
    fun `getErrorMessageFromResult returns null for success result`() {
        val result = Result.success("Success")
        
        val errorMessage = errorHandler.getErrorMessageFromResult(result)
        
        assertNull(errorMessage)
    }

    @Test
    fun `getErrorMessageFromResult returns error message for failure result`() {
        val exception = UnknownHostException("Unknown host")
        val result = Result.failure<String>(exception)
        
        val errorMessage = errorHandler.getErrorMessageFromResult(result)
        
        assertEquals("No internet connection", errorMessage)
    }

    @Test
    fun `getAppErrorFromResult returns null for success result`() {
        val result = Result.success("Success")
        
        val appError = errorHandler.getAppErrorFromResult(result)
        
        assertNull(appError)
    }

    @Test
    fun `getAppErrorFromResult returns AppError for failure result`() {
        val exception = UnknownHostException("Unknown host")
        val result = Result.failure<String>(exception)
        
        val appError = errorHandler.getAppErrorFromResult(result)
        
        assertTrue(appError is AppError.NetworkError)
        assertEquals(exception, (appError as AppError.NetworkError).exception)
    }
}
