package com.tfkcolin.meena.utils

import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

class AppErrorTest {

    @Test
    fun `toAppError converts network exceptions to NetworkError`() {
        val unknownHostException = UnknownHostException("Unknown host")
        val connectException = ConnectException("Connection refused")
        val socketTimeoutException = SocketTimeoutException("Timeout")

        val unknownHostError = unknownHostException.toAppError()
        val connectError = connectException.toAppError()
        val timeoutError = socketTimeoutException.toAppError()

        assertTrue(unknownHostError is AppError.NetworkError)
        assertTrue(connectError is AppError.NetworkError)
        assertTrue(timeoutError is AppError.NetworkError)

        assertEquals(unknownHostException, (unknownHostError as AppError.NetworkError).exception)
        assertEquals(connectException, (connectError as AppError.NetworkError).exception)
        assertEquals(socketTimeoutException, (timeoutError as AppError.NetworkError).exception)
    }

    @Test
    fun `toAppError converts ApiException to ApiError`() {
        val apiException = ApiException("API error", 400)
        val apiError = apiException.toAppError()

        assertTrue(apiError is AppError.ApiError)
        assertEquals(400, (apiError as AppError.ApiError).code)
        assertEquals("API error", apiError.message)
    }

    @Test
    fun `toAppError converts AuthException to AuthError`() {
        val authException = AuthException("Auth error")
        val authError = authException.toAppError()

        assertTrue(authError is AppError.AuthError)
        assertEquals("Auth error", (authError as AppError.AuthError).message)
    }

    @Test
    fun `toAppError converts ValidationException to ValidationError`() {
        val validationException = ValidationException("Validation error", "email")
        val validationError = validationException.toAppError()

        assertTrue(validationError is AppError.ValidationError)
        assertEquals("email", (validationError as AppError.ValidationError).field)
        assertEquals("Validation error", validationError.message)
    }

    @Test
    fun `toAppError converts IOException to FileError`() {
        val ioException = java.io.IOException("IO error")
        val fileError = ioException.toAppError()

        assertTrue(fileError is AppError.FileError)
        assertEquals(ioException, (fileError as AppError.FileError).exception)
    }

    @Test
    fun `toAppError converts SQLException to DatabaseError`() {
        val sqlException = java.sql.SQLException("SQL error")
        val databaseError = sqlException.toAppError()

        assertTrue(databaseError is AppError.DatabaseError)
        assertEquals(sqlException, (databaseError as AppError.DatabaseError).exception)
    }

    @Test
    fun `toAppError converts other exceptions to UnknownError`() {
        val exception = IllegalArgumentException("Illegal argument")
        val unknownError = exception.toAppError()

        assertTrue(unknownError is AppError.UnknownError)
        assertEquals(exception, (unknownError as AppError.UnknownError).exception)
    }

    @Test
    fun `getUserFriendlyMessage returns appropriate message for each error type`() {
        val networkError = AppError.NetworkError(UnknownHostException("Unknown host"))
        val apiError = AppError.ApiError(400, "Bad request")
        val authError = AppError.AuthError("Auth error")
        val validationError = AppError.ValidationError("email", "Invalid email")
        val fileError = AppError.FileError(java.io.IOException("IO error"))
        val databaseError = AppError.DatabaseError(java.sql.SQLException("SQL error"))
        val webSocketError = AppError.WebSocketError(Exception("WebSocket error"))
        val unknownError = AppError.UnknownError(IllegalArgumentException("Illegal argument"))

        assertEquals("Network error: Unknown host", networkError.getUserFriendlyMessage())
        assertEquals("API error (400): Bad request", apiError.getUserFriendlyMessage())
        assertEquals("Authentication error: Auth error", authError.getUserFriendlyMessage())
        assertEquals("Validation error for email: Invalid email", validationError.getUserFriendlyMessage())
        assertEquals("File error: IO error", fileError.getUserFriendlyMessage())
        assertEquals("Database error: SQL error", databaseError.getUserFriendlyMessage())
        assertEquals("WebSocket error: WebSocket error", webSocketError.getUserFriendlyMessage())
        assertEquals("Unknown error: Illegal argument", unknownError.getUserFriendlyMessage())
    }
}
