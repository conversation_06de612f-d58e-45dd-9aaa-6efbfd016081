package com.tfkcolin.meena.data.api

import com.google.gson.Gson
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.data.models.LoginRequest
import com.tfkcolin.meena.data.models.Tokens
import kotlinx.coroutines.runBlocking
import okhttp3.OkHttpClient
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

class ApiClientTest {
    
    private lateinit var mockWebServer: MockWebServer
    private lateinit var authApi: AuthApi
    private lateinit var gson: <PERSON><PERSON>
    
    @Before
    fun setup() {
        // Set up MockWebServer
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        // Create Gson instance
        gson = Gson()
        
        // Create a test OkHttpClient
        val okHttpClient = OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.SECONDS)
            .readTimeout(1, TimeUnit.SECONDS)
            .writeTimeout(1, TimeUnit.SECONDS)
            .build()
        
        // Create Retrofit instance pointing to the mock server
        val retrofit = Retrofit.Builder()
            .baseUrl(mockWebServer.url("/"))
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
        
        // Create API interface
        authApi = retrofit.create(AuthApi::class.java)
    }
    
    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }
    
    @Test
    fun `login with valid credentials returns success`() = runBlocking {
        // Prepare test data
        val loginRequest = LoginRequest(
            identifier = "testuser",
            password = "password123"
        )
        
        val authResponse = AuthResponse(
            userId = "user123",
            userHandle = "testuser",
            tokens = Tokens(
                accessToken = "access_token_123",
                refreshToken = "refresh_token_123"
            ),
            requires2fa = false
        )
        
        // Set up mock response
        val mockResponse = MockResponse()
            .setResponseCode(200)
            .setBody(gson.toJson(authResponse))
        
        mockWebServer.enqueue(mockResponse)
        
        // Execute the API call
        val response = authApi.login(loginRequest)
        
        // Verify the request
        val request = mockWebServer.takeRequest()
        assertEquals("/auth/login", request.path)
        assertEquals("POST", request.method)
        
        // Verify the response
        assertTrue(response.isSuccessful)
        assertEquals(authResponse.userId, response.body()?.userId)
        assertEquals(authResponse.userHandle, response.body()?.userHandle)
        assertEquals(authResponse.tokens.accessToken, response.body()?.tokens?.accessToken)
        assertEquals(authResponse.tokens.refreshToken, response.body()?.tokens?.refreshToken)
        assertEquals(authResponse.requires2fa, response.body()?.requires2fa)
    }
    
    @Test
    fun `login with invalid credentials returns error`() = runBlocking {
        // Prepare test data
        val loginRequest = LoginRequest(
            identifier = "testuser",
            password = "wrongpassword"
        )
        
        // Set up mock response
        val mockResponse = MockResponse()
            .setResponseCode(401)
            .setBody("""{"error":"Unauthorized","message":"Invalid credentials","status":401}""")
        
        mockWebServer.enqueue(mockResponse)
        
        // Execute the API call
        val response = authApi.login(loginRequest)
        
        // Verify the request
        val request = mockWebServer.takeRequest()
        assertEquals("/auth/login", request.path)
        
        // Verify the response
        assertEquals(401, response.code())
        assertEquals(false, response.isSuccessful)
    }
}
