package com.tfkcolin.meena.config

/**
 * Application configuration management.
 * This branch only supports mock backend implementation.
 */
object AppConfig {

    /**
     * Always use mock backend in this branch.
     */
    val useMockBackend: Boolean = true

    /**
     * Base URL for the API - not used in mock mode but kept for compatibility.
     */
    val baseUrl: String = "http://localhost:8080/api/v1"

    /**
     * WebSocket URL for real-time communication - not used in mock mode.
     */
    val webSocketUrl: String = "ws://localhost:8080/ws"

    /**
     * Media upload base URL - not used in mock mode.
     */
    val mediaUploadUrl: String = "http://localhost:8080/api/v1/media"
    
    /**
     * Configuration for mock backend behavior.
     */
    object MockConfig {
        // Simulated network delays (in milliseconds)
        const val NETWORK_DELAY_MIN = 200L
        const val NETWORK_DELAY_MAX = 1000L
        
        // AI response configuration
        const val AI_RESPONSE_DELAY_MIN = 2000L  // Increased minimum delay
        const val AI_RESPONSE_DELAY_MAX = 4000L  // Increased maximum delay
        
        // Mock data persistence
        const val PERSIST_MOCK_DATA = true
        
        // Enable realistic error simulation
        const val SIMULATE_NETWORK_ERRORS = true
        const val ERROR_RATE_PERCENTAGE = 5 // 5% chance of network errors
        
        // Mock user configuration (no default mock users, contacts, or chats)
        const val DEFAULT_MOCK_USERS_COUNT = 0
        const val DEFAULT_MOCK_CHATS_COUNT = 0
        const val DEFAULT_MOCK_MESSAGES_PER_CHAT = 0
        
        // AI personas configuration (no pre-defined AI personas)
        val AI_PERSONAS = emptyList<String>()
        
        // Story simulation
        const val MOCK_STORIES_COUNT = 15
        const val STORY_DURATION_HOURS = 24
        
        // Call simulation
        const val SIMULATE_INCOMING_CALLS = true
        const val CALL_SIMULATION_INTERVAL_MINUTES = 30
    }
    
    /**
     * Development and testing configuration.
     */
    object DevConfig {
        // Enable debug logging for mock backend
        const val ENABLE_MOCK_LOGGING = true
        
        // Enable detailed API request/response logging
        const val ENABLE_API_LOGGING = true
        
        // Enable mock data reset on app start
        const val RESET_MOCK_DATA_ON_START = false
        
        // Enable developer menu in app
        const val ENABLE_DEV_MENU = true
    }
    
    /**
     * Feature flags for controlling app behavior.
     */
    object FeatureFlags {
        // Enable/disable specific features in mock mode
        const val ENABLE_STORIES = true
        const val ENABLE_CALLS = true
        const val ENABLE_GROUP_CHATS = true
        const val ENABLE_CHANNELS = true
        const val ENABLE_MEDIA_MESSAGES = true
        const val ENABLE_MESSAGE_REACTIONS = true
        const val ENABLE_MESSAGE_REPLIES = true
        const val ENABLE_CONTACT_GROUPS = true
        const val ENABLE_PROFILE_VERIFICATION = true
        const val ENABLE_GOLD_SUBSCRIPTION = true
        
        // AI simulation features
        const val ENABLE_AI_CHAT_RESPONSES = true
        const val ENABLE_TYPING_INDICATORS = true
        const val ENABLE_READ_RECEIPTS = true
        const val ENABLE_ONLINE_STATUS = true
    }
}
