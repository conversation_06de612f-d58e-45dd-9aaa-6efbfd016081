package com.tfkcolin.meena.data.models

/**
 * Message deletion model.
 * 
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param deleteType The delete type (self or everyone).
 * @param timestamp The timestamp.
 */
data class MessageDeletion(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val deleteType: String, // "self" or "everyone"
    val timestamp: Long = System.currentTimeMillis()
)
