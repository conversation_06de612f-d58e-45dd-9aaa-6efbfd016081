package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Response from the server with a pre-signed URL for uploading media.
 */
data class UploadUrlResponse(
    @SerializedName("media_id")
    val mediaId: String,
    
    @SerializedName("upload_url")
    val uploadUrl: String,
    
    @SerializedName("required_form_fields")
    val requiredFormFields: Map<String, String>? = null
)
