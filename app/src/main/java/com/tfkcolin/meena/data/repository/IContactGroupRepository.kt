package com.tfkcolin.meena.data.repository

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.models.ContactGroupListResponse
import kotlinx.coroutines.flow.Flow

/**
 * Interface for contact group repository.
 */
interface IContactGroupRepository {

    /**
     * Get all contact groups.
     *
     * @param limit The maximum number of groups to return.
     * @param offset The offset for pagination.
     * @return The contact group list response.
     */
    suspend fun getContactGroups(
        limit: Int = 50,
        offset: Int = 0
    ): ContactGroupListResponse

    /**
     * Get a contact group by ID.
     *
     * @param groupId The ID of the group to get.
     * @return The contact group.
     */
    suspend fun getContactGroupById(groupId: String): ContactGroup

    /**
     * Get all contact groups as a flow.
     *
     * @return A flow of contact groups.
     */
    fun getContactGroupsFlow(): Flow<List<ContactGroup>>

    /**
     * Create a new contact group.
     *
     * @param name The name of the group.
     * @param description The description of the group.
     * @param memberIds The IDs of the initial members.
     * @return The created contact group.
     */
    suspend fun createContactGroup(
        name: String,
        description: String? = null,
        memberIds: List<String> = emptyList()
    ): ContactGroup

    /**
     * Update a contact group.
     *
     * @param groupId The ID of the group to update.
     * @param name The new name of the group.
     * @param description The new description of the group.
     * @return The updated contact group.
     */
    suspend fun updateContactGroup(
        groupId: String,
        name: String,
        description: String? = null
    ): ContactGroup

    /**
     * Delete a contact group.
     *
     * @param groupId The ID of the group to delete.
     */
    suspend fun deleteContactGroup(groupId: String)

    /**
     * Add a contact to a group.
     *
     * @param groupId The ID of the group to add the contact to.
     * @param contactId The ID of the contact to add.
     * @return The updated contact group.
     */
    suspend fun addContactToGroup(
        groupId: String,
        contactId: String
    ): ContactGroup

    /**
     * Remove a contact from a group.
     *
     * @param groupId The ID of the group to remove the contact from.
     * @param contactId The ID of the contact to remove.
     * @return The updated contact group.
     */
    suspend fun removeContactFromGroup(
        groupId: String,
        contactId: String
    ): ContactGroup
}
