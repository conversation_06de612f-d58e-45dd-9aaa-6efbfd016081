package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.Story
import com.tfkcolin.meena.data.models.StoryView
import com.tfkcolin.meena.data.models.StoryHighlight
import retrofit2.Response
import retrofit2.http.*

/**
 * Stories API interface for REST operations.
 */
interface StoriesApi {

    /**
     * Get stories for the current user's feed.
     */
    @GET("/api/v1/stories")
    suspend fun getStories(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<StoriesListResponse>

    /**
     * Get stories by a specific user.
     */
    @GET("/api/v1/users/{user_id}/stories")
    suspend fun getUserStories(
        @Header("Authorization") authToken: String,
        @Path("user_id") userId: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<StoriesListResponse>

    /**
     * Get a story by ID.
     */
    @GET("/api/v1/stories/{story_id}")
    suspend fun getStoryById(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String
    ): Response<StoryResponse>

    /**
     * Create a new story.
     */
    @POST("/api/v1/stories")
    suspend fun createStory(
        @Header("Authorization") authToken: String,
        @Body request: CreateStoryRequest
    ): Response<StoryResponse>

    /**
     * Delete a story.
     */
    @DELETE("/api/v1/stories/{story_id}")
    suspend fun deleteStory(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String
    ): Response<Unit>

    /**
     * View a story.
     */
    @POST("/api/v1/stories/{story_id}/view")
    suspend fun viewStory(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String,
        @Body request: ViewStoryRequest
    ): Response<StoryViewResponse>

    /**
     * Get story views.
     */
    @GET("/api/v1/stories/{story_id}/views")
    suspend fun getStoryViews(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<StoryViewsListResponse>

    /**
     * React to a story.
     */
    @POST("/api/v1/stories/{story_id}/react")
    suspend fun reactToStory(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String,
        @Body request: ReactToStoryRequest
    ): Response<Unit>

    /**
     * Reply to a story.
     */
    @POST("/api/v1/stories/{story_id}/reply")
    suspend fun replyToStory(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String,
        @Body request: ReplyToStoryRequest
    ): Response<Unit>

    /**
     * Get story highlights.
     */
    @GET("/api/v1/users/{user_id}/highlights")
    suspend fun getStoryHighlights(
        @Header("Authorization") authToken: String,
        @Path("user_id") userId: String
    ): Response<StoryHighlightsListResponse>

    /**
     * Create a story highlight.
     */
    @POST("/api/v1/highlights")
    suspend fun createStoryHighlight(
        @Header("Authorization") authToken: String,
        @Body request: CreateStoryHighlightRequest
    ): Response<StoryHighlightResponse>

    /**
     * Update a story highlight.
     */
    @PUT("/api/v1/highlights/{highlight_id}")
    suspend fun updateStoryHighlight(
        @Header("Authorization") authToken: String,
        @Path("highlight_id") highlightId: String,
        @Body request: UpdateStoryHighlightRequest
    ): Response<StoryHighlightResponse>

    /**
     * Delete a story highlight.
     */
    @DELETE("/api/v1/highlights/{highlight_id}")
    suspend fun deleteStoryHighlight(
        @Header("Authorization") authToken: String,
        @Path("highlight_id") highlightId: String
    ): Response<Unit>

    /**
     * Update story privacy.
     */
    @PUT("/api/v1/stories/{story_id}/privacy")
    suspend fun updateStoryPrivacy(
        @Header("Authorization") authToken: String,
        @Path("story_id") storyId: String,
        @Body request: UpdateStoryPrivacyRequest
    ): Response<Unit>

    /**
     * Get story analytics.
     */
    @GET("/api/v1/stories/analytics")
    suspend fun getStoryAnalytics(
        @Header("Authorization") authToken: String,
        @Query("story_id") storyId: String? = null
    ): Response<StoryAnalyticsResponse>

    /**
     * Search stories.
     */
    @GET("/api/v1/stories/search")
    suspend fun searchStories(
        @Header("Authorization") authToken: String,
        @Query("q") query: String,
        @Query("limit") limit: Int = 20,
        @Query("offset") offset: Int = 0
    ): Response<StoriesListResponse>
}

/**
 * Stories list response.
 */
data class StoriesListResponse(
    val stories: List<Story>,
    val total_count: Int,
    val has_more: Boolean = false
)

/**
 * Story response.
 */
data class StoryResponse(
    val story: Story
)

/**
 * Story view response.
 */
data class StoryViewResponse(
    val view: StoryView
)

/**
 * Story views list response.
 */
data class StoryViewsListResponse(
    val views: List<StoryView>,
    val total_count: Int
)

/**
 * Story highlights list response.
 */
data class StoryHighlightsListResponse(
    val highlights: List<StoryHighlight>,
    val total_count: Int
)

/**
 * Story highlight response.
 */
data class StoryHighlightResponse(
    val highlight: StoryHighlight
)

/**
 * Story analytics response.
 */
data class StoryAnalyticsResponse(
    val analytics: Map<String, Any>
)

/**
 * Create story request.
 */
data class CreateStoryRequest(
    val media_url: String,
    val media_type: String,
    val caption: String? = null,
    val privacy_type: String = "contacts",
    val allowed_viewer_ids: List<String> = emptyList(),
    val background_color: String? = null,
    val text_color: String? = null
)

/**
 * View story request.
 */
data class ViewStoryRequest(
    val view_duration: Long = 0
)

/**
 * React to story request.
 */
data class ReactToStoryRequest(
    val reaction_type: String
)

/**
 * Reply to story request.
 */
data class ReplyToStoryRequest(
    val content: String
)

/**
 * Create story highlight request.
 */
data class CreateStoryHighlightRequest(
    val title: String,
    val story_ids: List<String>,
    val cover_url: String? = null
)

/**
 * Update story highlight request.
 */
data class UpdateStoryHighlightRequest(
    val title: String? = null,
    val story_ids: List<String>? = null,
    val cover_url: String? = null
)

/**
 * Update story privacy request.
 */
data class UpdateStoryPrivacyRequest(
    val privacy_type: String,
    val allowed_viewer_ids: List<String> = emptyList()
)
