package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Additional models needed for mock API compatibility.
 * These extend or replace existing models to match the mock API contracts.
 */

/**
 * Enhanced ChatListResponse for mock API.
 */
data class ChatListResponse(
    @SerializedName("chats")
    val chats: List<ChatResponse>,

    @SerializedName("total_count")
    val totalCount: Int,

    @SerializedName("has_more")
    val hasMore: Boolean = false
)

/**
 * Enhanced ChatResponse for mock API.
 */
data class ChatResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("conversation_type")
    val conversationType: String,

    @SerializedName("privacy_type")
    val privacyType: String? = null,

    @SerializedName("participant_ids")
    val participantIds: String,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("avatar_url")
    val avatarUrl: String? = null,

    @SerializedName("admin_ids")
    val adminIds: String? = null,

    @SerializedName("created_by")
    val createdBy: String? = null,

    @SerializedName("last_message")
    val lastMessage: String? = null,

    @SerializedName("last_message_timestamp")
    val lastMessageTimestamp: Long? = null,

    @SerializedName("unread_count")
    val unreadCount: Int = 0,

    @SerializedName("is_archived")
    val isArchived: Boolean = false,

    @SerializedName("is_muted")
    val isMuted: Boolean = false,

    @SerializedName("is_pinned")
    val isPinned: Boolean = false,

    @SerializedName("muted_until")
    val mutedUntil: Long? = null,

    @SerializedName("created_at")
    val createdAt: Long,

    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false,

    @SerializedName("last_message_sender_id")
    val lastMessageSenderId: String? = null,

    @SerializedName("last_message_type")
    val lastMessageType: String? = null
)

/**
 * Enhanced MessageListResponse for mock API.
 */
data class MessageListResponse(
    @SerializedName("messages")
    val messages: List<MessageResponse>,

    @SerializedName("has_more")
    val hasMore: Boolean = false
)

/**
 * Enhanced MessageResponse for mock API.
 */
data class MessageResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("chat_id")
    val chatId: String,

    @SerializedName("sender_id")
    val senderId: String,

    @SerializedName("content")
    val content: String?,

    @SerializedName("message_type")
    val messageType: String = "text",

    @SerializedName("timestamp")
    val timestamp: Long,

    @SerializedName("is_edited")
    val isEdited: Boolean = false,

    @SerializedName("edited_at")
    val editedAt: Long? = null,

    @SerializedName("reply_to_message_id")
    val replyToMessageId: String? = null,

    @SerializedName("is_deleted")
    val isDeleted: Boolean = false,

    @SerializedName("deleted_at")
    val deletedAt: Long? = null,

    @SerializedName("delivery_status")
    val deliveryStatus: String = "sent",

    @SerializedName("read_by")
    val readBy: List<String> = emptyList(),

    @SerializedName("reactions")
    val reactions: Map<String, List<String>> = emptyMap(),

    @SerializedName("media_attachments")
    val mediaAttachments: List<MediaAttachment> = emptyList(),

    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false,

    @SerializedName("encryption_key_id")
    val encryptionKeyId: String? = null
)

/**
 * Enhanced SendMessageRequest for mock API.
 */
data class SendMessageRequest(
    @SerializedName("content")
    val content: String?,

    @SerializedName("message_type")
    val messageType: String? = "text",

    @SerializedName("reply_to_message_id")
    val replyToMessageId: String? = null,

    @SerializedName("media_attachments")
    val mediaAttachments: List<MediaAttachment>? = null
)

/**
 * Enhanced EditMessageRequest for mock API.
 */
data class EditMessageRequest(
    @SerializedName("content")
    val content: String
)

/**
 * MarkAsReadRequest for mock API.
 */
data class MarkAsReadRequest(
    @SerializedName("last_read_message_id")
    val lastReadMessageId: String? = null
)






