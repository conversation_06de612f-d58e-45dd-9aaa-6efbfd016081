package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Request for encrypted media upload.
 *
 * @property fileName The name of the encrypted file.
 * @property contentType The content type of the encrypted file.
 * @property fileSize The size of the encrypted file in bytes.
 * @property isEncrypted Whether the file is encrypted.
 */
data class EncryptedMediaUploadRequest(
    @SerializedName("file_name")
    val fileName: String,
    
    @SerializedName("content_type")
    val contentType: String,
    
    @SerializedName("file_size")
    val fileSize: Long,
    
    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = true
)

/**
 * Response for encrypted media upload.
 *
 * @property mediaId The ID of the media item.
 * @property uploadUrl The pre-signed URL for uploading the encrypted file.
 * @property expiresAt When the upload URL expires.
 */
data class EncryptedMediaUploadResponse(
    @SerializedName("media_id")
    val mediaId: String,
    
    @SerializedName("upload_url")
    val uploadUrl: String,
    
    @SerializedName("expires_at")
    val expiresAt: String
)

/**
 * Request for chunked upload initialization.
 *
 * @property fileName The name of the file.
 * @property contentType The content type of the file.
 * @property totalSize The total size of the file in bytes.
 * @property chunkSize The size of each chunk in bytes.
 * @property isEncrypted Whether the file is encrypted.
 */
data class ChunkedUploadInitRequest(
    @SerializedName("file_name")
    val fileName: String,
    
    @SerializedName("content_type")
    val contentType: String,
    
    @SerializedName("total_size")
    val totalSize: Long,
    
    @SerializedName("chunk_size")
    val chunkSize: Int,
    
    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false
)

/**
 * Response for chunked upload initialization.
 *
 * @property uploadId The ID of the upload session.
 * @property expiresAt When the upload session expires.
 */
data class ChunkedUploadInitResponse(
    @SerializedName("upload_id")
    val uploadId: String,
    
    @SerializedName("expires_at")
    val expiresAt: String
)

/**
 * Response for chunk upload.
 *
 * @property chunkIndex The index of the uploaded chunk.
 * @property receivedSize The size of the uploaded chunk in bytes.
 * @property totalReceived The total size of all uploaded chunks in bytes.
 */
data class ChunkUploadResponse(
    @SerializedName("chunk_index")
    val chunkIndex: Int,
    
    @SerializedName("received_size")
    val receivedSize: Long,
    
    @SerializedName("total_received")
    val totalReceived: Long
)

/**
 * Response for chunked upload status.
 *
 * @property uploadId The ID of the upload session.
 * @property fileName The name of the file.
 * @property contentType The content type of the file.
 * @property totalSize The total size of the file in bytes.
 * @property uploadedChunks The indices of the chunks that have been uploaded.
 * @property totalReceived The total size of all uploaded chunks in bytes.
 * @property expiresAt When the upload session expires.
 */
data class ChunkedUploadStatusResponse(
    @SerializedName("upload_id")
    val uploadId: String,
    
    @SerializedName("file_name")
    val fileName: String,
    
    @SerializedName("content_type")
    val contentType: String,
    
    @SerializedName("total_size")
    val totalSize: Long,
    
    @SerializedName("uploaded_chunks")
    val uploadedChunks: List<Int>,
    
    @SerializedName("total_received")
    val totalReceived: Long,
    
    @SerializedName("expires_at")
    val expiresAt: String
)
