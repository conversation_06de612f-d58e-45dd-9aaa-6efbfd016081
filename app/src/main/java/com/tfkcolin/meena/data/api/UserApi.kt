package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.data.models.UpdateProfileRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PUT
import retrofit2.http.Path

/**
 * API interface for user endpoints.
 */
interface UserApi {

    /**
     * Get a user by ID.
     *
     * @param userId The user ID.
     * @return The user.
     */
    @GET("/api/v1/users/{userId}")
    suspend fun getUserById(@Path("userId") userId: String): Response<User>

    /**
     * Get a user by handle.
     *
     * @param userHandle The user handle.
     * @return The user.
     */
    @GET("/api/v1/users/handle/{userHandle}")
    suspend fun getUserByHandle(@Path("userHandle") userHandle: String): Response<User>

    /**
     * Get the current user's profile.
     *
     * @param authToken The authentication token.
     * @return The user profile.
     */
    @GET("/api/v1/users/me")
    suspend fun getCurrentUserProfile(
        @Header("Authorization") authToken: String
    ): Response<User>

    /**
     * Update the current user's profile.
     *
     * @param authToken The authentication token.
     * @param request The update profile request.
     * @return The updated user profile.
     */
    @PUT("/api/v1/users/me")
    suspend fun updateProfile(
        @Header("Authorization") authToken: String,
        @Body request: UpdateProfileRequest
    ): Response<User>
}
