package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Contact list response model for the /contacts endpoint.
 */
data class ContactListResponse(
    @SerializedName("contacts")
    val contacts: List<Contact>,

    @SerializedName("total_count")
    val totalCount: Int
)

/**
 * Contact response model for a single contact.
 * This is used for API responses that don't return the full Contact model.
 */
data class ContactResponse(
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("contact_id")
    val contactId: String,

    @SerializedName("display_name")
    val displayName: String?,

    @SerializedName("relationship")
    val relationship: String,

    @SerializedName("notes")
    val notes: String?,

    @SerializedName("created_at")
    val createdAt: String,

    @SerializedName("updated_at")
    val updatedAt: String,

    @SerializedName("user")
    val user: UserProfile?
) {
    /**
     * Convert this ContactResponse to a Contact model.
     *
     * @return The Contact model.
     */
    fun toContact(): Contact {
        return Contact(
            id = id,
            userId = userId,
            contactId = contactId,
            displayName = displayName ?: user?.displayName,
            relationship = relationship,
            notes = null,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isFavorite = false,
            lastInteractionAt = null,
            user = user
        )
    }
}



/**
 * Response model for contact search.
 */
data class ContactSearchResponse(
    @SerializedName("results")
    val results: List<ContactSearchResult>,

    @SerializedName("total_count")
    val totalCount: Int
)

/**
 * Individual contact search result.
 */
data class ContactSearchResult(
    @SerializedName("user_id")
    val userId: String,

    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String,

    @SerializedName("avatar_url")
    val avatarUrl: String?,

    @SerializedName("bio")
    val bio: String?,

    @SerializedName("is_verified")
    val isVerified: Boolean,

    @SerializedName("mutual_contacts_count")
    val mutualContactsCount: Int,

    @SerializedName("is_contact")
    val isContact: Boolean
)
