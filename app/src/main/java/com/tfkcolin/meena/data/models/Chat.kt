package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Chat model representing a conversation (one-to-one, group, or channel).
 * Follows the API contract for conversations.
 */
@Entity(
    tableName = "chats",
    indices = [
        Index("participantIds")
    ]
)
data class Chat(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("conversation_type")
    val conversationType: String, // "one_to_one", "group", or "channel"

    @SerializedName("privacy_type")
    val privacyType: String? = null, // "public", "private", or "secret" (for groups/channels)

    @SerializedName("participant_ids")
    val participantIds: String, // Comma-separated list of participant IDs

    @SerializedName("conversation_name")
    val name: String? = null, // Group/channel name (null for one-to-one chats)

    @SerializedName("description")
    val description: String? = null, // Group/channel description

    @SerializedName("conversation_avatar_url")
    val avatarUrl: String? = null, // Group/channel avatar URL

    @SerializedName("admin_ids")
    val adminIds: String? = null, // Comma-separated list of admin user IDs

    @SerializedName("created_by")
    val createdBy: String? = null, // User ID of the creator

    @SerializedName("last_message")
    val lastMessage: String? = null,

    @SerializedName("last_activity_timestamp")
    val lastMessageTimestamp: Long? = null,

    @SerializedName("unread_count")
    val unreadCount: Int = 0,

    @SerializedName("is_archived")
    val isArchived: Boolean = false,

    @SerializedName("is_muted")
    val isMuted: Boolean = false,

    @SerializedName("is_pinned")
    val isPinned: Boolean = false,

    @SerializedName("muted_until")
    val mutedUntil: Long? = null,

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @SerializedName("is_encrypted")
    val isEncrypted: Boolean = false,

    @SerializedName("last_message_sender_id")
    val lastMessageSenderId: String? = null,

    @SerializedName("last_message_type")
    val lastMessageType: String? = null
) {
    /**
     * Get the list of participant IDs.
     *
     * @return The list of participant IDs.
     */
    fun getParticipantIdsList(): List<String> {
        return participantIds.split(",").filter { it.isNotBlank() }
    }

    /**
     * Get the list of admin IDs.
     *
     * @return The list of admin IDs, or an empty list if there are no admins.
     */
    fun getAdminIdsList(): List<String> {
        return adminIds?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    }

    /**
     * Check if a user is an admin of this chat.
     *
     * @param userId The user ID to check.
     * @return True if the user is an admin, false otherwise.
     */
    fun isUserAdmin(userId: String): Boolean {
        return getAdminIdsList().contains(userId)
    }

    /**
     * Get the conversation type as an enum.
     *
     * @return The conversation type enum.
     */
    fun getConversationType(): ConversationType {
        return ConversationType.fromString(conversationType)
    }

    /**
     * Get the privacy type as an enum.
     *
     * @return The privacy type enum, or null if the privacy type is not set.
     */
    fun getPrivacyType(): PrivacyType? {
        return privacyType?.let { PrivacyType.fromString(it) }
    }

    /**
     * Check if this is a group chat.
     *
     * @return True if this is a group chat, false otherwise.
     */
    fun isGroup(): Boolean {
        return getConversationType() == ConversationType.GROUP
    }

    /**
     * Check if this is a channel.
     *
     * @return True if this is a channel, false otherwise.
     */
    fun isChannel(): Boolean {
        return getConversationType() == ConversationType.CHANNEL
    }

    /**
     * Check if this is a one-to-one chat.
     *
     * @return True if this is a one-to-one chat, false otherwise.
     */
    fun isOneToOne(): Boolean {
        return getConversationType() == ConversationType.ONE_TO_ONE
    }

    /**
     * Check if this is a public conversation.
     *
     * @return True if this is a public conversation, false otherwise.
     */
    fun isPublic(): Boolean {
        return getPrivacyType() == PrivacyType.PUBLIC
    }

    /**
     * Check if this is a private conversation.
     *
     * @return True if this is a private conversation, false otherwise.
     */
    fun isPrivate(): Boolean {
        return getPrivacyType() == PrivacyType.PRIVATE
    }

    /**
     * Check if this is a secret conversation.
     *
     * @return True if this is a secret conversation, false otherwise.
     */
    fun isSecret(): Boolean {
        return getPrivacyType() == PrivacyType.SECRET
    }
}
