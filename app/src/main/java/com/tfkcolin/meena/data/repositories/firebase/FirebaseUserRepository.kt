package com.tfkcolin.meena.data.repositories.firebase

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.data.models.firebase.FirebaseUserProfile
import com.tfkcolin.meena.domain.repositories.IUserRepository
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IUserRepository.
 * Uses Firestore for user data storage and real-time updates.
 */
@Singleton
class FirebaseUserRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth,
    private val firestore: FirebaseFirestore,
    private val userDao: UserDao,
    private val tokenManager: TokenManager
) : IUserRepository {

    companion object {
        const val USERS_COLLECTION = "users"
    }

    override suspend fun getUserById(userId: String): Result<User> {
        return try {
            // First try to get from local database
            val localUser = userDao.getUserById(userId)
            if (localUser != null) {
                return Result.success(localUser)
            }

            // If not found locally, fetch from Firestore
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(userId)
                .get()
                .await()

            if (!userDoc.exists()) {
                throw Exception("User not found")
            }

            val firebaseUser = userDoc.toObject(FirebaseUserProfile::class.java)
                ?: throw Exception("Failed to parse user profile")

            // Convert to local User model and save to database
            val user = firebaseUser.toUser()
            userDao.insertUser(user)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getUserByHandle(userHandle: String): Result<User> {
        return try {
            // First try to get from local database
            val localUser = userDao.getUserByHandle(userHandle)
            if (localUser != null) {
                return Result.success(localUser)
            }

            // If not found locally, fetch from Firestore
            val querySnapshot = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("userHandle", userHandle)
                .limit(1)
                .get()
                .await()

            if (querySnapshot.isEmpty) {
                throw Exception("User not found")
            }

            val firebaseUser = querySnapshot.documents.first()
                .toObject(FirebaseUserProfile::class.java)
                ?: throw Exception("Failed to parse user profile")

            // Convert to local User model and save to database
            val user = firebaseUser.toUser()
            userDao.insertUser(user)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null && snapshot.exists()) {
                        val firebaseUser = snapshot.toObject(FirebaseUserProfile::class.java)
                        val user = firebaseUser?.toUser()

                        // Update local database in a coroutine
                        if (user != null) {
                            // Launch a coroutine to handle the suspend function
                            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                                try {
                                    userDao.insertUser(user)
                                } catch (e: Exception) {
                                    // Log error but don't fail the flow
                                    e.printStackTrace()
                                }
                            }
                        }

                        trySend(user)
                    } else {
                        trySend(null)
                    }
                }
        } else {
            trySend(null)
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun updateProfile(
        displayName: String?,
        bio: String?,
        avatarUrl: String?
    ): Result<User> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            // Prepare update data
            val updates = mutableMapOf<String, Any>()
            displayName?.let { updates["displayName"] = it }
            bio?.let { updates["bio"] = it }
            avatarUrl?.let { updates["avatarUrl"] = it }
            updates["lastActive"] = System.currentTimeMillis()

            // Update in Firestore
            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update(updates)
                .await()

            // Get updated user
            val updatedUserDoc = firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .get()
                .await()

            val firebaseUser = updatedUserDoc.toObject(FirebaseUserProfile::class.java)
                ?: throw Exception("Failed to parse updated user profile")

            // Convert to local User model and update database
            val user = firebaseUser.toUser()
            userDao.updateUser(user)

            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user's online presence.
     */
    suspend fun updatePresence(isOnline: Boolean): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val updates = mapOf(
                "lastActive" to System.currentTimeMillis()
            )

            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update(updates)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Search users by display name or user handle.
     */
    suspend fun searchUsers(query: String, limit: Int = 20): Result<List<User>> {
        return try {
            if (query.isBlank()) {
                return Result.success(emptyList())
            }

            // Search by user handle (exact match)
            val handleQuery = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("userHandle", query)
                .limit(limit.toLong())
                .get()
                .await()

            // Search by display name (starts with query)
            val nameQuery = firestore.collection(USERS_COLLECTION)
                .whereGreaterThanOrEqualTo("displayName", query)
                .whereLessThanOrEqualTo("displayName", query + "\uf8ff")
                .limit(limit.toLong())
                .get()
                .await()

            // Combine results and remove duplicates
            val allDocs = (handleQuery.documents + nameQuery.documents).distinctBy { it.id }
            
            val users = allDocs.mapNotNull { doc ->
                doc.toObject(FirebaseUserProfile::class.java)?.toUser()
            }

            // Update local database
            users.forEach { user ->
                try {
                    userDao.insertUser(user)
                } catch (e: Exception) {
                    // Log error but don't fail the operation
                    e.printStackTrace()
                }
            }

            Result.success(users)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * Extension function to convert FirebaseUserProfile to User.
 */
private fun FirebaseUserProfile.toUser(): User {
    return User(
        userId = this.userId,
        userHandle = this.userHandle,
        displayName = this.displayName,
        profilePictureUrl = this.avatarUrl,
        bio = this.bio,
        phoneNumber = this.phoneNumber,
        email = this.email,
        subscriptionTier = this.subscriptionTier,
        verificationStatus = this.verificationStatus,
        isActive = this.isActive,
        createdAt = this.createdAt.toString(),
        lastSeenAt = this.lastActive.toString()
    )
}
