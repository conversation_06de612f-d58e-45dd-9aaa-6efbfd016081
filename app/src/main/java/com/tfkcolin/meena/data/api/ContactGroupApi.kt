package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.models.ContactGroupListResponse
import com.tfkcolin.meena.data.models.CreateContactGroupRequest
import com.tfkcolin.meena.data.models.UpdateContactGroupRequest
import retrofit2.Response
import retrofit2.http.*

/**
 * API interface for contact group endpoints.
 */
interface ContactGroupApi {

    /**
     * Get the user's contact groups.
     *
     * @param authToken The authentication token.
     * @param limit The maximum number of groups to return.
     * @param offset The offset for pagination.
     * @return The contact group list response.
     */
    @GET("/api/v1/contact-groups")
    suspend fun getContactGroups(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<ContactGroupListResponse>

    /**
     * Get a contact group by ID.
     *
     * @param authToken The authentication token.
     * @param groupId The ID of the group to get.
     * @return The contact group.
     */
    @GET("/api/v1/contact-groups/{groupId}")
    suspend fun getContactGroupById(
        @Header("Authorization") authToken: String,
        @Path("groupId") groupId: String
    ): Response<ContactGroup>

    /**
     * Create a new contact group.
     *
     * @param authToken The authentication token.
     * @param request The create contact group request.
     * @return The created contact group.
     */
    @POST("/api/v1/contact-groups")
    suspend fun createContactGroup(
        @Header("Authorization") authToken: String,
        @Body request: CreateContactGroupRequest
    ): Response<ContactGroup>

    /**
     * Update a contact group.
     *
     * @param authToken The authentication token.
     * @param groupId The ID of the group to update.
     * @param request The update contact group request.
     * @return The updated contact group.
     */
    @PUT("/api/v1/contact-groups/{groupId}")
    suspend fun updateContactGroup(
        @Header("Authorization") authToken: String,
        @Path("groupId") groupId: String,
        @Body request: UpdateContactGroupRequest
    ): Response<ContactGroup>

    /**
     * Delete a contact group.
     *
     * @param authToken The authentication token.
     * @param groupId The ID of the group to delete.
     * @return An empty response.
     */
    @DELETE("/api/v1/contact-groups/{groupId}")
    suspend fun deleteContactGroup(
        @Header("Authorization") authToken: String,
        @Path("groupId") groupId: String
    ): Response<Unit>

    /**
     * Add a contact to a group.
     *
     * @param authToken The authentication token.
     * @param groupId The ID of the group to add the contact to.
     * @param contactId The ID of the contact to add.
     * @return The updated contact group.
     */
    @POST("/api/v1/contact-groups/{groupId}/members/{contactId}")
    suspend fun addContactToGroup(
        @Header("Authorization") authToken: String,
        @Path("groupId") groupId: String,
        @Path("contactId") contactId: String
    ): Response<ContactGroup>

    /**
     * Remove a contact from a group.
     *
     * @param authToken The authentication token.
     * @param groupId The ID of the group to remove the contact from.
     * @param contactId The ID of the contact to remove.
     * @return The updated contact group.
     */
    @DELETE("/api/v1/contact-groups/{groupId}/members/{contactId}")
    suspend fun removeContactFromGroup(
        @Header("Authorization") authToken: String,
        @Path("groupId") groupId: String,
        @Path("contactId") contactId: String
    ): Response<ContactGroup>
}
