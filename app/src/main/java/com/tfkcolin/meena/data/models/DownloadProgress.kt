package com.tfkcolin.meena.data.models

/**
 * Represents the progress of a media download.
 */
sealed class DownloadProgress {
    /**
     * The ID of the download.
     */
    abstract val id: String

    /**
     * Whether the download is complete.
     */
    abstract val isComplete: Boolean

    /**
     * The download is being prepared.
     *
     * @param id The ID of the download.
     */
    data class Preparing(
        override val id: String
    ) : DownloadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The download is in progress.
     *
     * @param id The ID of the download.
     * @param progress The download progress as a percentage (0-100).
     */
    data class Downloading(
        override val id: String,
        val progress: Int
    ) : DownloadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The download is complete.
     *
     * @param id The ID of the download.
     */
    data class Complete(
        override val id: String
    ) : DownloadProgress() {
        override val isComplete: Boolean = true
    }

    /**
     * The download failed.
     *
     * @param id The ID of the download.
     * @param error The error message.
     */
    data class Error(
        override val id: String,
        val error: String
    ) : DownloadProgress() {
        override val isComplete: Boolean = true
    }

    companion object {
        /**
         * Create a DownloadProgress object from a progress value.
         *
         * @param id The ID of the download.
         * @param progress The progress of the download (0-100).
         * @param isComplete Whether the download is complete.
         * @param error The error message, if any.
         * @return The appropriate DownloadProgress object.
         */
        fun create(id: String, progress: Int, isComplete: Boolean, error: String? = null): DownloadProgress {
            return when {
                error != null -> Error(id, error)
                isComplete -> Complete(id)
                progress == 0 -> Preparing(id)
                else -> Downloading(id, progress)
            }
        }
    }
}
