package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Message reaction model representing a reaction to a message.
 */
@Entity(
    tableName = "message_reactions",
    foreignKeys = [
        ForeignKey(
            entity = Message::class,
            parentColumns = ["id"],
            childColumns = ["messageId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("messageId"),
        Index("userId")
    ]
)
data class MessageReaction(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("message_id")
    val messageId: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("emoji")
    val emoji: String,

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Message reaction request model for adding or removing a reaction.
 */
data class MessageReactionRequest(
    @SerializedName("message_id")
    val messageId: String,

    @SerializedName("emoji")
    val emoji: String
)

/**
 * Message reaction summary model for displaying reactions in the UI.
 */
data class MessageReactionSummary(
    @SerializedName("emoji")
    val emoji: String,

    @SerializedName("count")
    val count: Int,

    @SerializedName("user_ids")
    val userIds: List<String>
) {
    /**
     * Check if a user has reacted with this emoji.
     *
     * @param userId The user ID to check.
     * @return True if the user has reacted with this emoji, false otherwise.
     */
    fun hasUserReacted(userId: String): Boolean {
        return userIds.contains(userId)
    }
}
