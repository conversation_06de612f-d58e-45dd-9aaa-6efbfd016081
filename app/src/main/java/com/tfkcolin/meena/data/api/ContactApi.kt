package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.*
import retrofit2.Response
import retrofit2.http.*

/**
 * API interface for contact endpoints.
 */
interface ContactApi {

    /**
     * Get the user's contact list.
     *
     * @param authToken The authentication token.
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @param relationship Optional filter for relationship type.
     * @return The contact list response.
     */
    @GET("/api/v1/contacts")
    suspend fun getContacts(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0,
        @Query("relationship") relationship: String? = null
    ): Response<ContactListResponse>

    /**
     * Get the user's favorite contacts.
     *
     * @param authToken The authentication token.
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The contact list response.
     */
    @GET("/api/v1/contacts/favorites")
    suspend fun getFavoriteContacts(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<ContactListResponse>

    /**
     * Add a contact to the user's contact list.
     *
     * @param authToken The authentication token.
     * @param request The add contact request.
     * @return The contact response.
     */
    @POST("/api/v1/contacts")
    suspend fun addContact(
        @Header("Authorization") authToken: String,
        @Body request: AddContactRequest
    ): Response<ContactResponse>

    /**
     * Get a contact by ID.
     *
     * @param authToken The authentication token.
     * @param contactId The contact ID.
     * @return The contact response.
     */
    @GET("/api/v1/contacts/{contact_id}")
    suspend fun getContactById(
        @Header("Authorization") authToken: String,
        @Path("contact_id") contactId: String
    ): Response<ContactResponse>

    /**
     * Update a contact in the user's contact list.
     *
     * @param authToken The authentication token.
     * @param contactId The contact ID.
     * @param request The update contact request.
     * @return The contact response.
     */
    @PATCH("/api/v1/contacts/{contact_id}")
    suspend fun updateContact(
        @Header("Authorization") authToken: String,
        @Path("contact_id") contactId: String,
        @Body request: UpdateContactRequest
    ): Response<ContactResponse>

    /**
     * Delete a contact.
     *
     * @param authToken The authentication token.
     * @param contactId The contact ID.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/contacts/{contact_id}")
    suspend fun deleteContact(
        @Header("Authorization") authToken: String,
        @Path("contact_id") contactId: String
    ): Response<Unit>

    /**
     * Search contacts.
     *
     * @param authToken The authentication token.
     * @param query The search query.
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The contact list response.
     */
    @GET("/api/v1/contacts/search")
    suspend fun searchContacts(
        @Header("Authorization") authToken: String,
        @Query("query") query: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<ContactListResponse>

    /**
     * Get recent contacts.
     *
     * @param authToken The authentication token.
     * @param limit The maximum number of contacts to return.
     * @return The contact list response.
     */
    @GET("/api/v1/contacts/recent")
    suspend fun getRecentContacts(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 10
    ): Response<ContactListResponse>

    /**
     * Add a contact to favorites.
     *
     * @param authToken The authentication token.
     * @param contactId The contact ID.
     * @return A response indicating success or failure.
     */
    @POST("/api/v1/contacts/{contact_id}/favorite")
    suspend fun addToFavorites(
        @Header("Authorization") authToken: String,
        @Path("contact_id") contactId: String
    ): Response<Unit>

    /**
     * Remove a contact from favorites.
     *
     * @param authToken The authentication token.
     * @param contactId The contact ID.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/contacts/{contact_id}/favorite")
    suspend fun removeFromFavorites(
        @Header("Authorization") authToken: String,
        @Path("contact_id") contactId: String
    ): Response<Unit>
}
