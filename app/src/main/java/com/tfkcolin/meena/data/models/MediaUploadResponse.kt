package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Response for media upload.
 */
data class MediaUploadResponse(
    @SerializedName("media_id")
    val mediaId: String,

    @SerializedName("url")
    val url: String,

    @SerializedName("thumbnail_url")
    val thumbnailUrl: String? = null,

    @SerializedName("file_name")
    val fileName: String,

    @SerializedName("file_size")
    val fileSize: Long,

    @SerializedName("mime_type")
    val mimeType: String,

    @SerializedName("width")
    val width: Int? = null,

    @SerializedName("height")
    val height: Int? = null,

    @SerializedName("duration")
    val duration: Long? = null,

    @SerializedName("uploaded_at")
    val uploadedAt: Long
)
