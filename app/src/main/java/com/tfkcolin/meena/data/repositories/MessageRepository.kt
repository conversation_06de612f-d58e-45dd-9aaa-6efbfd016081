package com.tfkcolin.meena.data.repositories

import com.google.gson.Gson
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.api.ErrorResponse
import com.tfkcolin.meena.data.api.MessageReactionRequest as ApiMessageReactionRequest
import com.tfkcolin.meena.data.api.MessageResponse
import com.tfkcolin.meena.data.api.SendMessageRequest
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.data.models.MessageReactionRequest as ModelMessageReactionRequest

import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.TokenManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for message operations.
 * This version only uses REST API calls (no WebSocket in mock-only branch).
 */
@Singleton
class MessageRepository @Inject constructor(
    private val chatApi: ChatApi,
    private val messageDao: MessageDao,
    private val mediaAttachmentDao: MediaAttachmentDao,
    private val chatDao: ChatDao,
    private val tokenManager: TokenManager,
    private val gson: Gson,
    private val errorHandler: ErrorHandler
) : BaseRepository {

    /**
     * Add a reaction to a message.
     *
     * @param request The reaction request.
     * @return The result of the operation.
     */
    suspend fun addReaction(request: ModelMessageReactionRequest): Result<Unit> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Get the message to get the chat ID
        val message = messageDao.getMessageById(request.messageId) ?: return Result.failure(
            Exception("Message not found")
        )

        return executeNetworkOperation {
            // Send reaction via REST API
            val response = chatApi.addReaction(
                authToken = "Bearer $token",
                messageId = request.messageId,
                request = ApiMessageReactionRequest(emoji = request.emoji)
            )

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        }
    }

    /**
     * Remove a reaction from a message.
     *
     * @param request The reaction request.
     * @return The result of the operation.
     */
    suspend fun removeReaction(request: ModelMessageReactionRequest): Result<Unit> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Get the message to get the chat ID
        val message = messageDao.getMessageById(request.messageId) ?: return Result.failure(
            Exception("Message not found")
        )

        return executeNetworkOperation {
            // Send reaction removal via REST API
            val response = chatApi.removeReaction(
                authToken = "Bearer $token",
                messageId = request.messageId,
                request = ApiMessageReactionRequest(emoji = request.emoji)
            )

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        }
    }

    /**
     * Send a message.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @param replyToMessageId The ID of the message being replied to (optional).
     * @return The result of the send message operation.
     */
    suspend fun sendMessage(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null,
        replyToMessageId: String? = null
    ): Result<Message> {
        val userId = tokenManager.getUserId() ?: return Result.failure(Exception("User not logged in"))

        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val request = SendMessageRequest(
                content = content,
                content_type = contentType,
                media_url = mediaUrl,
                reply_to_message_id = replyToMessageId
            )

            val response = chatApi.sendMessage(
                authToken = "Bearer $token",
                chatId = chatId,
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val messageResponse = response.body()!!

                // Save message to database
                executeDatabaseOperation {
                    messageDao.insertMessage(messageResponse.message)
                    // Update chat's last message
                    updateChatLastMessage(chatId, messageResponse.message)
                }

                messageResponse.message
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    /**
     * Send a message with attachments.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param attachments The list of attachments.
     * @param replyToMessageId The ID of the message being replied to (optional).
     * @return The result of the send message operation.
     */
    suspend fun sendMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>,
        replyToMessageId: String? = null
    ): Result<Message> {
        return executeNetworkOperation {
            // First send the message
            val message = sendMessage(
                chatId = chatId,
                recipientId = recipientId,
                content = content,
                contentType = if (attachments.isNotEmpty()) attachments.first().type else "text",
                replyToMessageId = replyToMessageId
            ).getOrThrow()

            // Update message to indicate it has attachments and save attachments
            val dbResult = executeDatabaseOperation {
                // Update message to indicate it has attachments
                val updatedMessage = message.copy(hasAttachments = true)
                messageDao.insertMessage(updatedMessage)

                // Save attachments with the message ID
                val messageAttachments = attachments.map { attachment ->
                    attachment.copy(messageId = message.id)
                }
                mediaAttachmentDao.insertAttachments(messageAttachments)

                // Return the updated message
                updatedMessage.apply { this.attachments = messageAttachments }
            }

            // Unwrap the database operation result
            dbResult.getOrThrow()
        }
    }

    /**
     * Update a chat's last message.
     *
     * @param chatId The chat ID.
     * @param message The message.
     */
    private suspend fun updateChatLastMessage(chatId: String, message: Message) {
        val chat = chatDao.getChatById(chatId) ?: return
        chatDao.updateChatLastMessage(
            chatId = chatId,
            lastMessage = message.content,
            lastMessageTimestamp = message.timestamp
        )
    }
}
