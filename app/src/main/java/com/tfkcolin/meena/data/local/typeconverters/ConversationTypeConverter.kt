package com.tfkcolin.meena.data.local.typeconverters

import androidx.room.TypeConverter
import com.tfkcolin.meena.data.models.ConversationType

/**
 * Type converter for ConversationType enum.
 */
class ConversationTypeConverter {
    
    /**
     * Convert a ConversationType to a String.
     *
     * @param conversationType The ConversationType to convert.
     * @return The String representation of the ConversationType.
     */
    @TypeConverter
    fun fromConversationType(conversationType: ConversationType?): String? {
        return conversationType?.value
    }
    
    /**
     * Convert a String to a ConversationType.
     *
     * @param value The String to convert.
     * @return The ConversationType.
     */
    @TypeConverter
    fun toConversationType(value: String?): ConversationType? {
        return value?.let { ConversationType.fromString(it) }
    }
}
