package com.tfkcolin.meena.data.models

/**
 * Message edit model.
 * 
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param newContent The new content.
 * @param timestamp The timestamp.
 */
data class MessageEdit(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val newContent: String,
    val timestamp: Long = System.currentTimeMillis()
)
