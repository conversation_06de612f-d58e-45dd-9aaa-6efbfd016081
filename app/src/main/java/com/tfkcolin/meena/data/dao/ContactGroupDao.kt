package com.tfkcolin.meena.data.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.tfkcolin.meena.data.models.ContactGroup
import kotlinx.coroutines.flow.Flow

/**
 * DAO interface for contact groups.
 */
@Dao
interface ContactGroupDao {

    /**
     * Get all contact groups.
     *
     * @return A list of contact groups.
     */
    @Query("SELECT * FROM contact_groups")
    suspend fun getAll(): List<ContactGroup>

    /**
     * Get all contact groups as a flow.
     *
     * @return A flow of contact groups.
     */
    @Query("SELECT * FROM contact_groups")
    fun getAllFlow(): Flow<List<ContactGroup>>

    /**
     * Get a contact group by ID.
     *
     * @param groupId The ID of the group to get.
     * @return The contact group.
     */
    @Query("SELECT * FROM contact_groups WHERE id = :groupId")
    suspend fun getById(groupId: String): ContactGroup

    /**
     * Insert a contact group.
     *
     * @param contactGroup The contact group to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(contactGroup: ContactGroup)

    /**
     * Insert multiple contact groups.
     *
     * @param contactGroups The contact groups to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(contactGroups: List<ContactGroup>)

    /**
     * Delete a contact group by ID.
     *
     * @param groupId The ID of the group to delete.
     */
    @Query("DELETE FROM contact_groups WHERE id = :groupId")
    suspend fun deleteById(groupId: String)

    /**
     * Delete all contact groups.
     */
    @Query("DELETE FROM contact_groups")
    suspend fun deleteAll()
}
