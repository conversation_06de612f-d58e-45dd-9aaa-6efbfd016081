package com.tfkcolin.meena.data.repositories.firebase

import android.net.Uri
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.Query
import com.google.firebase.storage.FirebaseStorage
import com.tfkcolin.meena.data.api.*
import com.tfkcolin.meena.data.models.Story
import com.tfkcolin.meena.data.models.StoryView
import com.tfkcolin.meena.data.models.StoryHighlight
import com.tfkcolin.meena.data.models.firebase.FirebaseStory
import com.tfkcolin.meena.data.models.firebase.FirebaseStoryView
import com.tfkcolin.meena.data.models.firebase.FirebaseStoryHighlight
import com.tfkcolin.meena.data.models.firebase.FirebaseUserProfile
import com.tfkcolin.meena.domain.repositories.IStoriesRepository
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IStoriesRepository.
 * Uses Firestore for story storage with real-time updates and Firebase Storage for media.
 */
@Singleton
class FirebaseStoriesRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val storage: FirebaseStorage,
    private val tokenManager: TokenManager
) : IStoriesRepository {

    companion object {
        private const val STORIES_COLLECTION = "stories"
        private const val STORY_VIEWS_COLLECTION = "story_views"
        private const val STORY_HIGHLIGHTS_COLLECTION = "story_highlights"
        private const val USERS_COLLECTION = "users"
        private const val STORAGE_STORIES_PATH = "stories"
    }

    override suspend fun getStories(limit: Int, offset: Int): Result<StoriesListResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            // Get stories from contacts and public stories
            val querySnapshot = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("isExpired", false)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseStories = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseStory::class.java)
            }

            // Filter stories based on privacy and user permissions
            val visibleStories = firebaseStories.filter { firebaseStory ->
                when (firebaseStory.privacyType) {
                    "public" -> true
                    "contacts" -> true // TODO: Check if user is in contacts
                    "close_friends" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                    "custom" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                    else -> firebaseStory.userId == currentUserId
                }
            }

            val stories = visibleStories.map { it.toStory() }

            val response = StoriesListResponse(
                stories = stories,
                total_count = stories.size,
                has_more = stories.size >= limit
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getStoriesFlow(): Flow<List<Story>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("isExpired", false)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseStories = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseStory::class.java)
                        }

                        // Filter stories based on privacy and user permissions
                        val visibleStories = firebaseStories.filter { firebaseStory ->
                            when (firebaseStory.privacyType) {
                                "public" -> true
                                "contacts" -> true // TODO: Check if user is in contacts
                                "close_friends" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                                "custom" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                                else -> firebaseStory.userId == currentUserId
                            }
                        }

                        val stories = visibleStories.map { it.toStory() }
                        trySend(stories)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun getUserStories(userId: String, limit: Int, offset: Int): Result<StoriesListResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val querySnapshot = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("isExpired", false)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseStories = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseStory::class.java)
            }

            // Filter stories based on privacy (only if not the owner)
            val visibleStories = if (userId == currentUserId) {
                firebaseStories
            } else {
                firebaseStories.filter { firebaseStory ->
                    when (firebaseStory.privacyType) {
                        "public" -> true
                        "contacts" -> true // TODO: Check if user is in contacts
                        "close_friends" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                        "custom" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                        else -> false
                    }
                }
            }

            val stories = visibleStories.map { it.toStory() }

            val response = StoriesListResponse(
                stories = stories,
                total_count = stories.size,
                has_more = stories.size >= limit
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getStoryById(storyId: String): Result<StoryResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val storyDoc = firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .get()
                .await()

            if (!storyDoc.exists()) {
                throw Exception("Story not found")
            }

            val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                ?: throw Exception("Failed to parse story")

            // Check if user can view this story
            val canView = when (firebaseStory.privacyType) {
                "public" -> true
                "contacts" -> true // TODO: Check if user is in contacts
                "close_friends" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                "custom" -> firebaseStory.allowedViewerIds.contains(currentUserId)
                else -> firebaseStory.userId == currentUserId
            }

            if (!canView) {
                throw Exception("You don't have permission to view this story")
            }

            val story = firebaseStory.toStory()
            val response = StoryResponse(story = story)

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createStory(
        mediaUri: Uri,
        mediaType: String,
        caption: String?,
        privacyType: String,
        allowedViewerIds: List<String>,
        backgroundColor: String?,
        textColor: String?
    ): Result<StoryResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            // Upload media to Firebase Storage
            val storyId = UUID.randomUUID().toString()
            val mediaRef = storage.reference
                .child("$STORAGE_STORIES_PATH/$currentUserId/$storyId")
            
            val uploadTask = mediaRef.putFile(mediaUri).await()
            val mediaUrl = uploadTask.storage.downloadUrl.await().toString()

            // Create thumbnail URL (for now, same as media URL)
            val thumbnailUrl = if (mediaType == "image") mediaUrl else null

            val firebaseStory = FirebaseStory(
                id = storyId,
                userId = currentUserId,
                mediaUrl = mediaUrl,
                mediaType = mediaType,
                thumbnailUrl = thumbnailUrl,
                caption = caption,
                privacyType = privacyType,
                allowedViewerIds = allowedViewerIds,
                backgroundColor = backgroundColor,
                textColor = textColor,
                createdAt = System.currentTimeMillis(),
                expiresAt = System.currentTimeMillis() + (24 * 60 * 60 * 1000) // 24 hours
            )

            // Save story to Firestore
            firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .set(firebaseStory)
                .await()

            val story = firebaseStory.toStory()
            val response = StoryResponse(story = story)

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteStory(storyId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            // Get story to verify ownership
            val storyDoc = firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .get()
                .await()

            if (!storyDoc.exists()) {
                throw Exception("Story not found")
            }

            val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                ?: throw Exception("Failed to parse story")

            if (firebaseStory.userId != currentUserId) {
                throw Exception("You can only delete your own stories")
            }

            // Delete story from Firestore
            firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .delete()
                .await()

            // Delete story views
            val viewsQuery = firestore.collection(STORY_VIEWS_COLLECTION)
                .whereEqualTo("storyId", storyId)
                .get()
                .await()

            val batch = firestore.batch()
            viewsQuery.documents.forEach { doc ->
                batch.delete(doc.reference)
            }
            batch.commit().await()

            // TODO: Delete media from Firebase Storage

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun viewStory(storyId: String, viewDuration: Long): Result<StoryViewResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Check if story exists and user can view it
            val storyResult = getStoryById(storyId)
            if (storyResult.isFailure) {
                return Result.failure(storyResult.exceptionOrNull() ?: Exception("Story not found"))
            }

            // Check if user already viewed this story
            val existingViewQuery = firestore.collection(STORY_VIEWS_COLLECTION)
                .whereEqualTo("storyId", storyId)
                .whereEqualTo("viewerId", currentUserId)
                .limit(1)
                .get()
                .await()

            val viewId = if (existingViewQuery.isEmpty) {
                // Create new view
                val newViewId = UUID.randomUUID().toString()
                val firebaseStoryView = FirebaseStoryView(
                    id = newViewId,
                    storyId = storyId,
                    viewerId = currentUserId,
                    viewedAt = System.currentTimeMillis(),
                    viewDuration = viewDuration
                )

                firestore.collection(STORY_VIEWS_COLLECTION)
                    .document(newViewId)
                    .set(firebaseStoryView)
                    .await()

                // Update story view count
                firestore.collection(STORIES_COLLECTION)
                    .document(storyId)
                    .update("viewCount", com.google.firebase.firestore.FieldValue.increment(1))
                    .await()

                // Add viewer to story's viewer list
                val storyDoc = firestore.collection(STORIES_COLLECTION)
                    .document(storyId)
                    .get()
                    .await()

                val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                if (firebaseStory != null && !firebaseStory.viewerIds.contains(currentUserId)) {
                    val updatedViewerIds = firebaseStory.viewerIds + currentUserId
                    firestore.collection(STORIES_COLLECTION)
                        .document(storyId)
                        .update("viewerIds", updatedViewerIds)
                        .await()
                }

                newViewId
            } else {
                // Update existing view
                val existingView = existingViewQuery.documents.first()
                existingView.reference.update(
                    mapOf(
                        "viewedAt" to System.currentTimeMillis(),
                        "viewDuration" to viewDuration
                    )
                ).await()
                existingView.id
            }

            // Get the view to return
            val viewDoc = firestore.collection(STORY_VIEWS_COLLECTION)
                .document(viewId)
                .get()
                .await()

            val firebaseStoryView = viewDoc.toObject(FirebaseStoryView::class.java)
                ?: throw Exception("Failed to get story view")

            val storyView = firebaseStoryView.toStoryView()
            val response = StoryViewResponse(view = storyView)

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getStoryViews(storyId: String, limit: Int, offset: Int): Result<List<StoryView>> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Check if user owns the story
            val storyDoc = firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .get()
                .await()

            if (!storyDoc.exists()) {
                throw Exception("Story not found")
            }

            val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                ?: throw Exception("Failed to parse story")

            if (firebaseStory.userId != currentUserId) {
                throw Exception("You can only view your own story views")
            }

            val querySnapshot = firestore.collection(STORY_VIEWS_COLLECTION)
                .whereEqualTo("storyId", storyId)
                .orderBy("viewedAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseStoryViews = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseStoryView::class.java)
            }

            val storyViews = firebaseStoryViews.map { it.toStoryView() }

            Result.success(storyViews)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun reactToStory(storyId: String, reactionType: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Check if story exists and user can view it
            val storyResult = getStoryById(storyId)
            if (storyResult.isFailure) {
                return Result.failure(storyResult.exceptionOrNull() ?: Exception("Story not found"))
            }

            // Find existing view or create new one
            val existingViewQuery = firestore.collection(STORY_VIEWS_COLLECTION)
                .whereEqualTo("storyId", storyId)
                .whereEqualTo("viewerId", currentUserId)
                .limit(1)
                .get()
                .await()

            if (existingViewQuery.isEmpty) {
                // Create new view with reaction
                val viewId = UUID.randomUUID().toString()
                val firebaseStoryView = FirebaseStoryView(
                    id = viewId,
                    storyId = storyId,
                    viewerId = currentUserId,
                    viewedAt = System.currentTimeMillis(),
                    reactionType = reactionType
                )

                firestore.collection(STORY_VIEWS_COLLECTION)
                    .document(viewId)
                    .set(firebaseStoryView)
                    .await()
            } else {
                // Update existing view with reaction
                val existingView = existingViewQuery.documents.first()
                existingView.reference.update("reactionType", reactionType).await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun replyToStory(storyId: String, content: String): Result<Unit> {
        return try {
            // TODO: Implement story reply functionality
            // This would typically create a direct message to the story owner
            Result.failure(Exception("Story replies not implemented yet"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getStoryHighlights(userId: String): Result<StoryHighlightsListResponse> {
        return try {
            val querySnapshot = firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .whereEqualTo("userId", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val firebaseHighlights = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseStoryHighlight::class.java)
            }

            val highlights = firebaseHighlights.map { it.toStoryHighlight() }

            val response = StoryHighlightsListResponse(
                highlights = highlights,
                total_count = highlights.size
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createStoryHighlight(
        title: String,
        storyIds: List<String>,
        coverUrl: String?
    ): Result<StoryHighlightResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Verify all stories belong to the current user
            for (storyId in storyIds) {
                val storyDoc = firestore.collection(STORIES_COLLECTION)
                    .document(storyId)
                    .get()
                    .await()

                if (!storyDoc.exists()) {
                    throw Exception("Story $storyId not found")
                }

                val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                if (firebaseStory?.userId != currentUserId) {
                    throw Exception("You can only add your own stories to highlights")
                }
            }

            val highlightId = UUID.randomUUID().toString()
            val firebaseHighlight = FirebaseStoryHighlight(
                id = highlightId,
                userId = currentUserId,
                title = title,
                coverUrl = coverUrl,
                storyIds = storyIds,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .set(firebaseHighlight)
                .await()

            // Mark stories as highlighted
            val batch = firestore.batch()
            storyIds.forEach { storyId ->
                val storyRef = firestore.collection(STORIES_COLLECTION).document(storyId)
                batch.update(storyRef, mapOf(
                    "isHighlighted" to true,
                    "highlightId" to highlightId
                ))
            }
            batch.commit().await()

            val highlight = firebaseHighlight.toStoryHighlight()
            val response = StoryHighlightResponse(highlight = highlight)

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateStoryHighlight(
        highlightId: String,
        title: String?,
        storyIds: List<String>?,
        coverUrl: String?
    ): Result<StoryHighlightResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get existing highlight
            val highlightDoc = firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .get()
                .await()

            if (!highlightDoc.exists()) {
                throw Exception("Highlight not found")
            }

            val existingHighlight = highlightDoc.toObject(FirebaseStoryHighlight::class.java)
                ?: throw Exception("Failed to parse highlight")

            if (existingHighlight.userId != currentUserId) {
                throw Exception("You can only update your own highlights")
            }

            // Prepare updates
            val updates = mutableMapOf<String, Any>()
            title?.let { updates["title"] = it }
            coverUrl?.let { updates["coverUrl"] = it }
            updates["updatedAt"] = System.currentTimeMillis()

            if (storyIds != null) {
                updates["storyIds"] = storyIds
            }

            // Update highlight
            firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .update(updates)
                .await()

            // Get updated highlight
            val updatedDoc = firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .get()
                .await()

            val updatedHighlight = updatedDoc.toObject(FirebaseStoryHighlight::class.java)
                ?: throw Exception("Failed to get updated highlight")

            val highlight = updatedHighlight.toStoryHighlight()
            val response = StoryHighlightResponse(highlight = highlight)

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteStoryHighlight(highlightId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get existing highlight
            val highlightDoc = firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .get()
                .await()

            if (!highlightDoc.exists()) {
                throw Exception("Highlight not found")
            }

            val existingHighlight = highlightDoc.toObject(FirebaseStoryHighlight::class.java)
                ?: throw Exception("Failed to parse highlight")

            if (existingHighlight.userId != currentUserId) {
                throw Exception("You can only delete your own highlights")
            }

            // Remove highlight from stories
            val batch = firestore.batch()
            existingHighlight.storyIds.forEach { storyId ->
                val storyRef = firestore.collection(STORIES_COLLECTION).document(storyId)
                batch.update(storyRef, mapOf(
                    "isHighlighted" to false,
                    "highlightId" to null
                ))
            }
            batch.commit().await()

            // Delete highlight
            firestore.collection(STORY_HIGHLIGHTS_COLLECTION)
                .document(highlightId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // Simplified implementations for remaining methods
    override suspend fun getExpiringStories(): Result<List<String>> {
        return try {
            val expirationTime = System.currentTimeMillis() + (60 * 60 * 1000) // 1 hour from now

            val querySnapshot = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("isExpired", false)
                .whereLessThan("expiresAt", expirationTime)
                .get()
                .await()

            val storyIds = querySnapshot.documents.map { it.id }
            Result.success(storyIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun cleanupExpiredStories(): Result<Unit> {
        return try {
            val currentTime = System.currentTimeMillis()

            val querySnapshot = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("isExpired", false)
                .whereLessThan("expiresAt", currentTime)
                .get()
                .await()

            val batch = firestore.batch()
            querySnapshot.documents.forEach { doc ->
                batch.update(doc.reference, "isExpired", true)
            }
            batch.commit().await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateStoryPrivacy(
        storyId: String,
        privacyType: String,
        allowedViewerIds: List<String>
    ): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Verify story ownership
            val storyDoc = firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .get()
                .await()

            if (!storyDoc.exists()) {
                throw Exception("Story not found")
            }

            val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                ?: throw Exception("Failed to parse story")

            if (firebaseStory.userId != currentUserId) {
                throw Exception("You can only update your own stories")
            }

            // Update privacy settings
            firestore.collection(STORIES_COLLECTION)
                .document(storyId)
                .update(
                    mapOf(
                        "privacyType" to privacyType,
                        "allowedViewerIds" to allowedViewerIds
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getStoryAnalytics(storyId: String?): Result<Map<String, Any>> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            if (storyId != null) {
                // Get analytics for specific story
                val storyDoc = firestore.collection(STORIES_COLLECTION)
                    .document(storyId)
                    .get()
                    .await()

                if (!storyDoc.exists()) {
                    throw Exception("Story not found")
                }

                val firebaseStory = storyDoc.toObject(FirebaseStory::class.java)
                    ?: throw Exception("Failed to parse story")

                if (firebaseStory.userId != currentUserId) {
                    throw Exception("You can only view analytics for your own stories")
                }

                val analytics = mapOf(
                    "story_id" to storyId,
                    "view_count" to firebaseStory.viewCount,
                    "viewer_count" to firebaseStory.viewerIds.size,
                    "created_at" to firebaseStory.createdAt,
                    "expires_at" to firebaseStory.expiresAt
                )

                Result.success(analytics)
            } else {
                // Get overall analytics for user's stories
                val storiesQuery = firestore.collection(STORIES_COLLECTION)
                    .whereEqualTo("userId", currentUserId)
                    .get()
                    .await()

                val stories = storiesQuery.documents.mapNotNull { doc ->
                    doc.toObject(FirebaseStory::class.java)
                }

                val totalViews = stories.sumOf { it.viewCount }
                val totalStories = stories.size
                val activeStories = stories.count { !it.isExpired }

                val analytics = mapOf(
                    "total_stories" to totalStories,
                    "active_stories" to activeStories,
                    "total_views" to totalViews,
                    "average_views" to if (totalStories > 0) totalViews / totalStories else 0
                )

                Result.success(analytics)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun searchStories(query: String, limit: Int, offset: Int): Result<StoriesListResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Simple search by caption (Firestore has limited text search capabilities)
            val querySnapshot = firestore.collection(STORIES_COLLECTION)
                .whereEqualTo("isExpired", false)
                .whereEqualTo("privacyType", "public") // Only search public stories
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseStories = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseStory::class.java)
            }

            // Filter by query in caption (client-side filtering due to Firestore limitations)
            val filteredStories = firebaseStories.filter { story ->
                story.caption?.contains(query, ignoreCase = true) == true
            }

            val stories = filteredStories.map { it.toStory() }

            val response = StoriesListResponse(
                stories = stories,
                total_count = stories.size,
                has_more = stories.size >= limit
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
