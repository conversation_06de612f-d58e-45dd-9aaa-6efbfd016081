package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Contact group list response.
 *
 * @property groups The list of contact groups.
 * @property totalCount The total number of contact groups.
 */
data class ContactGroupListResponse(
    @SerializedName("contact_groups")
    val contactGroups: List<ContactGroup>,

    @SerializedName("total_count")
    val totalCount: Int,

    @SerializedName("limit")
    val limit: Int,

    @SerializedName("offset")
    val offset: Int
)

/**
 * Create contact group request.
 *
 * @property name The name of the group.
 * @property description The description of the group.
 * @property memberIds The IDs of the initial members.
 */
data class CreateContactGroupRequest(
    @SerializedName("name")
    val name: String,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("contact_ids")
    val contactIds: List<String> = emptyList()
)

/**
 * Update contact group request.
 *
 * @property name The new name of the group.
 * @property description The new description of the group.
 */
data class UpdateContactGroupRequest(
    @SerializedName("name")
    val name: String? = null,

    @SerializedName("description")
    val description: String? = null
)
