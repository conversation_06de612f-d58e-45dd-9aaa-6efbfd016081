package com.tfkcolin.meena.data.repository

import com.tfkcolin.meena.data.api.ContactGroupApi
import com.tfkcolin.meena.data.local.ContactGroupDao
import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.models.ContactGroupListResponse
import com.tfkcolin.meena.data.models.CreateContactGroupRequest
import com.tfkcolin.meena.data.models.UpdateContactGroupRequest
import com.tfkcolin.meena.data.preferences.UserPreferences
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for contact groups.
 *
 * @property contactGroupApi The contact group API.
 * @property contactGroupDao The contact group DAO.
 * @property userPreferences The user preferences.
 */
@Singleton
class ContactGroupRepository @Inject constructor(
    private val contactGroupApi: ContactGroupApi,
    private val contactGroupDao: ContactGroupDao,
    private val userPreferences: UserPreferences
) : IContactGroupRepository {

    /**
     * Get all contact groups.
     *
     * @param limit The maximum number of groups to return.
     * @param offset The offset for pagination.
     * @return The contact group list response.
     */
    override suspend fun getContactGroups(
        limit: Int,
        offset: Int
    ): ContactGroupListResponse {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val response = contactGroupApi.getContactGroups(authToken, limit, offset)

        if (response.isSuccessful) {
            val contactGroups = response.body()
            contactGroups?.let {
                // Save to database
                contactGroupDao.insertAll(it.contactGroups)
            }
            return contactGroups ?: ContactGroupListResponse(emptyList(), 0, 0, 0)
        } else {
            // If API call fails, return data from database
            val groups = contactGroupDao.getAll()
            return ContactGroupListResponse(groups, groups.size, groups.size, 0)
        }
    }

    /**
     * Get a contact group by ID.
     *
     * @param groupId The ID of the group to get.
     * @return The contact group.
     */
    override suspend fun getContactGroupById(groupId: String): ContactGroup {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val response = contactGroupApi.getContactGroupById(authToken, groupId)

        if (response.isSuccessful) {
            val contactGroup = response.body()
            contactGroup?.let {
                // Save to database
                contactGroupDao.insert(it)
            }
            return contactGroup ?: contactGroupDao.getById(groupId)
        } else {
            // If API call fails, return data from database
            return contactGroupDao.getById(groupId)
        }
    }

    /**
     * Get all contact groups as a flow.
     *
     * @return A flow of contact groups.
     */
    override fun getContactGroupsFlow(): Flow<List<ContactGroup>> {
        return contactGroupDao.getAllFlow()
    }

    /**
     * Create a new contact group.
     *
     * @param name The name of the group.
     * @param description The description of the group.
     * @param memberIds The IDs of the initial members.
     * @return The created contact group.
     */
    override suspend fun createContactGroup(
        name: String,
        description: String?,
        memberIds: List<String>
    ): ContactGroup {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val request = CreateContactGroupRequest(name, description, memberIds)
        val response = contactGroupApi.createContactGroup(authToken, request)

        if (response.isSuccessful) {
            val contactGroup = response.body()
            contactGroup?.let {
                // Save to database
                contactGroupDao.insert(it)
            }
            return contactGroup ?: throw Exception("Failed to create contact group")
        } else {
            throw Exception("Failed to create contact group: ${response.errorBody()?.string()}")
        }
    }

    /**
     * Update a contact group.
     *
     * @param groupId The ID of the group to update.
     * @param name The new name of the group.
     * @param description The new description of the group.
     * @return The updated contact group.
     */
    override suspend fun updateContactGroup(
        groupId: String,
        name: String,
        description: String?
    ): ContactGroup {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val request = UpdateContactGroupRequest(name, description)
        val response = contactGroupApi.updateContactGroup(authToken, groupId, request)

        if (response.isSuccessful) {
            val contactGroup = response.body()
            contactGroup?.let {
                // Save to database
                contactGroupDao.insert(it)
            }
            return contactGroup ?: throw Exception("Failed to update contact group")
        } else {
            throw Exception("Failed to update contact group: ${response.errorBody()?.string()}")
        }
    }

    /**
     * Delete a contact group.
     *
     * @param groupId The ID of the group to delete.
     */
    override suspend fun deleteContactGroup(groupId: String) {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val response = contactGroupApi.deleteContactGroup(authToken, groupId)

        if (response.isSuccessful) {
            // Delete from database
            contactGroupDao.deleteById(groupId)
        } else {
            throw Exception("Failed to delete contact group: ${response.errorBody()?.string()}")
        }
    }

    /**
     * Add a contact to a group.
     *
     * @param groupId The ID of the group to add the contact to.
     * @param contactId The ID of the contact to add.
     * @return The updated contact group.
     */
    override suspend fun addContactToGroup(
        groupId: String,
        contactId: String
    ): ContactGroup {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val response = contactGroupApi.addContactToGroup(authToken, groupId, contactId)

        if (response.isSuccessful) {
            val contactGroup = response.body()
            contactGroup?.let {
                // Save to database
                contactGroupDao.insert(it)
            }
            return contactGroup ?: throw Exception("Failed to add contact to group")
        } else {
            throw Exception("Failed to add contact to group: ${response.errorBody()?.string()}")
        }
    }

    /**
     * Remove a contact from a group.
     *
     * @param groupId The ID of the group to remove the contact from.
     * @param contactId The ID of the contact to remove.
     * @return The updated contact group.
     */
    override suspend fun removeContactFromGroup(
        groupId: String,
        contactId: String
    ): ContactGroup {
        val authToken = "Bearer ${userPreferences.getAuthToken()}"
        val response = contactGroupApi.removeContactFromGroup(authToken, groupId, contactId)

        if (response.isSuccessful) {
            val contactGroup = response.body()
            contactGroup?.let {
                // Save to database
                contactGroupDao.insert(it)
            }
            return contactGroup ?: throw Exception("Failed to remove contact from group")
        } else {
            throw Exception("Failed to remove contact from group: ${response.errorBody()?.string()}")
        }
    }
}
