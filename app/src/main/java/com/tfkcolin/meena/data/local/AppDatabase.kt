package com.tfkcolin.meena.data.local

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.tfkcolin.meena.data.local.ContactGroupDao
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.typeconverters.ConversationTypeConverter
import com.tfkcolin.meena.data.local.typeconverters.ListStringConverter
import com.tfkcolin.meena.data.local.typeconverters.MapStringListConverter
import com.tfkcolin.meena.data.local.typeconverters.PrivacyTypeConverter
import com.tfkcolin.meena.data.local.typeconverters.UserProfileConverter
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.data.models.User

/**
 * Room database for the application.
 */
@Database(
    entities = [User::class, Contact::class, Message::class, Chat::class, MediaAttachment::class, ContactGroup::class],
    version = 2,
    exportSchema = false
)
@TypeConverters(
    ConversationTypeConverter::class,
    PrivacyTypeConverter::class,
    ListStringConverter::class,
    UserProfileConverter::class,
    MapStringListConverter::class
)
abstract class AppDatabase : RoomDatabase() {

    /**
     * Get the user DAO.
     */
    abstract fun userDao(): UserDao

    /**
     * Get the contact DAO.
     */
    abstract fun contactDao(): ContactDao

    /**
     * Get the message DAO.
     */
    abstract fun messageDao(): MessageDao

    /**
     * Get the chat DAO.
     */
    abstract fun chatDao(): ChatDao

    /**
     * Get the media attachment DAO.
     */
    abstract fun mediaAttachmentDao(): MediaAttachmentDao

    /**
     * Get the contact group DAO.
     */
    abstract fun contactGroupDao(): ContactGroupDao

    companion object {
        private const val DATABASE_NAME = "meena_database"

        @Volatile
        private var INSTANCE: AppDatabase? = null

        /**
         * Get the database instance.
         *
         * @param context The application context.
         * @return The database instance.
         */
        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration()
                .build()

                INSTANCE = instance
                instance
            }
        }
    }
}
