package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Message model representing a chat message.
 */
@Entity(
    tableName = "messages",
    indices = [
        Index("chatId"),
        Index("senderId"),
        Index("recipientId")
    ]
)
data class Message(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("chat_id")
    val chatId: String,

    @SerializedName("sender_id")
    val senderId: String,

    @SerializedName("recipient_id")
    val recipientId: String,

    @SerializedName("content")
    val content: String,

    @SerializedName("content_type")
    val contentType: String = "text", // text, image, video, audio, document, location

    @SerializedName("media_url")
    val mediaUrl: String? = null,

    @SerializedName("has_attachments")
    val hasAttachments: Boolean = false,

    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),

    @SerializedName("status")
    val status: String = "sending", // sending, sent, delivered, read, failed

    @SerializedName("is_edited")
    val isEdited: Boolean = false,

    @SerializedName("deleted_for")
    val deletedFor: String? = null, // null, "self", "everyone"

    @SerializedName("reply_to_message_id")
    val replyToMessageId: String? = null,

    @SerializedName("forward_from_message_id")
    val forwardFromMessageId: String? = null,

    @SerializedName("forward_from_chat_id")
    val forwardFromChatId: String? = null,

    @SerializedName("forward_from_user_id")
    val forwardFromUserId: String? = null,

    @SerializedName("reactions")
    val reactions: Map<String, List<String>>? = null // Map of reaction emoji to list of user IDs
) {
    @Ignore
    @SerializedName("attachments")
    var attachments: List<MediaAttachment>? = null

    @Ignore
    @SerializedName("reply_to_message")
    var replyToMessage: Message? = null

    /**
     * Check if this message is a reply.
     *
     * @return True if this message is a reply, false otherwise.
     */
    fun isReply(): Boolean {
        return replyToMessageId != null
    }

    /**
     * Check if this message is a forward.
     *
     * @return True if this message is a forward, false otherwise.
     */
    fun isForward(): Boolean {
        return forwardFromMessageId != null
    }

    /**
     * Check if this message has reactions.
     *
     * @return True if this message has reactions, false otherwise.
     */
    fun hasReactions(): Boolean {
        return reactions != null && reactions.isNotEmpty()
    }

    /**
     * Get the total number of reactions.
     *
     * @return The total number of reactions.
     */
    fun getTotalReactionCount(): Int {
        return reactions?.values?.sumOf { it.size } ?: 0
    }

    /**
     * Check if a user has reacted with a specific emoji.
     *
     * @param emoji The emoji to check.
     * @param userId The user ID to check.
     * @return True if the user has reacted with the emoji, false otherwise.
     */
    fun hasUserReacted(emoji: String, userId: String): Boolean {
        return reactions?.get(emoji)?.contains(userId) == true
    }

    /**
     * Get the list of emojis that a user has reacted with.
     *
     * @param userId The user ID to check.
     * @return The list of emojis that the user has reacted with.
     */
    fun getUserReactions(userId: String): List<String> {
        return reactions?.filter { it.value.contains(userId) }?.keys?.toList() ?: emptyList()
    }
}

/**
 * Message status model representing the status of a message.
 */
data class MessageStatus(
    @SerializedName("message_id")
    val messageId: String,

    @SerializedName("chat_id")
    val chatId: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("status")
    val status: String, // delivered, read

    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis()
)

