package com.tfkcolin.meena.data.preferences

import android.content.Context
// TODO: Migrate to DataStore Crypto when it becomes available
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * User preferences manager.
 * Handles user preferences and settings.
 */
@Singleton
class UserPreferences @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val PREFS_NAME = "user_preferences"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_THEME = "theme"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val KEY_MESSAGE_PREVIEW_ENABLED = "message_preview_enabled"
        private const val KEY_READ_RECEIPTS_ENABLED = "read_receipts_enabled"
        private const val KEY_TYPING_INDICATORS_ENABLED = "typing_indicators_enabled"
    }

    // Create master key for encryption
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()

    // Create encrypted shared preferences
    private val sharedPreferences = EncryptedSharedPreferences.create(
        context,
        PREFS_NAME,
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )

    /**
     * Get the authentication token.
     *
     * @return The authentication token, or null if not set.
     */
    fun getAuthToken(): String? {
        return sharedPreferences.getString(KEY_AUTH_TOKEN, null)
    }

    /**
     * Save the authentication token.
     *
     * @param token The authentication token.
     */
    fun saveAuthToken(token: String) {
        sharedPreferences.edit().putString(KEY_AUTH_TOKEN, token).apply()
    }

    /**
     * Get the refresh token.
     *
     * @return The refresh token, or null if not set.
     */
    fun getRefreshToken(): String? {
        return sharedPreferences.getString(KEY_REFRESH_TOKEN, null)
    }

    /**
     * Save the refresh token.
     *
     * @param token The refresh token.
     */
    fun saveRefreshToken(token: String) {
        sharedPreferences.edit().putString(KEY_REFRESH_TOKEN, token).apply()
    }

    /**
     * Get the user ID.
     *
     * @return The user ID, or null if not set.
     */
    fun getUserId(): String? {
        return sharedPreferences.getString(KEY_USER_ID, null)
    }

    /**
     * Save the user ID.
     *
     * @param userId The user ID.
     */
    fun saveUserId(userId: String) {
        sharedPreferences.edit().putString(KEY_USER_ID, userId).apply()
    }

    /**
     * Clear all preferences.
     */
    fun clearPreferences() {
        sharedPreferences.edit().clear().apply()
    }

    /**
     * Get the theme.
     *
     * @return The theme, or "system" if not set.
     */
    fun getTheme(): String {
        return sharedPreferences.getString(KEY_THEME, "system") ?: "system"
    }

    /**
     * Save the theme.
     *
     * @param theme The theme.
     */
    fun saveTheme(theme: String) {
        sharedPreferences.edit().putString(KEY_THEME, theme).apply()
    }

    /**
     * Get the language.
     *
     * @return The language, or "en" if not set.
     */
    fun getLanguage(): String {
        return sharedPreferences.getString(KEY_LANGUAGE, "en") ?: "en"
    }

    /**
     * Save the language.
     *
     * @param language The language.
     */
    fun saveLanguage(language: String) {
        sharedPreferences.edit().putString(KEY_LANGUAGE, language).apply()
    }

    /**
     * Check if notifications are enabled.
     *
     * @return True if notifications are enabled, false otherwise.
     */
    fun areNotificationsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_NOTIFICATIONS_ENABLED, true)
    }

    /**
     * Set whether notifications are enabled.
     *
     * @param enabled Whether notifications are enabled.
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled).apply()
    }

    /**
     * Check if message previews are enabled.
     *
     * @return True if message previews are enabled, false otherwise.
     */
    fun areMessagePreviewsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_MESSAGE_PREVIEW_ENABLED, true)
    }

    /**
     * Set whether message previews are enabled.
     *
     * @param enabled Whether message previews are enabled.
     */
    fun setMessagePreviewsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_MESSAGE_PREVIEW_ENABLED, enabled).apply()
    }

    /**
     * Check if read receipts are enabled.
     *
     * @return True if read receipts are enabled, false otherwise.
     */
    fun areReadReceiptsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_READ_RECEIPTS_ENABLED, true)
    }

    /**
     * Set whether read receipts are enabled.
     *
     * @param enabled Whether read receipts are enabled.
     */
    fun setReadReceiptsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_READ_RECEIPTS_ENABLED, enabled).apply()
    }

    /**
     * Check if typing indicators are enabled.
     *
     * @return True if typing indicators are enabled, false otherwise.
     */
    fun areTypingIndicatorsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_TYPING_INDICATORS_ENABLED, true)
    }

    /**
     * Set whether typing indicators are enabled.
     *
     * @param enabled Whether typing indicators are enabled.
     */
    fun setTypingIndicatorsEnabled(enabled: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_TYPING_INDICATORS_ENABLED, enabled).apply()
    }
}
