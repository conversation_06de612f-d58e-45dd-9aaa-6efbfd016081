package com.tfkcolin.meena.data.local.typeconverters

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tfkcolin.meena.data.models.UserProfile

/**
 * Type converter for UserProfile objects.
 */
class UserProfileConverter {
    private val gson = Gson()

    /**
     * Convert a UserProfile to a JSON string.
     *
     * @param userProfile The UserProfile to convert.
     * @return The JSON string representation of the UserProfile.
     */
    @TypeConverter
    fun fromUserProfile(userProfile: UserProfile?): String? {
        return userProfile?.let { gson.toJson(it) }
    }

    /**
     * Convert a JSON string to a UserProfile.
     *
     * @param json The JSON string to convert.
     * @return The UserProfile.
     */
    @TypeConverter
    fun toUserProfile(json: String?): UserProfile? {
        if (json == null) return null
        val type = object : TypeToken<UserProfile>() {}.type
        return gson.fromJson(json, type)
    }
}
