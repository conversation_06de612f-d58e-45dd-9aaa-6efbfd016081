package com.tfkcolin.meena.data.local

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.tfkcolin.meena.data.models.MediaAttachment
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for the MediaAttachment entity.
 */
@Dao
interface MediaAttachmentDao {
    
    /**
     * Insert a media attachment into the database.
     * If the attachment already exists, replace it.
     * 
     * @param attachment The attachment to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAttachment(attachment: MediaAttachment)
    
    /**
     * Insert multiple media attachments into the database.
     * If an attachment already exists, replace it.
     * 
     * @param attachments The attachments to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAttachments(attachments: List<MediaAttachment>)
    
    /**
     * Get all attachments for a message.
     * 
     * @param messageId The message ID.
     * @return All attachments for the message.
     */
    @Query("SELECT * FROM media_attachments WHERE messageId = :messageId ORDER BY createdAt ASC")
    suspend fun getAttachmentsForMessage(messageId: String): List<MediaAttachment>
    
    /**
     * Get all attachments for a message as a flow.
     * 
     * @param messageId The message ID.
     * @return A flow of all attachments for the message.
     */
    @Query("SELECT * FROM media_attachments WHERE messageId = :messageId ORDER BY createdAt ASC")
    fun getAttachmentsForMessageFlow(messageId: String): Flow<List<MediaAttachment>>
    
    /**
     * Delete an attachment.
     * 
     * @param attachmentId The attachment ID.
     */
    @Query("DELETE FROM media_attachments WHERE id = :attachmentId")
    suspend fun deleteAttachment(attachmentId: String)
    
    /**
     * Delete all attachments for a message.
     * 
     * @param messageId The message ID.
     */
    @Query("DELETE FROM media_attachments WHERE messageId = :messageId")
    suspend fun deleteAttachmentsForMessage(messageId: String)
}
