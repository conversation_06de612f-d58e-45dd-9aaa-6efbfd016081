package com.tfkcolin.meena.data.api

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * API interface for support endpoints.
 */
interface SupportApi {
    
    /**
     * Get support tickets for the current user.
     * 
     * @param limit The maximum number of tickets to return.
     * @param offset The offset for pagination.
     * @return The tickets response.
     */
    @GET("/api/v1/support/tickets")
    suspend fun getTickets(
        @Query("limit") limit: Int = 20,
        @Query("offset") offset: Int = 0
    ): Response<TicketsResponse>
    
    /**
     * Create a new support ticket.
     * 
     * @param request The create ticket request.
     * @return The ticket response.
     */
    @POST("/api/v1/support/tickets")
    suspend fun createTicket(@Body request: CreateTicketRequest): Response<TicketResponse>
    
    /**
     * Get a support ticket by ID.
     * 
     * @param ticketId The ticket ID.
     * @return The ticket response.
     */
    @GET("/api/v1/support/tickets/{ticket_id}")
    suspend fun getTicket(@Path("ticket_id") ticketId: String): Response<TicketResponse>
    
    /**
     * Update a support ticket.
     * 
     * @param ticketId The ticket ID.
     * @param request The update ticket request.
     * @return The ticket response.
     */
    @PUT("/api/v1/support/tickets/{ticket_id}")
    suspend fun updateTicket(
        @Path("ticket_id") ticketId: String,
        @Body request: UpdateTicketRequest
    ): Response<TicketResponse>
    
    /**
     * Get messages for a support ticket.
     * 
     * @param ticketId The ticket ID.
     * @param limit The maximum number of messages to return.
     * @param offset The offset for pagination.
     * @return The ticket messages response.
     */
    @GET("/api/v1/support/tickets/{ticket_id}/messages")
    suspend fun getTicketMessages(
        @Path("ticket_id") ticketId: String,
        @Query("limit") limit: Int = 20,
        @Query("offset") offset: Int = 0
    ): Response<TicketMessagesResponse>
    
    /**
     * Add a message to a support ticket.
     * 
     * @param ticketId The ticket ID.
     * @param request The add ticket message request.
     * @return The ticket message response.
     */
    @POST("/api/v1/support/tickets/{ticket_id}/messages")
    suspend fun addTicketMessage(
        @Path("ticket_id") ticketId: String,
        @Body request: AddTicketMessageRequest
    ): Response<TicketMessageResponse>
}

/**
 * Tickets response.
 */
data class TicketsResponse(
    val tickets: List<Ticket>,
    val total_count: Int,
    val limit: Int,
    val offset: Int
)

/**
 * Ticket response.
 */
data class TicketResponse(
    val ticket: Ticket
)

/**
 * Ticket.
 */
data class Ticket(
    val ticket_id: String,
    val subject: String,
    val status: String, // "open", "in_progress", "resolved", "closed"
    val priority: String, // "low", "medium", "high", "urgent"
    val category: String,
    val created_at: Long,
    val updated_at: Long,
    val resolved_at: Long?
)

/**
 * Create ticket request.
 */
data class CreateTicketRequest(
    val subject: String,
    val category: String,
    val message: String,
    val attachment_ids: List<String>? = null
)

/**
 * Update ticket request.
 */
data class UpdateTicketRequest(
    val status: String? = null,
    val priority: String? = null
)

/**
 * Ticket messages response.
 */
data class TicketMessagesResponse(
    val messages: List<TicketMessage>,
    val total_count: Int,
    val limit: Int,
    val offset: Int
)

/**
 * Ticket message response.
 */
data class TicketMessageResponse(
    val message: TicketMessage
)

/**
 * Ticket message.
 */
data class TicketMessage(
    val message_id: String,
    val ticket_id: String,
    val sender_type: String, // "user", "support"
    val sender_id: String,
    val content: String,
    val attachments: List<TicketAttachment>?,
    val created_at: Long
)

/**
 * Ticket attachment.
 */
data class TicketAttachment(
    val attachment_id: String,
    val name: String,
    val url: String,
    val size: Long,
    val mime_type: String
)

/**
 * Add ticket message request.
 */
data class AddTicketMessageRequest(
    val content: String,
    val attachment_ids: List<String>? = null
)
