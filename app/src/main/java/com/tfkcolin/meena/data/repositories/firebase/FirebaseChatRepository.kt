package com.tfkcolin.meena.data.repositories.firebase

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.Query
import com.tfkcolin.meena.data.api.GroupMemberResponse
import com.tfkcolin.meena.data.api.GroupMembersResponse
import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.data.models.firebase.FirebaseChat
import com.tfkcolin.meena.data.models.firebase.FirebaseMessage
import com.tfkcolin.meena.data.models.firebase.FirebaseMediaAttachment
import com.tfkcolin.meena.domain.repositories.IChatRepository
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IChatRepository.
 * Uses Firestore for chat and message storage with real-time updates.
 */
@Singleton
class FirebaseChatRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val chatDao: ChatDao,
    private val messageDao: MessageDao,
    private val mediaAttachmentDao: MediaAttachmentDao,
    private val tokenManager: TokenManager
) : IChatRepository {

    companion object {
        private const val CHATS_COLLECTION = "chats"
        private const val MESSAGES_COLLECTION = "messages"
        private const val MEDIA_ATTACHMENTS_COLLECTION = "media_attachments"
        private const val TYPING_INDICATORS_COLLECTION = "typing_indicators"
        private const val PRESENCE_COLLECTION = "presence"
        private const val USERS_COLLECTION = "users"
    }

    override suspend fun getChats(limit: Int, offset: Int): Result<com.tfkcolin.meena.data.api.ChatListResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val querySnapshot = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .orderBy("lastMessageTimestamp", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseChats = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseChat::class.java)
            }

            val chats = firebaseChats.map { it.toChat(currentUserId) }

            // Update local database
            chats.forEach { chat ->
                try {
                    chatDao.insertChat(chat)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            val response = com.tfkcolin.meena.data.api.ChatListResponse(
                chats = chats,
                total_count = chats.size
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getChatsFlow(): Flow<List<Chat>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .orderBy("lastMessageTimestamp", Query.Direction.DESCENDING)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseChats = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseChat::class.java)
                        }

                        val chats = firebaseChats.map { it.toChat(currentUserId) }

                        // Update local database in a coroutine
                        CoroutineScope(Dispatchers.IO).launch {
                            chats.forEach { chat ->
                                try {
                                    chatDao.insertChat(chat)
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }

                        trySend(chats)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun getChatById(chatId: String): Result<com.tfkcolin.meena.data.api.ChatResponse> {
        return try {
            val chatDoc = firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .get()
                .await()

            if (!chatDoc.exists()) {
                throw Exception("Chat not found")
            }

            val firebaseChat = chatDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse chat")

            val currentUserId = tokenManager.getUserId() ?: ""
            val chat = firebaseChat.toChat(currentUserId)

            // Update local database
            chatDao.insertChat(chat)

            val response = com.tfkcolin.meena.data.api.ChatResponse(
                chat = chat
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createChat(participantIds: List<String>): Result<com.tfkcolin.meena.data.api.ChatResponse> {
        return try {
            val currentUserId = tokenManager.getUserId() 
                ?: throw Exception("No authenticated user")

            val chatId = UUID.randomUUID().toString()
            val allParticipants = (participantIds + currentUserId).distinct()

            val firebaseChat = FirebaseChat(
                id = chatId,
                conversationType = if (allParticipants.size == 2) "one_to_one" else "group",
                participantIds = allParticipants,
                createdBy = currentUserId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastMessageTimestamp = System.currentTimeMillis()
            )

            // Create chat in Firestore
            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .set(firebaseChat)
                .await()

            val chat = firebaseChat.toChat(currentUserId)
            chatDao.insertChat(chat)

            val response = com.tfkcolin.meena.data.api.ChatResponse(
                chat = chat
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteChat(chatId: String): Result<Unit> {
        return try {
            // Delete chat from Firestore
            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .delete()
                .await()

            // Delete messages in this chat
            val messagesQuery = firestore.collection(MESSAGES_COLLECTION)
                .whereEqualTo("chatId", chatId)
                .get()
                .await()

            val batch = firestore.batch()
            messagesQuery.documents.forEach { doc ->
                batch.delete(doc.reference)
            }
            batch.commit().await()

            // Delete from local database
            chatDao.deleteChat(chatId)
            messageDao.deleteMessagesForChat(chatId)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getMessages(chatId: String, limit: Int, offset: Int): Result<com.tfkcolin.meena.data.api.MessageListResponse> {
        return try {
            val querySnapshot = firestore.collection(MESSAGES_COLLECTION)
                .whereEqualTo("chatId", chatId)
                .orderBy("timestamp", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val firebaseMessages = querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseMessage::class.java)
            }

            val messages = firebaseMessages.map { it.toMessage() }

            // Load attachments for messages
            messages.forEach { message ->
                if (message.hasAttachments) {
                    val attachments = getMessageAttachments(message.id)
                    message.attachments = attachments
                }
            }

            // Update local database
            messages.forEach { message ->
                try {
                    messageDao.insertMessage(message)
                    message.attachments?.forEach { attachment ->
                        mediaAttachmentDao.insertAttachment(attachment)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            val response = com.tfkcolin.meena.data.api.MessageListResponse(
                messages = messages,
                total_count = messages.size
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getMessagesFlow(chatId: String): Flow<List<Message>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        listenerRegistration = firestore.collection(MESSAGES_COLLECTION)
            .whereEqualTo("chatId", chatId)
            .orderBy("timestamp", Query.Direction.ASCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val firebaseMessages = snapshot.documents.mapNotNull { doc ->
                        doc.toObject(FirebaseMessage::class.java)
                    }

                    val messages = firebaseMessages.map { it.toMessage() }

                    // Update local database in a coroutine
                    CoroutineScope(Dispatchers.IO).launch {
                        messages.forEach { message ->
                            try {
                                messageDao.insertMessage(message)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }

                    trySend(messages)
                }
            }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun sendMessage(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String,
        mediaUrl: String?
    ): Result<com.tfkcolin.meena.data.api.MessageResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageId = UUID.randomUUID().toString()
            val firebaseMessage = FirebaseMessage(
                id = messageId,
                chatId = chatId,
                senderId = currentUserId,
                recipientId = recipientId,
                content = content,
                contentType = contentType,
                mediaUrl = mediaUrl,
                hasAttachments = mediaUrl != null,
                timestamp = System.currentTimeMillis(),
                status = "sent",
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            // Send message to Firestore
            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .set(firebaseMessage)
                .await()

            // Update chat's last message
            val chatUpdates = mapOf(
                "lastMessage" to content,
                "lastMessageTimestamp" to System.currentTimeMillis(),
                "updatedAt" to System.currentTimeMillis()
            )
            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .update(chatUpdates)
                .await()

            val message = firebaseMessage.toMessage()
            messageDao.insertMessage(message)

            val response = com.tfkcolin.meena.data.api.MessageResponse(
                message = message
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // Removed duplicate editMessage method - using the one that returns Result<Message>

    private suspend fun deleteMessage(messageId: String, deleteForEveryone: Boolean): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            if (deleteForEveryone) {
                // Delete for everyone
                firestore.collection(MESSAGES_COLLECTION)
                    .document(messageId)
                    .delete()
                    .await()
            } else {
                // Delete for self only
                val messageDoc = firestore.collection(MESSAGES_COLLECTION)
                    .document(messageId)
                    .get()
                    .await()

                val firebaseMessage = messageDoc.toObject(FirebaseMessage::class.java)
                if (firebaseMessage != null) {
                    val updatedDeletedFor = firebaseMessage.deletedFor + currentUserId
                    firestore.collection(MESSAGES_COLLECTION)
                        .document(messageId)
                        .update("deletedFor", updatedDeletedFor)
                        .await()
                }
            }

            messageDao.deleteMessage(messageId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun markMessageAsRead(messageId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageDoc = firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .get()
                .await()

            val firebaseMessage = messageDoc.toObject(FirebaseMessage::class.java)
            if (firebaseMessage != null) {
                val updatedReadBy = firebaseMessage.readBy + (currentUserId to System.currentTimeMillis())
                firestore.collection(MESSAGES_COLLECTION)
                    .document(messageId)
                    .update("readBy", updatedReadBy)
                    .await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateMessageStatus(messageId: String, status: String) {
        try {
            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .update("status", status)
                .await()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun markChatMessagesAsRead(chatId: String, currentUserId: String) {
        try {
            val messagesSnapshot = firestore.collection(MESSAGES_COLLECTION)
                .whereEqualTo("chatId", chatId)
                .whereNotEqualTo("senderId", currentUserId)
                .get()
                .await()

            val batch = firestore.batch()
            messagesSnapshot.documents.forEach { doc ->
                val firebaseMessage = doc.toObject(FirebaseMessage::class.java)
                if (firebaseMessage != null && !firebaseMessage.readBy.containsKey(currentUserId)) {
                    val updatedReadBy = firebaseMessage.readBy + (currentUserId to System.currentTimeMillis())
                    batch.update(doc.reference, "readBy", updatedReadBy)
                }
            }
            batch.commit().await()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun getOrCreateChatWithUser(userId: String): Result<Chat> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Check if chat already exists
            val existingChatSnapshot = firestore.collection(CHATS_COLLECTION)
                .whereEqualTo("conversationType", "one_to_one")
                .whereArrayContains("participantIds", currentUserId)
                .get()
                .await()

            val existingChat = existingChatSnapshot.documents.firstOrNull { doc ->
                val firebaseChat = doc.toObject(FirebaseChat::class.java)
                firebaseChat?.participantIds?.contains(userId) == true
            }

            if (existingChat != null) {
                val firebaseChat = existingChat.toObject(FirebaseChat::class.java)!!
                val chat = firebaseChat.toChat(currentUserId)
                return Result.success(chat)
            }

            // Create new chat
            val createResult = createChat(listOf(userId))
            if (createResult.isSuccess) {
                val chatResponse = createResult.getOrNull()!!
                Result.success(chatResponse.chat)
            } else {
                Result.failure(createResult.exceptionOrNull() ?: Exception("Failed to create chat"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun sendMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>
    ): Result<com.tfkcolin.meena.data.api.MessageResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageId = UUID.randomUUID().toString()

            // Save attachments first
            attachments.forEach { attachment ->
                val firebaseAttachment = FirebaseMediaAttachment(
                    id = attachment.id,
                    messageId = messageId,
                    type = attachment.type,
                    url = attachment.url,
                    thumbnailUrl = attachment.thumbnailUrl,
                    name = attachment.name,
                    size = attachment.size,
                    duration = attachment.duration,
                    width = attachment.width,
                    height = attachment.height,
                    latitude = attachment.latitude,
                    longitude = attachment.longitude,
                    createdAt = System.currentTimeMillis()
                )

                firestore.collection(MEDIA_ATTACHMENTS_COLLECTION)
                    .document(attachment.id)
                    .set(firebaseAttachment)
                    .await()
            }

            // Send message
            val firebaseMessage = FirebaseMessage(
                id = messageId,
                chatId = chatId,
                senderId = currentUserId,
                recipientId = recipientId,
                content = content,
                contentType = if (attachments.isNotEmpty()) attachments.first().type else "text",
                hasAttachments = attachments.isNotEmpty(),
                timestamp = System.currentTimeMillis(),
                status = "sent",
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .set(firebaseMessage)
                .await()

            // Update chat's last message
            val chatUpdates = mapOf(
                "lastMessage" to content,
                "lastMessageTimestamp" to System.currentTimeMillis(),
                "updatedAt" to System.currentTimeMillis()
            )
            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .update(chatUpdates)
                .await()

            val message = firebaseMessage.toMessage()
            message.attachments = attachments
            messageDao.insertMessage(message)

            val response = com.tfkcolin.meena.data.api.MessageResponse(
                message = message
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getAttachmentsForMessage(messageId: String): List<MediaAttachment> {
        return getMessageAttachments(messageId)
    }

    override fun getAttachmentsForMessageFlow(messageId: String): Flow<List<MediaAttachment>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        listenerRegistration = firestore.collection(MEDIA_ATTACHMENTS_COLLECTION)
            .whereEqualTo("messageId", messageId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val attachments = snapshot.documents.mapNotNull { doc ->
                        doc.toObject(FirebaseMediaAttachment::class.java)?.toMediaAttachment()
                    }
                    trySend(attachments)
                }
            }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override suspend fun searchMessages(query: String): List<Message> {
        return try {
            if (query.isBlank()) return emptyList()

            val currentUserId = tokenManager.getUserId() ?: return emptyList()

            // Get user's chats first
            val chatsSnapshot = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .get()
                .await()

            val chatIds = chatsSnapshot.documents.map { it.id }

            // Search messages in user's chats
            val messages = mutableListOf<Message>()
            chatIds.forEach { chatId ->
                val messagesSnapshot = firestore.collection(MESSAGES_COLLECTION)
                    .whereEqualTo("chatId", chatId)
                    .whereGreaterThanOrEqualTo("content", query)
                    .whereLessThanOrEqualTo("content", query + "\uf8ff")
                    .limit(20)
                    .get()
                    .await()

                val chatMessages = messagesSnapshot.documents.mapNotNull { doc ->
                    doc.toObject(FirebaseMessage::class.java)?.toMessage()
                }
                messages.addAll(chatMessages)
            }

            messages
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun searchMessagesInChat(chatId: String, query: String): List<Message> {
        return try {
            if (query.isBlank()) return emptyList()

            val messagesSnapshot = firestore.collection(MESSAGES_COLLECTION)
                .whereEqualTo("chatId", chatId)
                .whereGreaterThanOrEqualTo("content", query)
                .whereLessThanOrEqualTo("content", query + "\uf8ff")
                .limit(20)
                .get()
                .await()

            messagesSnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseMessage::class.java)?.toMessage()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun editMessage(messageId: String, newContent: String): Result<Message> {
        return try {
            val updates = mapOf(
                "content" to newContent,
                "isEdited" to true,
                "updatedAt" to System.currentTimeMillis()
            )

            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .update(updates)
                .await()

            // Get updated message
            val messageDoc = firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .get()
                .await()

            val firebaseMessage = messageDoc.toObject(FirebaseMessage::class.java)
                ?: throw Exception("Failed to get updated message")

            val message = firebaseMessage.toMessage()
            messageDao.updateMessage(message)

            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteMessageForSelf(messageId: String): Result<Unit> {
        return deleteMessage(messageId, false)
    }

    override suspend fun deleteMessageForEveryone(messageId: String): Result<Unit> {
        return deleteMessage(messageId, true)
    }

    override suspend fun archiveChat(chatId: String, isArchived: Boolean): Result<Unit> {
        return try {
            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .update("isArchived", isArchived, "updatedAt", System.currentTimeMillis())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun muteChat(chatId: String, isMuted: Boolean): Result<Unit> {
        return try {
            val updates = mutableMapOf<String, Any>(
                "isMuted" to isMuted,
                "updatedAt" to System.currentTimeMillis()
            )
            if (!isMuted) {
                // Remove mutedUntil field when unmuting
                // Note: Firestore doesn't support setting null, so we'd need to use FieldValue.delete()
                // For now, we'll just not include it in the update
            }

            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .update(updates)
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateChatSettings(
        chatId: String,
        isArchived: Boolean?,
        isMuted: Boolean?,
        isPinned: Boolean?
    ): Result<Unit> {
        return try {
            val updates = mutableMapOf<String, Any>("updatedAt" to System.currentTimeMillis())
            isArchived?.let { updates["isArchived"] = it }
            isMuted?.let {
                updates["isMuted"] = it
                // Note: Firestore doesn't support setting null values
                // We'd need to use FieldValue.delete() to remove the field
            }
            isPinned?.let { updates["isPinned"] = it }

            firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .update(updates)
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getNonArchivedChatsFlow(): Flow<List<Chat>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .whereEqualTo("isArchived", false)
                .orderBy("lastMessageTimestamp", Query.Direction.DESCENDING)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseChats = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseChat::class.java)
                        }
                        val chats = firebaseChats.map { it.toChat(currentUserId) }
                        trySend(chats)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override fun getArchivedChatsFlow(): Flow<List<Chat>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .whereEqualTo("isArchived", true)
                .orderBy("lastMessageTimestamp", Query.Direction.DESCENDING)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseChats = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseChat::class.java)
                        }
                        val chats = firebaseChats.map { it.toChat(currentUserId) }
                        trySend(chats)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    override fun getGroupChatsFlow(): Flow<List<Chat>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CHATS_COLLECTION)
                .whereArrayContains("participantIds", currentUserId)
                .whereEqualTo("conversationType", "group")
                .orderBy("lastMessageTimestamp", Query.Direction.DESCENDING)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val firebaseChats = snapshot.documents.mapNotNull { doc ->
                            doc.toObject(FirebaseChat::class.java)
                        }
                        val chats = firebaseChats.map { it.toChat(currentUserId) }
                        trySend(chats)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    private suspend fun getMessageAttachments(messageId: String): List<MediaAttachment> {
        return try {
            val querySnapshot = firestore.collection(MEDIA_ATTACHMENTS_COLLECTION)
                .whereEqualTo("messageId", messageId)
                .get()
                .await()

            querySnapshot.documents.mapNotNull { doc ->
                doc.toObject(FirebaseMediaAttachment::class.java)?.toMediaAttachment()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    override suspend fun forwardMessage(
        messageId: String,
        chatId: String,
        additionalContent: String?
    ): Result<Message> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get original message
            val originalMessageDoc = firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .get()
                .await()

            val originalMessage = originalMessageDoc.toObject(FirebaseMessage::class.java)
                ?: throw Exception("Original message not found")

            // Create forwarded message
            val forwardedMessageId = UUID.randomUUID().toString()
            val content = if (additionalContent.isNullOrBlank()) {
                originalMessage.content
            } else {
                "$additionalContent\n\n${originalMessage.content}"
            }

            val firebaseMessage = FirebaseMessage(
                id = forwardedMessageId,
                chatId = chatId,
                senderId = currentUserId,
                recipientId = "", // Will be set based on chat participants
                content = content,
                contentType = originalMessage.contentType,
                mediaUrl = originalMessage.mediaUrl,
                hasAttachments = originalMessage.hasAttachments,
                timestamp = System.currentTimeMillis(),
                status = "sent",
                forwardFromMessageId = messageId,
                forwardFromChatId = originalMessage.chatId,
                forwardFromUserId = originalMessage.senderId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            firestore.collection(MESSAGES_COLLECTION)
                .document(forwardedMessageId)
                .set(firebaseMessage)
                .await()

            val message = firebaseMessage.toMessage()
            messageDao.insertMessage(message)

            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addReaction(messageId: String, emoji: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageDoc = firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .get()
                .await()

            val firebaseMessage = messageDoc.toObject(FirebaseMessage::class.java)
                ?: throw Exception("Message not found")

            val currentReactions = firebaseMessage.reactions.toMutableMap()
            val emojiReactions = currentReactions[emoji]?.toMutableList() ?: mutableListOf()

            if (!emojiReactions.contains(currentUserId)) {
                emojiReactions.add(currentUserId)
                currentReactions[emoji] = emojiReactions

                firestore.collection(MESSAGES_COLLECTION)
                    .document(messageId)
                    .update("reactions", currentReactions)
                    .await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun removeReaction(messageId: String, emoji: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageDoc = firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .get()
                .await()

            val firebaseMessage = messageDoc.toObject(FirebaseMessage::class.java)
                ?: throw Exception("Message not found")

            val currentReactions = firebaseMessage.reactions.toMutableMap()
            val emojiReactions = currentReactions[emoji]?.toMutableList()

            if (emojiReactions != null && emojiReactions.contains(currentUserId)) {
                emojiReactions.remove(currentUserId)
                if (emojiReactions.isEmpty()) {
                    currentReactions.remove(emoji)
                } else {
                    currentReactions[emoji] = emojiReactions
                }

                firestore.collection(MESSAGES_COLLECTION)
                    .document(messageId)
                    .update("reactions", currentReactions)
                    .await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun replyToMessage(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String,
        mediaUrl: String?
    ): Result<com.tfkcolin.meena.data.api.MessageResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageId = UUID.randomUUID().toString()
            val firebaseMessage = FirebaseMessage(
                id = messageId,
                chatId = chatId,
                senderId = currentUserId,
                recipientId = recipientId,
                content = content,
                contentType = contentType,
                mediaUrl = mediaUrl,
                hasAttachments = mediaUrl != null,
                timestamp = System.currentTimeMillis(),
                status = "sent",
                replyToMessageId = replyToMessageId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .set(firebaseMessage)
                .await()

            val message = firebaseMessage.toMessage()
            messageDao.insertMessage(message)

            val response = com.tfkcolin.meena.data.api.MessageResponse(
                message = message
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun replyToMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ): Result<com.tfkcolin.meena.data.api.MessageResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val messageId = UUID.randomUUID().toString()

            // Save attachments first
            attachments.forEach { attachment ->
                val firebaseAttachment = FirebaseMediaAttachment(
                    id = attachment.id,
                    messageId = messageId,
                    type = attachment.type,
                    url = attachment.url,
                    thumbnailUrl = attachment.thumbnailUrl,
                    name = attachment.name,
                    size = attachment.size,
                    duration = attachment.duration,
                    width = attachment.width,
                    height = attachment.height,
                    latitude = attachment.latitude,
                    longitude = attachment.longitude,
                    createdAt = System.currentTimeMillis()
                )

                firestore.collection(MEDIA_ATTACHMENTS_COLLECTION)
                    .document(attachment.id)
                    .set(firebaseAttachment)
                    .await()
            }

            val firebaseMessage = FirebaseMessage(
                id = messageId,
                chatId = chatId,
                senderId = currentUserId,
                recipientId = recipientId,
                content = content,
                contentType = if (attachments.isNotEmpty()) attachments.first().type else "text",
                hasAttachments = attachments.isNotEmpty(),
                timestamp = System.currentTimeMillis(),
                status = "sent",
                replyToMessageId = replyToMessageId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            firestore.collection(MESSAGES_COLLECTION)
                .document(messageId)
                .set(firebaseMessage)
                .await()

            val message = firebaseMessage.toMessage()
            message.attachments = attachments
            messageDao.insertMessage(message)

            val response = com.tfkcolin.meena.data.api.MessageResponse(
                message = message
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // Group management methods - simplified implementations for Firebase
    override suspend fun createGroup(
        name: String,
        description: String?,
        privacyType: String,
        initialMembers: List<String>?
    ): Result<String> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val groupId = UUID.randomUUID().toString()
            val allMembers = (initialMembers ?: emptyList()) + currentUserId

            val firebaseChat = FirebaseChat(
                id = groupId,
                conversationType = "group",
                privacyType = privacyType,
                participantIds = allMembers.distinct(),
                name = name,
                description = description,
                adminIds = listOf(currentUserId),
                createdBy = currentUserId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastMessageTimestamp = System.currentTimeMillis()
            )

            firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .set(firebaseChat)
                .await()

            Result.success(groupId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getGroup(groupId: String): Result<GroupResponse> {
        return try {
            val chatDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            val firebaseChat = chatDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Group not found")

            val groupResponse = GroupResponse(
                group_id = firebaseChat.id,
                name = firebaseChat.name ?: "",
                description = firebaseChat.description,
                picture_url = null,
                privacy_type = firebaseChat.privacyType ?: "private",
                creator = com.tfkcolin.meena.data.api.UserInfo(
                    user_id = firebaseChat.createdBy ?: "unknown",
                    user_handle = "user_${firebaseChat.createdBy ?: "unknown"}",
                    display_name = "User ${firebaseChat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = firebaseChat.participantIds.size,
                settings = emptyMap()
            )

            Result.success(groupResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateGroup(
        groupId: String,
        name: String?,
        description: String?,
        pictureUrl: String?
    ): Result<GroupResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the current group to verify permissions
            val groupDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            if (!groupDoc.exists()) {
                throw Exception("Group not found")
            }

            val firebaseChat = groupDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse group")

            // Check if user is admin
            if (!firebaseChat.adminIds.contains(currentUserId)) {
                throw Exception("Only admins can update group")
            }

            // Prepare update data
            val updates = mutableMapOf<String, Any>()
            name?.let { updates["name"] = it }
            description?.let { updates["description"] = it }
            pictureUrl?.let { updates["avatarUrl"] = it }
            updates["updatedAt"] = System.currentTimeMillis()

            // Update the group
            firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .update(updates)
                .await()

            // Get updated group and return response
            val updatedDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            val updatedChat = updatedDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to get updated group")

            val groupResponse = GroupResponse(
                group_id = updatedChat.id,
                name = updatedChat.name ?: "",
                description = updatedChat.description,
                picture_url = updatedChat.avatarUrl,
                privacy_type = updatedChat.privacyType ?: "private",
                creator = com.tfkcolin.meena.data.api.UserInfo(
                    user_id = updatedChat.createdBy ?: "unknown",
                    user_handle = "user_${updatedChat.createdBy ?: "unknown"}",
                    display_name = "User ${updatedChat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = updatedChat.participantIds.size,
                settings = emptyMap()
            )

            Result.success(groupResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getGroupMembers(
        groupId: String,
        limit: Int,
        offset: Int
    ): Result<GroupMembersResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the group
            val groupDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            if (!groupDoc.exists()) {
                throw Exception("Group not found")
            }

            val firebaseChat = groupDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse group")

            // Check if user is a member
            if (!firebaseChat.participantIds.contains(currentUserId)) {
                throw Exception("User is not a member of this group")
            }

            // Get paginated member IDs
            val memberIds = firebaseChat.participantIds.drop(offset).take(limit)

            // Fetch user profiles for members (simplified for now)
            val members = memberIds.map { memberId ->
                com.tfkcolin.meena.data.api.GroupMember(
                    user = com.tfkcolin.meena.data.api.UserInfo(
                        user_id = memberId,
                        user_handle = "user_$memberId",
                        display_name = "User $memberId",
                        avatar_url = null
                    ),
                    role = if (firebaseChat.adminIds.contains(memberId)) "admin" else "member",
                    joined_at = firebaseChat.createdAt
                )
            }

            val response = GroupMembersResponse(
                members = members,
                total_count = firebaseChat.participantIds.size
            )

            Result.success(response)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addGroupMembers(
        groupId: String,
        userHandles: List<String>
    ): Result<GroupResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the group
            val groupDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            if (!groupDoc.exists()) {
                throw Exception("Group not found")
            }

            val firebaseChat = groupDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse group")

            // Check if user is admin
            if (!firebaseChat.adminIds.contains(currentUserId)) {
                throw Exception("Only admins can add members")
            }

            // For now, treat userHandles as userIds (simplified implementation)
            val userIds = userHandles

            if (userIds.isEmpty()) {
                throw Exception("No valid users found to add")
            }

            // Add new members to participant list
            val updatedParticipants = (firebaseChat.participantIds + userIds).distinct()

            // Update the group
            firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .update(
                    mapOf(
                        "participantIds" to updatedParticipants,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            // Return updated group response
            val groupResponse = GroupResponse(
                group_id = firebaseChat.id,
                name = firebaseChat.name ?: "",
                description = firebaseChat.description,
                picture_url = firebaseChat.avatarUrl,
                privacy_type = firebaseChat.privacyType ?: "private",
                creator = com.tfkcolin.meena.data.api.UserInfo(
                    user_id = firebaseChat.createdBy ?: "unknown",
                    user_handle = "user_${firebaseChat.createdBy ?: "unknown"}",
                    display_name = "User ${firebaseChat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = updatedParticipants.size,
                settings = emptyMap()
            )

            Result.success(groupResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun removeGroupMember(
        groupId: String,
        userHandle: String
    ): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the group
            val groupDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            if (!groupDoc.exists()) {
                throw Exception("Group not found")
            }

            val firebaseChat = groupDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse group")

            // Check if user is admin
            if (!firebaseChat.adminIds.contains(currentUserId)) {
                throw Exception("Only admins can remove members")
            }

            // For now, treat userHandle as userId (simplified implementation)
            val userIdToRemove = userHandle

            // Check if user is in the group
            if (!firebaseChat.participantIds.contains(userIdToRemove)) {
                throw Exception("User is not a member of this group")
            }

            // Don't allow removing the group creator
            if (firebaseChat.createdBy == userIdToRemove) {
                throw Exception("Cannot remove the group creator")
            }

            // Remove user from participants and admins
            val updatedParticipants = firebaseChat.participantIds.filter { it != userIdToRemove }
            val updatedAdmins = firebaseChat.adminIds.filter { it != userIdToRemove }

            // Update the group
            firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .update(
                    mapOf(
                        "participantIds" to updatedParticipants,
                        "adminIds" to updatedAdmins,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateGroupMemberRole(
        groupId: String,
        userHandle: String,
        role: String
    ): Result<GroupMemberResponse> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the group
            val groupDoc = firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .get()
                .await()

            if (!groupDoc.exists()) {
                throw Exception("Group not found")
            }

            val firebaseChat = groupDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse group")

            // Check if user is admin
            if (!firebaseChat.adminIds.contains(currentUserId)) {
                throw Exception("Only admins can update member roles")
            }

            // For now, treat userHandle as userId (simplified implementation)
            val targetUserId = userHandle

            // Check if user is in the group
            if (!firebaseChat.participantIds.contains(targetUserId)) {
                throw Exception("User is not a member of this group")
            }

            // Don't allow changing the creator's role
            if (firebaseChat.createdBy == targetUserId) {
                throw Exception("Cannot change the role of the group creator")
            }

            // Update admin list based on role
            val updatedAdmins = when (role.lowercase()) {
                "admin" -> {
                    if (!firebaseChat.adminIds.contains(targetUserId)) {
                        firebaseChat.adminIds + targetUserId
                    } else {
                        firebaseChat.adminIds
                    }
                }
                "member" -> {
                    firebaseChat.adminIds.filter { it != targetUserId }
                }
                else -> throw Exception("Invalid role: $role. Must be 'admin' or 'member'")
            }

            // Update the group
            firestore.collection(CHATS_COLLECTION)
                .document(groupId)
                .update(
                    mapOf(
                        "adminIds" to updatedAdmins,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            // Return member response
            val memberResponse = GroupMemberResponse(
                user = com.tfkcolin.meena.data.api.UserInfo(
                    user_id = targetUserId,
                    user_handle = userHandle,
                    display_name = "User $targetUserId",
                    avatar_url = null
                ),
                role = role.lowercase(),
                joined_at = firebaseChat.createdAt
            )

            Result.success(memberResponse)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // MARK: - Typing Indicators

    /**
     * Send typing indicator for a chat.
     */
    suspend fun sendTypingIndicator(chatId: String, isTyping: Boolean): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            if (isTyping) {
                // Set typing indicator with timestamp
                val typingData = mapOf(
                    "chatId" to chatId,
                    "userId" to currentUserId,
                    "isTyping" to true,
                    "timestamp" to System.currentTimeMillis()
                )

                firestore.collection(TYPING_INDICATORS_COLLECTION)
                    .document("${chatId}_${currentUserId}")
                    .set(typingData)
                    .await()
            } else {
                // Remove typing indicator
                firestore.collection(TYPING_INDICATORS_COLLECTION)
                    .document("${chatId}_${currentUserId}")
                    .delete()
                    .await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get typing indicators for a chat as a flow.
     */
    fun getTypingIndicatorsFlow(chatId: String): Flow<List<String>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(TYPING_INDICATORS_COLLECTION)
                .whereEqualTo("chatId", chatId)
                .whereEqualTo("isTyping", true)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val typingUsers = snapshot.documents.mapNotNull { doc ->
                            val data = doc.data
                            val userId = data?.get("userId") as? String
                            if (userId != null && userId != currentUserId) userId else null
                        }

                        trySend(typingUsers)
                    }
                }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    // MARK: - Presence Status Management

    /**
     * Update user presence status.
     */
    suspend fun updatePresenceStatus(isOnline: Boolean, status: String = "online"): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val presence = mapOf(
                "userId" to currentUserId,
                "isOnline" to isOnline,
                "lastSeen" to System.currentTimeMillis(),
                "status" to if (isOnline) status else "offline"
            )

            firestore.collection(PRESENCE_COLLECTION)
                .document(currentUserId)
                .set(presence)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get presence status for multiple users.
     */
    suspend fun getPresenceStatus(userIds: List<String>): Result<Map<String, Map<String, Any>>> {
        return try {
            if (userIds.isEmpty()) {
                return Result.success(emptyMap())
            }

            val presenceMap = mutableMapOf<String, Map<String, Any>>()

            // Firestore 'in' queries are limited to 10 items, so we need to batch
            userIds.chunked(10).forEach { batch ->
                val querySnapshot = firestore.collection(PRESENCE_COLLECTION)
                    .whereIn("userId", batch)
                    .get()
                    .await()

                querySnapshot.documents.forEach { doc ->
                    val data = doc.data
                    if (data != null) {
                        val userId = data["userId"] as? String
                        if (userId != null) {
                            presenceMap[userId] = data
                        }
                    }
                }
            }

            Result.success(presenceMap)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get presence status for users in a chat as a flow.
     */
    fun getPresenceStatusFlow(chatId: String): Flow<Map<String, Map<String, Any>>> = callbackFlow {
        var chatListenerRegistration: ListenerRegistration? = null
        var presenceListenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            // First, get the chat to know the participants
            chatListenerRegistration = firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .addSnapshotListener { chatSnapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (chatSnapshot != null && chatSnapshot.exists()) {
                        val firebaseChat = chatSnapshot.toObject(FirebaseChat::class.java)
                        if (firebaseChat != null) {
                            val participantIds = firebaseChat.participantIds.filter { it != currentUserId }

                            if (participantIds.isNotEmpty()) {
                                // Listen to presence updates for participants
                                presenceListenerRegistration?.remove()
                                presenceListenerRegistration = firestore.collection(PRESENCE_COLLECTION)
                                    .whereIn("userId", participantIds.take(10)) // Firestore limit
                                    .addSnapshotListener { presenceSnapshot, presenceError ->
                                        if (presenceError != null) {
                                            close(presenceError)
                                            return@addSnapshotListener
                                        }

                                        if (presenceSnapshot != null) {
                                            val presenceMap = mutableMapOf<String, Map<String, Any>>()
                                            presenceSnapshot.documents.forEach { doc ->
                                                val data = doc.data
                                                if (data != null) {
                                                    val userId = data["userId"] as? String
                                                    if (userId != null) {
                                                        presenceMap[userId] = data
                                                    }
                                                }
                                            }
                                            trySend(presenceMap)
                                        }
                                    }
                            } else {
                                trySend(emptyMap())
                            }
                        }
                    }
                }
        } else {
            trySend(emptyMap())
        }

        awaitClose {
            chatListenerRegistration?.remove()
            presenceListenerRegistration?.remove()
        }
    }

    // MARK: - Enhanced Real-time Flows

    /**
     * Get real-time updates for group membership changes.
     */
    fun getGroupMembershipFlow(groupId: String): Flow<List<String>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        listenerRegistration = firestore.collection(CHATS_COLLECTION)
            .document(groupId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    val firebaseChat = snapshot.toObject(FirebaseChat::class.java)
                    if (firebaseChat != null) {
                        trySend(firebaseChat.participantIds)
                    }
                }
            }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    /**
     * Get real-time updates for group admin changes.
     */
    fun getGroupAdminsFlow(groupId: String): Flow<List<String>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        listenerRegistration = firestore.collection(CHATS_COLLECTION)
            .document(groupId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    val firebaseChat = snapshot.toObject(FirebaseChat::class.java)
                    if (firebaseChat != null) {
                        trySend(firebaseChat.adminIds)
                    }
                }
            }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    /**
     * Get enhanced chat flow with real-time group/channel updates.
     */
    fun getEnhancedChatFlow(chatId: String): Flow<Chat?> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            listenerRegistration = firestore.collection(CHATS_COLLECTION)
                .document(chatId)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null && snapshot.exists()) {
                        val firebaseChat = snapshot.toObject(FirebaseChat::class.java)
                        if (firebaseChat != null) {
                            val chat = firebaseChat.toChat(currentUserId)

                            // Update local database in a coroutine
                            CoroutineScope(Dispatchers.IO).launch {
                                try {
                                    chatDao.insertChat(chat)
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }

                            trySend(chat)
                        }
                    } else {
                        trySend(null)
                    }
                }
        } else {
            trySend(null)
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }

    /**
     * Get real-time channel subscriber count updates.
     */
    fun getChannelSubscriberCountFlow(channelId: String): Flow<Int> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        listenerRegistration = firestore.collection(CHATS_COLLECTION)
            .document(channelId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    val firebaseChat = snapshot.toObject(FirebaseChat::class.java)
                    if (firebaseChat != null && firebaseChat.conversationType == "channel") {
                        // For now, use participantIds size as subscriber count
                        trySend(firebaseChat.participantIds.size)
                    }
                }
            }

        awaitClose {
            listenerRegistration.remove()
        }
    }

    // MARK: - Channel Operations

    /**
     * Create a channel (similar to group but with different settings).
     */
    suspend fun createChannel(
        name: String,
        description: String?,
        category: String?,
        isPublic: Boolean = true
    ): Result<String> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            val channelId = UUID.randomUUID().toString()

            val firebaseChat = FirebaseChat(
                id = channelId,
                conversationType = "channel",
                privacyType = if (isPublic) "public" else "private",
                participantIds = listOf(currentUserId),
                name = name,
                description = description,
                adminIds = listOf(currentUserId),
                createdBy = currentUserId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastMessageTimestamp = System.currentTimeMillis()
            )

            firestore.collection(CHATS_COLLECTION)
                .document(channelId)
                .set(firebaseChat)
                .await()

            Result.success(channelId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Subscribe to a channel.
     */
    suspend fun subscribeToChannel(channelId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the channel
            val channelDoc = firestore.collection(CHATS_COLLECTION)
                .document(channelId)
                .get()
                .await()

            if (!channelDoc.exists()) {
                throw Exception("Channel not found")
            }

            val firebaseChat = channelDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse channel")

            if (firebaseChat.conversationType != "channel") {
                throw Exception("This is not a channel")
            }

            // Check if already subscribed
            if (firebaseChat.participantIds.contains(currentUserId)) {
                throw Exception("Already subscribed to this channel")
            }

            // Add user to participants
            val updatedParticipants = firebaseChat.participantIds + currentUserId

            firestore.collection(CHATS_COLLECTION)
                .document(channelId)
                .update(
                    mapOf(
                        "participantIds" to updatedParticipants,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Unsubscribe from a channel.
     */
    suspend fun unsubscribeFromChannel(channelId: String): Result<Unit> {
        return try {
            val currentUserId = tokenManager.getUserId()
                ?: throw Exception("No authenticated user")

            // Get the channel
            val channelDoc = firestore.collection(CHATS_COLLECTION)
                .document(channelId)
                .get()
                .await()

            if (!channelDoc.exists()) {
                throw Exception("Channel not found")
            }

            val firebaseChat = channelDoc.toObject(FirebaseChat::class.java)
                ?: throw Exception("Failed to parse channel")

            if (firebaseChat.conversationType != "channel") {
                throw Exception("This is not a channel")
            }

            // Check if subscribed
            if (!firebaseChat.participantIds.contains(currentUserId)) {
                throw Exception("Not subscribed to this channel")
            }

            // Don't allow channel creator to unsubscribe
            if (firebaseChat.createdBy == currentUserId) {
                throw Exception("Channel creator cannot unsubscribe")
            }

            // Remove user from participants
            val updatedParticipants = firebaseChat.participantIds.filter { it != currentUserId }

            firestore.collection(CHATS_COLLECTION)
                .document(channelId)
                .update(
                    mapOf(
                        "participantIds" to updatedParticipants,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get channels flow for discovery.
     */
    fun getPublicChannelsFlow(category: String? = null): Flow<List<Chat>> = callbackFlow {
        var listenerRegistration: ListenerRegistration? = null

        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            val query = firestore.collection(CHATS_COLLECTION)
                .whereEqualTo("conversationType", "channel")
                .whereEqualTo("privacyType", "public")
                .orderBy("createdAt", Query.Direction.DESCENDING)

            listenerRegistration = query.addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val firebaseChats = snapshot.documents.mapNotNull { doc ->
                        doc.toObject(FirebaseChat::class.java)
                    }
                    val chats = firebaseChats.map { it.toChat(currentUserId) }
                    trySend(chats)
                }
            }
        } else {
            trySend(emptyList())
        }

        awaitClose {
            listenerRegistration?.remove()
        }
    }
}

/**
 * Extension functions to convert Firebase models to local models.
 */
private fun FirebaseChat.toChat(currentUserId: String): Chat {
    return Chat(
        id = this.id,
        conversationType = this.conversationType,
        privacyType = this.privacyType,
        participantIds = this.participantIds.joinToString(","),
        name = this.name,
        description = this.description,
        avatarUrl = this.avatarUrl,
        adminIds = this.adminIds.joinToString(","),
        createdBy = this.createdBy,
        lastMessage = this.lastMessage,
        lastMessageTimestamp = this.lastMessageTimestamp,
        unreadCount = this.unreadCounts[currentUserId] ?: 0,
        isArchived = this.isArchived,
        isMuted = this.isMuted,
        isPinned = this.isPinned,
        mutedUntil = this.mutedUntil,
        createdAt = this.createdAt
    )
}

private fun FirebaseMessage.toMessage(): Message {
    return Message(
        id = this.id,
        chatId = this.chatId,
        senderId = this.senderId,
        recipientId = this.recipientId,
        content = this.content,
        contentType = this.contentType,
        mediaUrl = this.mediaUrl,
        hasAttachments = this.hasAttachments,
        timestamp = this.timestamp,
        status = this.status,
        isEdited = this.isEdited,
        deletedFor = if (this.deletedFor.isEmpty()) null else this.deletedFor.joinToString(","),
        replyToMessageId = this.replyToMessageId,
        forwardFromMessageId = this.forwardFromMessageId,
        forwardFromChatId = this.forwardFromChatId,
        forwardFromUserId = this.forwardFromUserId,
        reactions = this.reactions
    )
}

private fun FirebaseMediaAttachment.toMediaAttachment(): MediaAttachment {
    return MediaAttachment(
        id = this.id,
        messageId = this.messageId,
        type = this.type,
        url = this.url,
        thumbnailUrl = this.thumbnailUrl,
        name = this.name,
        size = this.size,
        duration = this.duration,
        width = this.width,
        height = this.height,
        latitude = this.latitude,
        longitude = this.longitude
    )
}
