package com.tfkcolin.meena.data.local.typeconverters

import androidx.room.TypeConverter

/**
 * Type converter for converting between List<String> and String.
 */
class ListStringConverter {

    /**
     * Convert a list of strings to a comma-separated string.
     *
     * @param list The list of strings to convert.
     * @return The comma-separated string.
     */
    @TypeConverter
    fun fromList(list: List<String>?): String? {
        return list?.joinToString(",")
    }

    /**
     * Convert a comma-separated string to a list of strings.
     *
     * @param string The comma-separated string to convert.
     * @return The list of strings.
     */
    @TypeConverter
    fun toList(string: String?): List<String> {
        return string?.split(",")?.filter { it.isNotEmpty() } ?: emptyList()
    }
}
