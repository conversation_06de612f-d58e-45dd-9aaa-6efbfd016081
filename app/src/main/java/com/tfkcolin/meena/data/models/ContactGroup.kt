package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Contact group model representing a group of contacts.
 * This model is used for both API responses and local database storage.
 */
@Entity(
    tableName = "contact_groups",
    foreignKeys = [
        ForeignKey(
            entity = User::class,
            parentColumns = ["userId"],
            childColumns = ["ownerId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("ownerId")
    ]
)
data class ContactGroup(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("name")
    val name: String,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("owner_id")
    val ownerId: String,

    @SerializedName("contact_ids")
    val contactIds: List<String> = emptyList(),

    @SerializedName("created_at")
    val createdAt: Long,

    @SerializedName("updated_at")
    val updatedAt: Long
) {
    /**
     * Get the number of contacts in the group.
     *
     * @return The number of contacts.
     */
    val contactCount: Int
        get() = contactIds.size
}
