package com.tfkcolin.meena.data.repositories

import com.google.gson.Gson
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.domain.repositories.IContactRepository
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for contact operations.
 */
@Singleton
class ContactRepository @Inject constructor(
    private val contactApi: ContactApi,
    private val contactDao: ContactDao,
    private val tokenManager: TokenManager,
    private val gson: <PERSON><PERSON>,
    private val errorHandler: ErrorHandler
) : IContactRepository, BaseRepository {

    override suspend fun getContacts(
        limit: Int,
        offset: Int,
        relationship: String?
    ): Result<ContactListResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.getContacts(
                authToken = "Bearer $token",
                limit = limit,
                offset = offset,
                relationship = relationship
            )

            if (response.isSuccessful && response.body() != null) {
                val contactListResponse = response.body()!!

                // Save contacts to database
                executeDatabaseOperation {
                    contactDao.insertContacts(contactListResponse.contacts)
                }

                contactListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override fun getContactsFlow(): Flow<List<Contact>> {
        val userId = tokenManager.getUserId() ?: return emptyFlow()
        return contactDao.getContactsFlow(userId)
            .asResourceFlow()
            .map { resource ->
                when (resource) {
                    is Resource.Success -> resource.data
                    is Resource.Loading -> emptyList()
                    is Resource.Error -> {
                        errorHandler.setGlobalError(resource.error)
                        emptyList()
                    }
                }
            }
    }

    override fun getContactsByRelationshipFlow(relationship: String): Flow<List<Contact>> {
        val userId = tokenManager.getUserId() ?: return emptyFlow()
        return contactDao.getContactsByRelationshipFlow(userId, relationship)
            .asResourceFlow()
            .map { resource ->
                when (resource) {
                    is Resource.Success -> resource.data
                    is Resource.Loading -> emptyList()
                    is Resource.Error -> {
                        errorHandler.setGlobalError(resource.error)
                        emptyList()
                    }
                }
            }
    }

    override suspend fun getContactById(contactId: String): Result<ContactResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.getContactById(
                authToken = "Bearer $token",
                contactId = contactId
            )

            if (response.isSuccessful && response.body() != null) {
                val contactResponse = response.body()!!

                // Save contact to database
                executeDatabaseOperation {
                    contactDao.insertContact(contactResponse.toContact())
                }

                contactResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun addContact(
        userHandle: String,
        displayName: String?,
        notes: String?
    ): Result<ContactResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val request = AddContactRequest(
                userHandle = userHandle,
                displayName = displayName,
                notes = notes
            )

            val response = contactApi.addContact(
                authToken = "Bearer $token",
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val contactResponse = response.body()!!

                // Save contact to database
                executeDatabaseOperation {
                    contactDao.insertContact(contactResponse.toContact())
                }

                contactResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun updateContact(
        contactId: String,
        displayName: String?,
        notes: String?,
        relationship: String?
    ): Result<ContactResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val request = UpdateContactRequest(
                displayName = displayName,
                notes = notes,
                relationship = relationship
            )

            val response = contactApi.updateContact(
                authToken = "Bearer $token",
                contactId = contactId,
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val contactResponse = response.body()!!

                // Update contact in database
                executeDatabaseOperation {
                    contactDao.insertContact(contactResponse.toContact())
                }

                contactResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun deleteContact(contactId: String): Result<Unit> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.deleteContact(
                authToken = "Bearer $token",
                contactId = contactId
            )

            if (response.isSuccessful) {
                // Delete contact from database
                executeDatabaseOperation {
                    contactDao.deleteContact(contactId)
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun searchContacts(
        query: String,
        limit: Int,
        offset: Int
    ): Result<ContactListResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.searchContacts(
                authToken = "Bearer $token",
                query = query,
                limit = limit,
                offset = offset
            )

            if (response.isSuccessful && response.body() != null) {
                val contactListResponse = response.body()!!

                // Save contacts to database
                executeDatabaseOperation {
                    contactDao.insertContacts(contactListResponse.contacts)
                }

                contactListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun getRecentContacts(limit: Int): Result<ContactListResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.getRecentContacts(
                authToken = "Bearer $token",
                limit = limit
            )

            if (response.isSuccessful && response.body() != null) {
                val contactListResponse = response.body()!!

                // Save contacts to database
                executeDatabaseOperation {
                    contactDao.insertContacts(contactListResponse.contacts)
                }

                contactListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun getFavoriteContacts(
        limit: Int,
        offset: Int
    ): Result<ContactListResponse> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.getFavoriteContacts(
                authToken = "Bearer $token",
                limit = limit,
                offset = offset
            )

            if (response.isSuccessful && response.body() != null) {
                val contactListResponse = response.body()!!

                // Save contacts to database
                executeDatabaseOperation {
                    contactDao.insertContacts(contactListResponse.contacts)
                }

                contactListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun addToFavorites(contactId: String): Result<Unit> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.addToFavorites(
                authToken = "Bearer $token",
                contactId = contactId
            )

            if (response.isSuccessful) {
                // Update contact in database
                val contact = contactDao.getContactById(contactId)
                if (contact != null) {
                    // We don't have a "favorite" field in the Contact model yet,
                    // so we'll need to add it in a future update
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun removeFromFavorites(contactId: String): Result<Unit> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            val response = contactApi.removeFromFavorites(
                authToken = "Bearer $token",
                contactId = contactId
            )

            if (response.isSuccessful) {
                // Update contact in database
                val contact = contactDao.getContactById(contactId)
                if (contact != null) {
                    // We don't have a "favorite" field in the Contact model yet,
                    // so we'll need to add it in a future update
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun blockContact(contactId: String): Result<Unit> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            // Update the contact's relationship to "blocked"
            val request = UpdateContactRequest(relationship = "blocked")

            val response = contactApi.updateContact(
                authToken = "Bearer $token",
                contactId = contactId,
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val contactResponse = response.body()!!

                // Update contact in database
                executeDatabaseOperation {
                    contactDao.insertContact(contactResponse.toContact())
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun unblockContact(contactId: String): Result<Unit> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            // Update the contact's relationship to "friend"
            val request = UpdateContactRequest(relationship = "friend")

            val response = contactApi.updateContact(
                authToken = "Bearer $token",
                contactId = contactId,
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val contactResponse = response.body()!!

                // Update contact in database
                executeDatabaseOperation {
                    contactDao.insertContact(contactResponse.toContact())
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }
}
