package com.tfkcolin.meena.data.services

import android.content.Context
import android.net.Uri
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.DownloadResult
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.UploadProgress
import com.tfkcolin.meena.data.models.UploadResult
import com.tfkcolin.meena.utils.FileUtils
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for media service operations.
 */
interface MediaService {
    /**
     * Upload a media file.
     *
     * @param uri The URI of the file to upload.
     * @param contentType The content type of the file.
     * @return The result of the upload operation.
     */
    suspend fun uploadMedia(uri: Uri, contentType: String): Result<MediaAttachment>

    /**
     * Download a media file.
     *
     * @param url The URL of the file to download.
     * @param fileName The name to save the file as.
     * @return The result of the download operation.
     */
    suspend fun downloadMedia(url: String, fileName: String): Result<File>

    /**
     * Observe the progress of an upload operation.
     *
     * @param id The ID of the upload operation.
     * @return A flow of upload progress updates.
     */
    fun observeUploadProgress(id: String): Flow<UploadProgress>

    /**
     * Observe the progress of a download operation.
     *
     * @param id The ID of the download operation.
     * @return A flow of download progress updates.
     */
    fun observeDownloadProgress(id: String): Flow<DownloadProgress>

    /**
     * Get all media attachments for a message.
     *
     * @param messageId The ID of the message.
     * @return A flow of media attachments.
     */
    fun getAttachmentsForMessage(messageId: String): Flow<List<MediaAttachment>>

    /**
     * Cancel an upload operation.
     *
     * @param id The ID of the upload operation.
     * @return True if the operation was cancelled, false otherwise.
     */
    suspend fun cancelUpload(id: String): Boolean

    /**
     * Cancel a download operation.
     *
     * @param id The ID of the download operation.
     * @return True if the operation was cancelled, false otherwise.
     */
    suspend fun cancelDownload(id: String): Boolean
}

/**
 * Implementation of the MediaService interface.
 */
@Singleton
class MediaServiceImpl @Inject constructor(
    private val context: Context,
    private val mediaApi: MediaApi,
    private val mediaAttachmentDao: MediaAttachmentDao,
    private val mediaUploadService: MediaUploadService,
    private val mediaDownloadService: MediaDownloadService,
    private val tokenManager: TokenManager,
    private val fileUtils: FileUtils
) : MediaService {

    // Map of upload IDs to progress flows
    private val uploadProgressFlows = mutableMapOf<String, MutableStateFlow<UploadProgress>>()

    // Map of download IDs to progress flows
    private val downloadProgressFlows = mutableMapOf<String, MutableStateFlow<DownloadProgress>>()

    override suspend fun uploadMedia(uri: Uri, contentType: String): Result<MediaAttachment> {
        // Create a unique ID for this upload
        val uploadId = UUID.randomUUID().toString()

        // Create a progress flow for this upload
        val progressFlow = MutableStateFlow<UploadProgress>(UploadProgress.Preparing(uploadId))
        uploadProgressFlows[uploadId] = progressFlow

        // Start the upload
        return try {
            // Use the mediaUploadService to upload the media
            val result = mediaUploadService.uploadMedia(uri, "message_media")

            when (result) {
                is UploadResult.Success -> {
                    // Update progress to complete
                    progressFlow.value = UploadProgress.Complete(uploadId)

                    // Return the media attachment
                    Result.success(result.attachment)
                }
                is UploadResult.Error -> {
                    // Update progress with error
                    progressFlow.value = UploadProgress.Error(uploadId, result.error)

                    // Return the error
                    Result.failure(Exception(result.error))
                }
            }
        } catch (e: Exception) {
            // Update progress with error
            progressFlow.value = UploadProgress.Error(uploadId, e.message ?: "Unknown error")

            // Return the error
            Result.failure(e)
        }
    }

    override suspend fun downloadMedia(url: String, fileName: String): Result<File> {
        // Create a unique ID for this download
        val downloadId = UUID.randomUUID().toString()

        // Create a progress flow for this download
        val progressFlow = MutableStateFlow<DownloadProgress>(DownloadProgress.Preparing(downloadId))
        downloadProgressFlows[downloadId] = progressFlow

        // Create a dummy attachment for the download service
        val attachment = MediaAttachment(
            id = downloadId,
            messageId = "",
            type = "document",
            url = url,
            name = fileName
        )

        // Start the download
        return try {
            // Use the mediaDownloadService to download the media
            val result = mediaDownloadService.downloadMedia(attachment, false)

            when (result) {
                is DownloadResult.Success -> {
                    // Update progress to complete
                    progressFlow.value = DownloadProgress.Complete(downloadId)

                    // Return the file
                    Result.success(result.file)
                }
                is DownloadResult.Error -> {
                    // Update progress with error
                    progressFlow.value = DownloadProgress.Error(downloadId, result.error)

                    // Return the error
                    Result.failure(Exception(result.error))
                }
            }
        } catch (e: Exception) {
            // Update progress with error
            progressFlow.value = DownloadProgress.Error(downloadId, e.message ?: "Unknown error")

            // Return the error
            Result.failure(e)
        }
    }

    override fun observeUploadProgress(id: String): Flow<UploadProgress> {
        return uploadProgressFlows[id]?.asStateFlow() ?: MutableStateFlow(UploadProgress.Error(id, "Upload not found"))
    }

    override fun observeDownloadProgress(id: String): Flow<DownloadProgress> {
        return downloadProgressFlows[id]?.asStateFlow() ?: MutableStateFlow(DownloadProgress.Error(id, "Download not found"))
    }

    override fun getAttachmentsForMessage(messageId: String): Flow<List<MediaAttachment>> {
        return mediaAttachmentDao.getAttachmentsForMessageFlow(messageId)
    }

    override suspend fun cancelUpload(id: String): Boolean {
        // Call the MediaUploadService's cancelUpload method
        val result = mediaUploadService.cancelUpload(id)

        // Update our local progress flow
        if (result) {
            uploadProgressFlows[id]?.value = UploadProgress.Error(id, "Cancelled")
        }

        return result
    }

    override suspend fun cancelDownload(id: String): Boolean {
        // Call the MediaDownloadService's cancelDownload method
        val result = mediaDownloadService.cancelDownload(id)

        // Update our local progress flow
        if (result) {
            downloadProgressFlows[id]?.value = DownloadProgress.Error(id, "Cancelled")
        }

        return result
    }
}
