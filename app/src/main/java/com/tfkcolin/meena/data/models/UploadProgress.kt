package com.tfkcolin.meena.data.models

/**
 * Represents the progress of a media upload.
 */
sealed class UploadProgress {
    /**
     * The ID of the upload.
     */
    abstract val id: String

    /**
     * Whether the upload is complete.
     */
    abstract val isComplete: Boolean

    /**
     * The upload is being prepared.
     *
     * @param id The ID of the upload.
     */
    data class Preparing(
        override val id: String
    ) : UploadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The upload URL is being requested from the server.
     *
     * @param id The ID of the upload.
     */
    data class RequestingUploadUrl(
        override val id: String
    ) : UploadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The upload is in progress.
     *
     * @param id The ID of the upload.
     * @param progress The upload progress as a percentage (0-100).
     */
    data class Uploading(
        override val id: String,
        val progress: Int
    ) : UploadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The upload is being completed.
     *
     * @param id The ID of the upload.
     */
    data class Completing(
        override val id: String
    ) : UploadProgress() {
        override val isComplete: Boolean = false
    }

    /**
     * The upload is complete.
     *
     * @param id The ID of the upload.
     */
    data class Complete(
        override val id: String
    ) : UploadProgress() {
        override val isComplete: Boolean = true
    }

    /**
     * The upload failed.
     *
     * @param id The ID of the upload.
     * @param error The error message.
     */
    data class Error(
        override val id: String,
        val error: String
    ) : UploadProgress() {
        override val isComplete: Boolean = true
    }

    companion object {
        /**
         * Create an UploadProgress object from a progress value.
         *
         * @param id The ID of the upload.
         * @param progress The progress of the upload (0-100).
         * @param isComplete Whether the upload is complete.
         * @param error The error message, if any.
         * @return The appropriate UploadProgress object.
         */
        fun create(id: String, progress: Int, isComplete: Boolean, error: String? = null): UploadProgress {
            return when {
                error != null -> Error(id, error)
                isComplete -> Complete(id)
                progress == 0 -> Preparing(id)
                progress == 100 -> Completing(id)
                else -> Uploading(id, progress)
            }
        }
    }
}
