package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Update profile request model for the /users/me endpoint.
 * This matches the backend's UpdateProfileRequest structure.
 */
data class UpdateProfileRequest(
    @SerializedName("display_name")
    val displayName: String? = null,

    @SerializedName("bio")
    val bio: String? = null,

    @SerializedName("avatar_url")
    val avatarUrl: String? = null,

    @SerializedName("email")
    val email: String? = null,

    @SerializedName("phone_number")
    val phoneNumber: String? = null
)
