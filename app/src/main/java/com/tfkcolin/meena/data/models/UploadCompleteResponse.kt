package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Response from the server after an upload is complete.
 */
data class UploadCompleteResponse(
    @SerializedName("media_id")
    val mediaId: String,
    
    @SerializedName("file_url")
    val fileUrl: String,
    
    @SerializedName("thumbnail_url")
    val thumbnailUrl: String? = null,
    
    @SerializedName("file_name")
    val fileName: String,
    
    @SerializedName("file_size")
    val fileSize: Long,
    
    @SerializedName("mime_type")
    val mimeType: String,
    
    @SerializedName("width")
    val width: Int? = null,
    
    @SerializedName("height")
    val height: Int? = null,
    
    @SerializedName("duration")
    val duration: Long? = null
)
