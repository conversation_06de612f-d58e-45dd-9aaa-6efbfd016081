package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.ChunkedUploadInitRequest
import com.tfkcolin.meena.data.models.ChunkedUploadInitResponse
import com.tfkcolin.meena.data.models.ChunkedUploadStatusResponse
import com.tfkcolin.meena.data.models.ChunkUploadResponse
import com.tfkcolin.meena.data.models.EncryptedMediaUploadRequest
import com.tfkcolin.meena.data.models.EncryptedMediaUploadResponse
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaUploadResponse
import com.tfkcolin.meena.data.models.UploadCompleteResponse
import com.tfkcolin.meena.data.models.UploadRequest
import com.tfkcolin.meena.data.models.UploadUrlResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Url

/**
 * API interface for media operations.
 */
interface MediaApi {

    /**
     * Request a pre-signed URL for uploading media.
     *
     * @param request The upload request.
     * @return The upload URL response.
     */
    @POST("/api/v1/media/upload/request")
    suspend fun requestUploadUrl(@Body request: UploadRequest): UploadUrlResponse

    /**
     * Notify the server that an upload is complete.
     *
     * @param mediaId The media ID.
     * @return The upload complete response.
     */
    @POST("/api/v1/media/upload/complete")
    suspend fun completeUpload(@Path("mediaId") mediaId: String): UploadCompleteResponse

    /**
     * Upload a media file.
     *
     * @param authToken The authentication token.
     * @param file The file to upload.
     * @return The uploaded media attachment.
     */
    @Multipart
    @POST("api/v1/media/upload")
    suspend fun uploadMedia(
        @Header("Authorization") authToken: String,
        @Part file: MultipartBody.Part
    ): Response<MediaUploadResponse>

    /**
     * Upload a media file with a custom request body.
     *
     * @param authToken The authentication token.
     * @param requestBody The request body containing the file.
     * @return The uploaded media attachment.
     */
    @POST("api/v1/media/upload")
    suspend fun uploadMedia(
        @Header("Authorization") authToken: String,
        @Body requestBody: RequestBody
    ): Response<MediaAttachment>

    /**
     * Get a pre-signed URL for uploading an encrypted media file.
     *
     * @param authToken The authentication token.
     * @param request The encrypted media upload request.
     * @return The upload URL response.
     */
    @POST("api/v1/media/upload/encrypted")
    suspend fun getEncryptedUploadUrl(
        @Header("Authorization") authToken: String,
        @Body request: EncryptedMediaUploadRequest
    ): Response<EncryptedMediaUploadResponse>

    /**
     * Convenience method for getting a pre-signed URL for uploading an encrypted media file.
     *
     * @param authToken The authentication token.
     * @param fileName The name of the encrypted file.
     * @param contentType The content type of the encrypted file.
     * @param fileSize The size of the encrypted file in bytes.
     * @return The upload URL response.
     */
    suspend fun getEncryptedUploadUrl(
        authToken: String,
        fileName: String,
        contentType: String,
        fileSize: Long
    ): Response<EncryptedMediaUploadResponse> {
        val request = EncryptedMediaUploadRequest(
            fileName = fileName,
            contentType = contentType,
            fileSize = fileSize,
            isEncrypted = true
        )
        return getEncryptedUploadUrl(authToken, request)
    }

    /**
     * Initialize a chunked upload.
     *
     * @param authToken The authentication token.
     * @param request The chunked upload initialization request.
     * @return The chunked upload initialization response.
     */
    @POST("api/v1/media/upload/chunked/init")
    suspend fun initiateChunkedUpload(
        @Header("Authorization") authToken: String,
        @Body request: ChunkedUploadInitRequest
    ): Response<ChunkedUploadInitResponse>

    /**
     * Upload a chunk of a file.
     *
     * @param authToken The authentication token.
     * @param uploadId The ID of the upload session.
     * @param chunkIndex The index of the chunk.
     * @param chunk The chunk data.
     * @return The chunk upload response.
     */
    @PUT("api/v1/media/upload/chunked/{upload_id}/{chunk_index}")
    suspend fun uploadChunk(
        @Header("Authorization") authToken: String,
        @Path("upload_id") uploadId: String,
        @Path("chunk_index") chunkIndex: Int,
        @Body chunk: RequestBody
    ): Response<ChunkUploadResponse>

    /**
     * Get the status of a chunked upload.
     *
     * @param authToken The authentication token.
     * @param uploadId The ID of the upload session.
     * @return The chunked upload status response.
     */
    @GET("api/v1/media/upload/chunked/{upload_id}/status")
    suspend fun getChunkedUploadStatus(
        @Header("Authorization") authToken: String,
        @Path("upload_id") uploadId: String
    ): Response<ChunkedUploadStatusResponse>

    /**
     * Complete a chunked upload.
     *
     * @param authToken The authentication token.
     * @param uploadId The ID of the upload session.
     * @return The completed media attachment.
     */
    @POST("api/v1/media/upload/chunked/{upload_id}/complete")
    suspend fun completeChunkedUpload(
        @Header("Authorization") authToken: String,
        @Path("upload_id") uploadId: String
    ): Response<MediaAttachment>

    /**
     * Get a media file by ID.
     *
     * @param authToken The authentication token.
     * @param mediaId The ID of the media to get.
     * @return The media attachment.
     */
    @GET("api/v1/media/{media_id}")
    suspend fun getMedia(
        @Header("Authorization") authToken: String,
        @Path("media_id") mediaId: String
    ): Response<MediaAttachment>

    /**
     * Delete a media file by ID.
     *
     * @param authToken The authentication token.
     * @param mediaId The ID of the media to delete.
     * @return An empty response.
     */
    @DELETE("api/v1/media/{media_id}")
    suspend fun deleteMedia(
        @Header("Authorization") authToken: String,
        @Path("media_id") mediaId: String
    ): Response<Void>
}
