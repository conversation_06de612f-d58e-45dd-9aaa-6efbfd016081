package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.*
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * API interface for authentication endpoints.
 */
interface AuthApi {

    /**
     * Register a new user.
     *
     * @param request The registration request.
     * @return The authentication response containing tokens and user information.
     */
    @POST("/api/v1/auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<AuthResponse>

    /**
     * Log in a user.
     *
     * @param request The login request.
     * @return The authentication response containing tokens and user information.
     */
    @POST("/api/v1/auth/login")
    suspend fun login(@Body request: LoginRequest): Response<AuthResponse>

    /**
     * Complete two-factor authentication.
     *
     * @param request The two-factor authentication request.
     * @return The authentication response containing tokens and user information.
     */
    @POST("/api/v1/auth/login/2fa")
    suspend fun twoFactorAuth(@Body request: TwoFactorAuthRequest): Response<AuthResponse>

    /**
     * Refresh the access token using a refresh token.
     *
     * @param request The token refresh request.
     * @return The authentication response containing new tokens.
     */
    @POST("/api/v1/auth/refresh")
    suspend fun refreshToken(@Body request: TokenRefreshRequest): Response<AuthResponse>

    /**
     * Request account recovery using the recovery phrase and PIN.
     *
     * @param request The account recovery request.
     * @return The recovery response containing a recovery token.
     */
    @POST("/api/v1/auth/recovery/request")
    suspend fun requestRecovery(@Body request: AccountRecoveryRequest): Response<RecoveryResponse>

    /**
     * Confirm account recovery using the recovery token.
     *
     * @param request The recovery confirmation request.
     * @return The authentication response containing tokens and user information.
     */
    @POST("/api/v1/auth/recovery/confirm")
    suspend fun confirmRecovery(@Body request: RecoveryConfirmRequest): Response<AuthResponse>

    /**
     * Change the user's password.
     *
     * @param request The password change request.
     * @return A response indicating success or failure.
     */
    @POST("/api/v1/auth/password")
    suspend fun changePassword(@Body request: PasswordChangeRequest): Response<Unit>
}
