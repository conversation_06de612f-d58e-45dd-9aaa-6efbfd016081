package com.tfkcolin.meena.data.models

import java.io.File

/**
 * Represents the result of a media download.
 */
sealed class DownloadResult {
    /**
     * The download was successful.
     *
     * @param file The downloaded file.
     */
    data class Success(val file: File) : DownloadResult()

    /**
     * The download failed.
     *
     * @param error The error message.
     */
    data class Error(val error: String) : DownloadResult()
}
