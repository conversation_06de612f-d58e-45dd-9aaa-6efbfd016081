package com.tfkcolin.meena.data.repositories

import com.google.gson.Gson
import com.tfkcolin.meena.data.api.UserApi
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.models.ErrorResponse
import com.tfkcolin.meena.data.models.UpdateProfileRequest
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.domain.repositories.IUserRepository
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for user operations.
 */
@Singleton
class UserRepository @Inject constructor(
    private val userApi: User<PERSON><PERSON>,
    private val userDao: <PERSON><PERSON><PERSON><PERSON>,
    private val tokenManager: <PERSON>kenManager,
    private val gson: <PERSON><PERSON>,
    private val errorHandler: ErrorHandler
) : IUserRepository, BaseRepository {

    override suspend fun getUserById(userId: String): Result<User> {
        // First, try to get the user from the local database
        val localUserResult = executeDatabaseOperation {
            userDao.getUserById(userId)
        }

        if (localUserResult.isSuccess && localUserResult.getOrNull() != null) {
            return Result.success(localUserResult.getOrNull()!!)
        }

        // If not found locally, try to get from the API
        return executeNetworkOperation {
            val response = userApi.getUserById(userId)

            if (response.isSuccessful && response.body() != null) {
                val user = response.body()!!

                // Save user to database
                executeDatabaseOperation {
                    userDao.insertUser(user)
                }

                user
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun getUserByHandle(userHandle: String): Result<User> {
        // First, try to get the user from the local database
        val localUserResult = executeDatabaseOperation {
            userDao.getUserByHandle(userHandle)
        }

        if (localUserResult.isSuccess && localUserResult.getOrNull() != null) {
            return Result.success(localUserResult.getOrNull()!!)
        }

        // If not found locally, try to get from the API
        return executeNetworkOperation {
            val response = userApi.getUserByHandle(userHandle)

            if (response.isSuccessful && response.body() != null) {
                val user = response.body()!!

                // Save user to database
                executeDatabaseOperation {
                    userDao.insertUser(user)
                }

                user
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override fun getCurrentUser(): Flow<User?> {
        val userId = tokenManager.getUserId()

        return if (userId != null) {
            userDao.getCurrentUserFlow(userId)
                .asResourceFlow()
                .map { resource ->
                    when (resource) {
                        is Resource.Success -> resource.data
                        is Resource.Loading -> null
                        is Resource.Error -> {
                            errorHandler.setGlobalError(resource.error)
                            null
                        }
                    }
                }
        } else {
            flow { emit(null) }
        }
    }

    override suspend fun updateProfile(
        displayName: String?,
        bio: String?,
        avatarUrl: String?
    ): Result<User> {
        return executeNetworkOperation {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")

            // Create the request
            val request = UpdateProfileRequest(
                displayName = displayName,
                bio = bio,
                avatarUrl = avatarUrl
            )

            // Make the API call
            val response = userApi.updateProfile(
                authToken = "Bearer $token",
                request = request
            )

            if (response.isSuccessful && response.body() != null) {
                val updatedUser = response.body()!!

                // Update the user in the database
                executeDatabaseOperation {
                    userDao.insertUser(updatedUser)
                }

                updatedUser
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }
}
