package com.tfkcolin.meena.data.local

import androidx.room.*
import com.tfkcolin.meena.data.models.Contact
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for the Contact entity.
 */
@Dao
interface ContactDao {

    /**
     * Insert a contact into the database.
     * If the contact already exists, replace it.
     *
     * @param contact The contact to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertContact(contact: Contact)

    /**
     * Insert multiple contacts into the database.
     * If a contact already exists, replace it.
     *
     * @param contacts The contacts to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertContacts(contacts: List<Contact>)

    /**
     * Get all contacts for a user.
     *
     * @param userId The user ID.
     * @return A flow of all contacts for the user.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId")
    fun getContactsFlow(userId: String): Flow<List<Contact>>

    /**
     * Get all contacts for a user.
     *
     * @param userId The user ID.
     * @return All contacts for the user.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId")
    suspend fun getContacts(userId: String): List<Contact>

    /**
     * Get a contact by ID.
     *
     * @param id The contact ID.
     * @return The contact, or null if not found.
     */
    @Query("SELECT * FROM contacts WHERE id = :id")
    suspend fun getContactById(id: String): Contact?

    /**
     * Get a contact by user ID and contact ID.
     *
     * @param userId The user ID.
     * @param contactId The contact ID.
     * @return The contact, or null if not found.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId AND contactId = :contactId")
    suspend fun getContact(userId: String, contactId: String): Contact?

    /**
     * Update a contact's display name.
     *
     * @param id The contact ID.
     * @param displayName The display name.
     */
    @Query("UPDATE contacts SET displayName = :displayName WHERE id = :id")
    suspend fun updateContactDisplayName(id: String, displayName: String?)

    /**
     * Update a contact's notes.
     *
     * @param id The contact ID.
     * @param notes The notes.
     */
    @Query("UPDATE contacts SET notes = :notes WHERE id = :id")
    suspend fun updateContactNotes(id: String, notes: String?)

    /**
     * Update a contact's relationship.
     *
     * @param id The contact ID.
     * @param relationship The relationship.
     */
    @Query("UPDATE contacts SET relationship = :relationship WHERE id = :id")
    suspend fun updateContactRelationship(id: String, relationship: String)

    /**
     * Delete a contact.
     *
     * @param id The contact ID.
     */
    @Query("DELETE FROM contacts WHERE id = :id")
    suspend fun deleteContact(id: String)

    /**
     * Update a contact.
     *
     * @param contact The contact to update.
     */
    @Update
    suspend fun updateContact(contact: Contact)

    /**
     * Delete all contacts for a user.
     *
     * @param userId The user ID.
     */
    @Query("DELETE FROM contacts WHERE userId = :userId")
    suspend fun deleteAllContacts(userId: String)

    /**
     * Get all contacts with a specific relationship.
     *
     * @param userId The user ID.
     * @param relationship The relationship.
     * @return A flow of all contacts with the specified relationship.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId AND relationship = :relationship")
    fun getContactsByRelationshipFlow(userId: String, relationship: String): Flow<List<Contact>>

    /**
     * Get all contacts with a specific relationship.
     *
     * @param userId The user ID.
     * @param relationship The relationship.
     * @return All contacts with the specified relationship.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId AND relationship = :relationship")
    suspend fun getContactsByRelationship(userId: String, relationship: String): List<Contact>

    /**
     * Search contacts by name or notes.
     *
     * @param userId The user ID.
     * @param query The search query.
     * @return All contacts matching the search query.
     */
    @Query("SELECT * FROM contacts WHERE userId = :userId AND (displayName LIKE '%' || :query || '%' OR notes LIKE '%' || :query || '%')")
    suspend fun searchContacts(userId: String, query: String): List<Contact>
}
