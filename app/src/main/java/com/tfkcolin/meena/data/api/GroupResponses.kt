package com.tfkcolin.meena.data.api

/**
 * Group response.
 */
data class GroupResponse(
    val group_id: String,
    val name: String,
    val description: String?,
    val picture_url: String?,
    val privacy_type: String,
    val creator: UserInfo,
    val member_count: Int,
    val settings: Map<String, Any>?
)

/**
 * User info.
 */
data class UserInfo(
    val user_id: String,
    val user_handle: String,
    val display_name: String?,
    val avatar_url: String?
)

/**
 * Group members response.
 */
data class GroupMembersResponse(
    val members: List<GroupMember>,
    val total_count: Int
)

/**
 * Group member.
 */
data class GroupMember(
    val user: UserInfo,
    val role: String, // "admin" or "member"
    val joined_at: Long
)

/**
 * Group member response.
 */
data class GroupMemberResponse(
    val user: UserInfo,
    val role: String,
    val joined_at: Long
)

/**
 * Group members request.
 */
data class GroupMembersRequest(
    val user_handles: List<String>
)

/**
 * Update role request.
 */
data class UpdateRoleRequest(
    val role: String // "admin" or "member"
)

/**
 * Update group request.
 */
data class UpdateGroupRequest(
    val name: String? = null,
    val description: String? = null,
    val picture_url: String? = null
)

/**
 * Create group request.
 */
data class CreateGroupRequest(
    val name: String,
    val description: String? = null,
    val privacy_type: String, // "public", "private", or "secret"
    val initial_members: List<String>? = null,
    val payment_token: String? = null
)
