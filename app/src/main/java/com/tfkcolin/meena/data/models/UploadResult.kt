package com.tfkcolin.meena.data.models

/**
 * Represents the result of a media upload.
 */
sealed class UploadResult {
    /**
     * The upload was successful.
     *
     * @param attachment The uploaded media attachment.
     */
    data class Success(val attachment: MediaAttachment) : UploadResult()

    /**
     * The upload failed.
     *
     * @param error The error message.
     */
    data class Error(val error: String) : UploadResult()
}
