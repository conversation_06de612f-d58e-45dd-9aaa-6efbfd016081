package com.tfkcolin.meena.data.repositories

import com.google.gson.Gson
import com.tfkcolin.meena.data.api.ChatApi
// Removed unused import
import com.tfkcolin.meena.data.api.ChatListResponse
import com.tfkcolin.meena.data.api.ChatResponse
import com.tfkcolin.meena.data.api.ChatSettingsRequest
import com.tfkcolin.meena.data.api.CreateChatRequest
import com.tfkcolin.meena.data.api.CreateGroupRequest
import com.tfkcolin.meena.data.api.EditMessageRequest
import com.tfkcolin.meena.data.api.ForwardMessageRequest
import com.tfkcolin.meena.data.api.GroupMemberResponse
import com.tfkcolin.meena.data.api.GroupMembersRequest
import com.tfkcolin.meena.data.api.GroupMembersResponse
import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.data.api.MessageListResponse
import com.tfkcolin.meena.data.api.MessageReactionRequest
import com.tfkcolin.meena.data.api.MessageResponse
// Removed unused import
import com.tfkcolin.meena.data.api.SendMessageRequest
import com.tfkcolin.meena.data.api.UpdateBatchMessageStatusRequest
import com.tfkcolin.meena.data.api.UpdateGroupRequest
import com.tfkcolin.meena.data.api.UpdateRoleRequest
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.api.ErrorResponse
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message

import com.tfkcolin.meena.domain.repositories.IChatRepository
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for chat operations.
 */
@Singleton
class ChatRepository @Inject constructor(
    private val chatApi: ChatApi,
    private val chatDao: ChatDao,
    private val messageDao: MessageDao,
    private val mediaAttachmentDao: MediaAttachmentDao,
    private val tokenManager: TokenManager,
    private val gson: Gson,
    private val errorHandler: ErrorHandler
) : IChatRepository, BaseRepository {

    override suspend fun getChats(limit: Int, offset: Int): Result<ChatListResponse> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.getChats(
                authToken = "Bearer $token",
                limit = limit,
                offset = offset
            )

            if (response.isSuccessful && response.body() != null) {
                val chatListResponse = response.body()!!

                // Save chats to database
                executeDatabaseOperation {
                    chatDao.insertChats(chatListResponse.chats)
                }

                chatListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override fun getChatsFlow(): Flow<List<Chat>> {
        val userId = tokenManager.getUserId() ?: return emptyFlow()
        return chatDao.getChatsForUserFlow(userId)
    }

    override suspend fun getChatById(chatId: String): Result<ChatResponse> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.getChatById(
                authToken = "Bearer $token",
                chatId = chatId
            )

            if (response.isSuccessful && response.body() != null) {
                val chatResponse = response.body()!!

                // Save chat to database
                executeDatabaseOperation {
                    chatDao.insertChat(chatResponse.chat)
                }

                chatResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun createChat(participantIds: List<String>): Result<ChatResponse> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Ensure the current user is included in the participant IDs
        val currentUserId = tokenManager.getUserId() ?: return Result.failure(
            Exception("User not logged in")
        )

        val allParticipantIds = if (participantIds.contains(currentUserId)) {
            participantIds
        } else {
            participantIds + currentUserId
        }

        return executeNetworkOperation {
            val response = chatApi.createChat(
                authToken = "Bearer $token",
                request = CreateChatRequest(
                    participant_ids = allParticipantIds
                )
            )

            if (response.isSuccessful && response.body() != null) {
                val chatResponse = response.body()!!

                // Save chat to database
                executeDatabaseOperation {
                    chatDao.insertChat(chatResponse.chat)
                }

                chatResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun deleteChat(chatId: String): Result<Unit> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.deleteChat(
                authToken = "Bearer $token",
                chatId = chatId
            )

            if (response.isSuccessful) {
                // Delete chat and messages from database
                executeDatabaseOperation {
                    chatDao.deleteChat(chatId)
                    messageDao.deleteMessagesForChat(chatId)
                }

                Unit
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun getMessages(chatId: String, limit: Int, offset: Int): Result<MessageListResponse> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.getMessages(
                authToken = "Bearer $token",
                chatId = chatId,
                limit = limit,
                offset = offset
            )

            if (response.isSuccessful && response.body() != null) {
                val messageListResponse = response.body()!!

                // Save messages to database
                executeDatabaseOperation {
                    messageDao.insertMessages(messageListResponse.messages)
                }

                messageListResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override fun getMessagesFlow(chatId: String): Flow<List<Message>> {
        return messageDao.getMessagesForChatFlow(chatId).map { messages ->
            // Load attachments for each message
            messages.onEach { message ->
                if (message.hasAttachments) {
                    // We can't use executeDatabaseOperation here because it's a suspend function
                    // and we're in a Flow transformation
                    message.attachments = mediaAttachmentDao.getAttachmentsForMessage(message.id)
                }
            }
        }.asResourceFlow().map { resource ->
            when (resource) {
                is Resource.Success -> resource.data
                is Resource.Loading -> emptyList()
                is Resource.Error -> {
                    errorHandler.setGlobalError(resource.error)
                    emptyList()
                }
            }
        }
    }

    override suspend fun sendMessage(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String,
        mediaUrl: String?
    ): Result<MessageResponse> {
        val userId = tokenManager.getUserId() ?: return Result.failure(Exception("User not logged in"))

        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.sendMessage(
                authToken = "Bearer $token",
                chatId = chatId,
                request = SendMessageRequest(
                    content = content,
                    content_type = contentType,
                    media_url = mediaUrl
                )
            )

            if (response.isSuccessful && response.body() != null) {
                val messageResponse = response.body()!!

                // Save message to database
                executeDatabaseOperation {
                    messageDao.insertMessage(messageResponse.message)
                    // Update chat's last message
                    updateChatLastMessage(chatId, messageResponse.message)
                }

                messageResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun updateMessageStatus(messageId: String, status: String) {
        executeNetworkOperation {
            // Update message status in database
            executeDatabaseOperation {
                messageDao.updateMessageStatus(messageId, status)

                // If the message is being marked as read, update the unread count for the chat
                if (status == "read") {
                    val message = messageDao.getMessageById(messageId)
                    if (message != null) {
                        val userId = tokenManager.getUserId()
                        if (userId != null) {
                            val unreadCount = messageDao.getUnreadMessageCountForChat(message.chatId, userId)
                            chatDao.updateChatUnreadCount(message.chatId, unreadCount)
                        }
                    }
                }
            }

            // Send status update to the server
            val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")
            chatApi.updateMessageStatus("Bearer $token", messageId, status)

            // Note: Real-time status updates are not supported in mock-only mode
        }.onFailure { e ->
            errorHandler.setGlobalError(e)
        }
    }

    override suspend fun markChatMessagesAsRead(chatId: String, currentUserId: String) {
        executeNetworkOperation {
            // Get all unread messages in this chat that were sent to the current user
            val unreadMessagesResult = executeDatabaseOperation {
                messageDao.getUnreadMessagesForChat(chatId, currentUserId)
            }

            // Unwrap the result to get the actual list of messages
            val unreadMessages = unreadMessagesResult.getOrNull() ?: emptyList()

            if (unreadMessages.isNotEmpty()) {
                // Update all messages to read status in the database
                executeDatabaseOperation {
                    unreadMessages.forEach { message ->
                        messageDao.updateMessageStatus(message.id, "read")
                    }

                    // Update the unread count for the chat
                    val unreadCount = messageDao.getUnreadMessageCountForChat(chatId, currentUserId)
                    chatDao.updateChatUnreadCount(chatId, unreadCount)
                }

                // Send batch status update to the server
                val token = tokenManager.getAccessToken() ?: throw Exception("User not authenticated")
                val messageIds = unreadMessages.map { it.id }

                // Send batch status update to the server
                chatApi.updateBatchMessageStatus(
                    "Bearer $token",
                    UpdateBatchMessageStatusRequest(messageIds, "read")
                )

                // Note: Real-time status updates are not supported in mock-only mode
            }
        }.onFailure { e ->
            errorHandler.setGlobalError(e)
        }
    }

    override suspend fun getOrCreateChatWithUser(userId: String): Result<Chat> {
        val currentUserId = tokenManager.getUserId() ?: return Result.failure(Exception("User not logged in"))

        // Sort participant IDs to ensure consistent chat ID
        val participantIds = listOf(currentUserId, userId).sorted()
        val participantIdsString = participantIds.joinToString(",")

        return executeDatabaseOperation {
            // Check if chat already exists
            val existingChat = chatDao.getChatByParticipants(participantIdsString)
            if (existingChat != null) {
                existingChat
            } else {
                // Create new chat
                val chatResponse = createChat(participantIds).getOrThrow()
                chatResponse.chat
            }
        }
    }

    override suspend fun sendMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>
    ): Result<MessageResponse> {
        return executeNetworkOperation {
            // First send the message
            val messageResponse = sendMessage(
                chatId = chatId,
                recipientId = recipientId,
                content = content,
                contentType = if (attachments.isNotEmpty()) attachments.first().type else "text"
            ).getOrThrow()

            val message = messageResponse.message

            // Update message to indicate it has attachments and save attachments
            val dbResult = executeDatabaseOperation {
                // Update message to indicate it has attachments
                val updatedMessage = message.copy(hasAttachments = true)
                messageDao.insertMessage(updatedMessage)

                // Save attachments with the message ID
                val messageAttachments = attachments.map { attachment ->
                    attachment.copy(messageId = message.id)
                }
                mediaAttachmentDao.insertAttachments(messageAttachments)

                // Return the updated message response
                MessageResponse(updatedMessage.apply { this.attachments = messageAttachments })
            }

            // Unwrap the database operation result
            dbResult.getOrThrow()
        }
    }

    override suspend fun getAttachmentsForMessage(messageId: String): List<MediaAttachment> {
        return executeDatabaseOperation {
            mediaAttachmentDao.getAttachmentsForMessage(messageId)
        }.getOrDefault(emptyList())
    }

    override fun getAttachmentsForMessageFlow(messageId: String): Flow<List<MediaAttachment>> {
        return mediaAttachmentDao.getAttachmentsForMessageFlow(messageId)
            .asResourceFlow()
            .map { resource ->
                when (resource) {
                    is Resource.Success -> resource.data
                    is Resource.Loading -> emptyList()
                    is Resource.Error -> {
                        errorHandler.setGlobalError(resource.error)
                        emptyList()
                    }
                }
            }
    }

    override suspend fun searchMessages(query: String): List<Message> {
        if (query.isBlank()) return emptyList()

        return executeDatabaseOperation {
            // Search messages in the local database
            val messages = messageDao.searchMessages(query)

            // Load attachments for messages that have them
            messages.onEach { message ->
                if (message.hasAttachments) {
                    message.attachments = mediaAttachmentDao.getAttachmentsForMessage(message.id)
                }
            }
        }.getOrDefault(emptyList())
    }

    override suspend fun searchMessagesInChat(chatId: String, query: String): List<Message> {
        if (query.isBlank()) return emptyList()

        return executeDatabaseOperation {
            // Search messages in the local database
            val messages = messageDao.searchMessagesInChat(chatId, query)

            // Load attachments for messages that have them
            messages.onEach { message ->
                if (message.hasAttachments) {
                    message.attachments = mediaAttachmentDao.getAttachmentsForMessage(message.id)
                }
            }
        }.getOrDefault(emptyList())
    }

    override suspend fun editMessage(messageId: String, newContent: String): Result<Message> {
        return try {
            // Get the original message
            val originalMessage = messageDao.getMessageById(messageId) ?: return Result.failure(
                Exception("Message not found")
            )

            // Check if the user is the sender of the message
            val currentUserId = tokenManager.getUserId() ?: return Result.failure(
                Exception("User not authenticated")
            )

            if (originalMessage.senderId != currentUserId) {
                return Result.failure(Exception("You can only edit your own messages"))
            }

            // Update the message in the local database
            messageDao.updateMessageContent(messageId, newContent)

            // Send the update to the server
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            val response = chatApi.editMessage(
                authToken = "Bearer $token",
                messageId = messageId,
                request = EditMessageRequest(newContent)
            )

            if (response.isSuccessful) {
                // Get the updated message
                val updatedMessage = messageDao.getMessageById(messageId) ?: return Result.failure(
                    Exception("Failed to retrieve updated message")
                )

                // Note: Real-time edit notifications are not supported in mock-only mode

                Result.success(updatedMessage)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to edit message"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun deleteMessageForSelf(messageId: String): Result<Unit> {
        return try {
            // Get the original message
            val message = messageDao.getMessageById(messageId) ?: return Result.failure(
                Exception("Message not found")
            )

            // Mark the message as deleted for self in the local database
            messageDao.markMessageAsDeletedForSelf(messageId)

            // Send the delete request to the server
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            val response = chatApi.deleteMessageForSelf(
                authToken = "Bearer $token",
                messageId = messageId
            )

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to delete message"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun deleteMessageForEveryone(messageId: String): Result<Unit> {
        return try {
            // Get the original message
            val message = messageDao.getMessageById(messageId) ?: return Result.failure(
                Exception("Message not found")
            )

            // Check if the user is the sender of the message
            val currentUserId = tokenManager.getUserId() ?: return Result.failure(
                Exception("User not authenticated")
            )

            if (message.senderId != currentUserId) {
                return Result.failure(Exception("You can only delete your own messages for everyone"))
            }

            // Mark the message as deleted for everyone in the local database
            messageDao.markMessageAsDeletedForEveryone(messageId)

            // Send the delete request to the server
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            val response = chatApi.deleteMessageForEveryone(
                authToken = "Bearer $token",
                messageId = messageId
            )

            if (response.isSuccessful) {
                // Note: Real-time deletion notifications are not supported in mock-only mode

                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to delete message for everyone"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun archiveChat(chatId: String, isArchived: Boolean): Result<Unit> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Update the chat in the local database
            chatDao.updateChatArchiveStatus(chatId, isArchived)

            // Send the update to the server
            val response = chatApi.updateChatSettings(
                authToken = "Bearer $token",
                chatId = chatId,
                request = ChatSettingsRequest(is_archived = isArchived)
            )

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to archive conversation"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun muteChat(chatId: String, isMuted: Boolean): Result<Unit> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Update the chat in the local database
            chatDao.updateChatMuteStatus(chatId, isMuted, null)

            // Send the update to the server
            val response = chatApi.updateChatSettings(
                authToken = "Bearer $token",
                chatId = chatId,
                request = ChatSettingsRequest(is_muted = isMuted)
            )

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to mute conversation"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override fun getNonArchivedChatsFlow(): Flow<List<Chat>> {
        val userId = tokenManager.getUserId() ?: return flowOf(emptyList())
        return chatDao.getNonArchivedChatsForUserFlow(userId)
    }

    override fun getArchivedChatsFlow(): Flow<List<Chat>> {
        val userId = tokenManager.getUserId() ?: return flowOf(emptyList())
        return chatDao.getArchivedChatsForUserFlow(userId)
    }

    override fun getGroupChatsFlow(): Flow<List<Chat>> {
        val userId = tokenManager.getUserId() ?: return flowOf(emptyList())
        return chatDao.getGroupChatsForUserFlow(userId)
    }

    override suspend fun forwardMessage(
        messageId: String,
        chatId: String,
        additionalContent: String?
    ): Result<Message> {
        return try {
            // Get the original message
            val originalMessage = messageDao.getMessageById(messageId) ?: return Result.failure(
                Exception("Message not found")
            )

            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Forward the message via API
            val response = chatApi.forwardMessage(
                authToken = "Bearer $token",
                messageId = messageId,
                request = ForwardMessageRequest(
                    chat_id = chatId,
                    additional_content = additionalContent
                )
            )

            if (response.isSuccessful && response.body() != null) {
                val forwardedMessage = response.body()!!.message

                // Save the forwarded message to the database
                messageDao.insertMessage(forwardedMessage)

                // If the original message had attachments, copy them to the new message
                if (originalMessage.hasAttachments) {
                    val attachments = mediaAttachmentDao.getAttachmentsForMessage(originalMessage.id)
                    val forwardedAttachments = attachments.map { attachment ->
                        attachment.copy(
                            id = java.util.UUID.randomUUID().toString(),
                            messageId = forwardedMessage.id
                        )
                    }
                    mediaAttachmentDao.insertAttachments(forwardedAttachments)
                }

                // Update the chat's last message
                updateChatLastMessage(chatId, forwardedMessage)

                Result.success(forwardedMessage)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to forward message"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun addReaction(messageId: String, emoji: String): Result<Unit> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Get the message to get the chat ID
        val message = messageDao.getMessageById(messageId) ?: return Result.failure(
            Exception("Message not found")
        )

        return executeNetworkOperation {
            // Send reaction via REST API
            val response = chatApi.addReaction(
                authToken = "Bearer $token",
                messageId = messageId,
                request = MessageReactionRequest(emoji = emoji)
            )

            if (response.isSuccessful) {
                // Update the message in the database to include the reaction
                executeDatabaseOperation {
                    val userId = tokenManager.getUserId() ?: throw Exception("User not logged in")

                    // Get the current reactions or initialize an empty map
                    val currentReactions = message.reactions?.toMutableMap() ?: mutableMapOf<String, List<String>>()

                    // Add the user ID to the list of users who reacted with this emoji
                    val usersForEmoji = currentReactions[emoji]?.toMutableList() ?: mutableListOf()
                    if (!usersForEmoji.contains(userId)) {
                        usersForEmoji.add(userId)
                    }

                    // Create a new map with the updated reactions
                    val updatedReactions = currentReactions.toMutableMap()
                    updatedReactions[emoji] = usersForEmoji

                    // Update the message
                    val updatedMessage = message.copy(reactions = updatedReactions)
                    messageDao.insertMessage(updatedMessage)
                }

                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        }
    }

    override suspend fun removeReaction(messageId: String, emoji: String): Result<Unit> {
        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Get the message to get the chat ID
        val message = messageDao.getMessageById(messageId) ?: return Result.failure(
            Exception("Message not found")
        )

        return executeNetworkOperation {
            // Send reaction removal via REST API
            val response = chatApi.removeReaction(
                authToken = "Bearer $token",
                messageId = messageId,
                request = MessageReactionRequest(emoji = emoji)
            )

            if (response.isSuccessful) {
                // Update the message in the database to remove the reaction
                executeDatabaseOperation {
                    val userId = tokenManager.getUserId() ?: throw Exception("User not logged in")

                    // Get the current reactions or return if there are none
                    val currentReactions = message.reactions ?: return@executeDatabaseOperation

                    // Create a mutable copy of the reactions map
                    val updatedReactions = currentReactions.toMutableMap()

                    // Remove the user ID from the list of users who reacted with this emoji
                    val usersForEmoji = currentReactions[emoji]?.toMutableList()
                    if (usersForEmoji != null) {
                        usersForEmoji.removeAll { it == userId }

                        // If there are no more users for this emoji, remove the emoji from the map
                        if (usersForEmoji.isEmpty()) {
                            updatedReactions.remove(emoji)
                        } else {
                            updatedReactions[emoji] = usersForEmoji
                        }

                        // Update the message
                        val updatedMessage = message.copy(reactions = updatedReactions)
                        messageDao.insertMessage(updatedMessage)
                    }
                }

                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        }
    }

    override suspend fun replyToMessage(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String,
        mediaUrl: String?
    ): Result<MessageResponse> {
        val userId = tokenManager.getUserId() ?: return Result.failure(Exception("User not logged in"))

        // Get the access token
        val token = tokenManager.getAccessToken() ?: return Result.failure(
            Exception("User not authenticated")
        )

        return executeNetworkOperation {
            val response = chatApi.replyToMessage(
                authToken = "Bearer $token",
                chatId = chatId,
                request = SendMessageRequest(
                    content = content,
                    content_type = contentType,
                    media_url = mediaUrl,
                    reply_to_message_id = replyToMessageId
                )
            )

            if (response.isSuccessful && response.body() != null) {
                val messageResponse = response.body()!!

                // Save message to database
                executeDatabaseOperation {
                    messageDao.insertMessage(messageResponse.message)
                    // Update chat's last message
                    updateChatLastMessage(chatId, messageResponse.message)
                }

                messageResponse
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                throw Exception(errorResponse.message)
            }
        }
    }

    override suspend fun replyToMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ): Result<MessageResponse> {
        return executeNetworkOperation {
            // First send the reply
            val messageResponse = replyToMessage(
                chatId = chatId,
                recipientId = recipientId,
                content = content,
                replyToMessageId = replyToMessageId,
                contentType = if (attachments.isNotEmpty()) attachments.first().type else "text"
            ).getOrThrow()

            val message = messageResponse.message

            // Update message to indicate it has attachments and save attachments
            val dbResult = executeDatabaseOperation {
                // Update message to indicate it has attachments
                val updatedMessage = message.copy(hasAttachments = true)
                messageDao.insertMessage(updatedMessage)

                // Save attachments with the message ID
                val messageAttachments = attachments.map { attachment ->
                    attachment.copy(messageId = message.id)
                }
                mediaAttachmentDao.insertAttachments(messageAttachments)

                // Return the updated message response
                MessageResponse(updatedMessage.apply { this.attachments = messageAttachments })
            }

            // Unwrap the database operation result
            dbResult.getOrThrow()
        }
    }

    override suspend fun createGroup(
        name: String,
        description: String?,
        privacyType: String,
        initialMembers: List<String>?
    ): Result<String> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Create the group via API
            val response = chatApi.createGroup(
                authToken = "Bearer $token",
                request = CreateGroupRequest(
                    name = name,
                    description = description,
                    privacy_type = privacyType,
                    initial_members = initialMembers
                )
            )

            if (response.isSuccessful && response.body() != null) {
                val groupId = response.body()!!.group_id

                // The group will be available as a conversation through the WebSocket or when fetching conversations

                Result.success(groupId)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to create group"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun getGroup(groupId: String): Result<GroupResponse> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Get the group via API
            val response = chatApi.getGroup(
                authToken = "Bearer $token",
                groupId = groupId
            )

            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to get group"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun updateGroup(
        groupId: String,
        name: String?,
        description: String?,
        pictureUrl: String?
    ): Result<GroupResponse> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Update the group via API
            val response = chatApi.updateGroup(
                authToken = "Bearer $token",
                groupId = groupId,
                request = UpdateGroupRequest(
                    name = name,
                    description = description,
                    picture_url = pictureUrl
                )
            )

            if (response.isSuccessful && response.body() != null) {
                // The updated group will be reflected in the conversation through WebSocket or when fetching conversations

                Result.success(response.body()!!)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to update group"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun getGroupMembers(
        groupId: String,
        limit: Int,
        offset: Int
    ): Result<GroupMembersResponse> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Get the group members via API
            val response = chatApi.getGroupMembers(
                authToken = "Bearer $token",
                groupId = groupId,
                limit = limit,
                offset = offset
            )

            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to get group members"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun addGroupMembers(
        groupId: String,
        userHandles: List<String>
    ): Result<GroupResponse> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Add members via API
            val response = chatApi.addGroupMembers(
                authToken = "Bearer $token",
                groupId = groupId,
                request = GroupMembersRequest(
                    user_handles = userHandles
                )
            )

            if (response.isSuccessful && response.body() != null) {
                // The updated group will be reflected in the conversation through WebSocket or when fetching conversations

                Result.success(response.body()!!)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to add group members"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun removeGroupMember(
        groupId: String,
        userHandle: String
    ): Result<Unit> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Remove member via API
            val response = chatApi.removeGroupMember(
                authToken = "Bearer $token",
                groupId = groupId,
                userHandle = userHandle
            )

            if (response.isSuccessful) {
                // The updated group will be reflected in the conversation through WebSocket or when fetching conversations

                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to remove group member"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun updateGroupMemberRole(
        groupId: String,
        userHandle: String,
        role: String
    ): Result<GroupMemberResponse> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Update member role via API
            val response = chatApi.updateGroupMemberRole(
                authToken = "Bearer $token",
                groupId = groupId,
                userHandle = userHandle,
                request = UpdateRoleRequest(
                    role = role
                )
            )

            if (response.isSuccessful && response.body() != null) {
                // The updated group will be reflected in the conversation through WebSocket or when fetching conversations

                Result.success(response.body()!!)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to update member role"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    override suspend fun updateChatSettings(
        chatId: String,
        isArchived: Boolean?,
        isMuted: Boolean?,
        isPinned: Boolean?
    ): Result<Unit> {
        return try {
            // Get the access token
            val token = tokenManager.getAccessToken() ?: return Result.failure(
                Exception("User not authenticated")
            )

            // Update chat settings via API
            val response = chatApi.updateChatSettings(
                authToken = "Bearer $token",
                chatId = chatId,
                request = ChatSettingsRequest(
                    is_archived = isArchived,
                    is_muted = isMuted,
                    is_pinned = isPinned
                )
            )

            if (response.isSuccessful) {
                // Update the local database if needed
                if (isArchived != null) {
                    chatDao.updateChatArchiveStatus(chatId, isArchived)
                }

                if (isMuted != null) {
                    chatDao.updateChatMuteStatus(
                        chatId = chatId,
                        isMuted = isMuted,
                        mutedUntil = null // We don't have a muted_until in this API
                    )
                }

                if (isPinned != null) {
                    chatDao.updateChatPinnedStatus(chatId, isPinned)
                }

                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = if (!errorBody.isNullOrEmpty()) {
                    try {
                        gson.fromJson(errorBody, ErrorResponse::class.java)
                    } catch (e: Exception) {
                        null
                    }
                } else null

                Result.failure(Exception(errorResponse?.message ?: "Failed to update chat settings"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

    // Keeping this for backward compatibility
    @Deprecated("Use updateChatSettings instead", ReplaceWith("updateChatSettings(chatId, isArchived, isMuted, isPinned)"))
    suspend fun updateConversationSettings(
        conversationId: String,
        isArchived: Boolean?,
        isMuted: Boolean?,
        isPinned: Boolean?
    ): Result<Unit> {
        return updateChatSettings(conversationId, isArchived, isMuted, isPinned)
    }

    /**
     * Update a chat's last message.
     *
     * @param chatId The chat ID.
     * @param message The last message.
     */
    private suspend fun updateChatLastMessage(chatId: String, message: Message) {
        val chat = chatDao.getChatById(chatId)
        if (chat != null) {
            chatDao.updateChatLastMessage(
                chatId = chatId,
                lastMessage = message.content,
                lastMessageTimestamp = message.timestamp
            )
        }
    }
}
