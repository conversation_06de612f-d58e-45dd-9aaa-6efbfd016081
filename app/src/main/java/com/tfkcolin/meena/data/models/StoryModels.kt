package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Domain Story model representing a user's story for data layer.
 * This is separate from the UI Story model to maintain clean architecture.
 */
@Entity(
    tableName = "stories",
    indices = [
        Index("userId"),
        Index("expiresAt"),
        Index("createdAt")
    ]
)
data class Story(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("media_url")
    val mediaUrl: String,

    @SerializedName("media_type")
    val mediaType: String = "image", // "image", "video"

    @SerializedName("thumbnail_url")
    val thumbnailUrl: String? = null,

    @SerializedName("caption")
    val caption: String? = null,

    @SerializedName("privacy_type")
    val privacyType: String = "contacts", // "public", "contacts", "close_friends", "custom"

    @SerializedName("view_count")
    val viewCount: Int = 0,

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @SerializedName("expires_at")
    val expiresAt: Long = System.currentTimeMillis() + (24 * 60 * 60 * 1000), // 24 hours

    @SerializedName("is_expired")
    val isExpired: Boolean = false,

    @SerializedName("is_highlighted")
    val isHighlighted: Boolean = false,

    @SerializedName("highlight_id")
    val highlightId: String? = null,

    @SerializedName("background_color")
    val backgroundColor: String? = null,

    @SerializedName("text_color")
    val textColor: String? = null,

    @SerializedName("allowed_viewer_ids")
    val allowedViewerIds: String? = null, // Comma-separated list for Room compatibility

    @SerializedName("viewer_ids")
    val viewerIds: String? = null // Comma-separated list of users who viewed
) {
    /**
     * Get the list of allowed viewer IDs.
     */
    fun getAllowedViewerIdsList(): List<String> {
        return allowedViewerIds?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    }

    /**
     * Get the list of viewer IDs.
     */
    fun getViewerIdsList(): List<String> {
        return viewerIds?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    }

    /**
     * Check if a user can view this story.
     */
    fun canUserView(userId: String, userContactIds: List<String> = emptyList()): Boolean {
        return when (privacyType) {
            "public" -> true
            "contacts" -> userContactIds.contains(this.userId) || userId == this.userId
            "close_friends" -> getAllowedViewerIdsList().contains(userId) || userId == this.userId
            "custom" -> getAllowedViewerIdsList().contains(userId) || userId == this.userId
            else -> userId == this.userId
        }
    }

    /**
     * Convert to UI StoryItem model.
     */
    fun toStoryItem(userName: String, userAvatarUrl: String, isViewed: Boolean = false): com.tfkcolin.meena.ui.stories.models.StoryItem {
        return com.tfkcolin.meena.ui.stories.models.StoryItem(
            id = id,
            userName = userName,
            userAvatarUrl = userAvatarUrl,
            storyImageUrl = thumbnailUrl ?: mediaUrl,
            isViewed = isViewed,
            hasMultipleStories = false, // This would be determined by checking if user has multiple stories
            isLive = false
        )
    }

    /**
     * Convert to UI Story model for story viewer.
     */
    fun toUIStory(userName: String, userAvatarUrl: String): com.tfkcolin.meena.ui.stories.models.Story {
        return com.tfkcolin.meena.ui.stories.models.Story(
            id = id,
            authorName = userName,
            authorAvatarUrl = userAvatarUrl,
            authorId = userId,
            timePosted = java.util.Date(createdAt),
            segments = listOf(
                com.tfkcolin.meena.ui.stories.models.StorySegment(
                    id = id,
                    type = if (mediaType == "video") com.tfkcolin.meena.ui.stories.models.SegmentType.VIDEO 
                           else com.tfkcolin.meena.ui.stories.models.SegmentType.IMAGE,
                    mediaUrl = mediaUrl,
                    duration = if (mediaType == "video") 15 else 5,
                    overlayItems = if (caption != null) {
                        listOf(
                            com.tfkcolin.meena.ui.stories.models.OverlayItem(
                                id = "${id}_caption",
                                type = com.tfkcolin.meena.ui.stories.models.OverlayType.TEXT,
                                content = caption,
                                position = androidx.compose.ui.geometry.Offset(0.5f, 0.9f)
                            )
                        )
                    } else emptyList()
                )
            ),
            isViewed = getViewerIdsList().isNotEmpty()
        )
    }
}

/**
 * Story view model representing a user's view of a story.
 */
@Entity(
    tableName = "story_views",
    indices = [
        Index("storyId"),
        Index("viewerId"),
        Index("viewedAt")
    ]
)
data class StoryView(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("story_id")
    val storyId: String,

    @SerializedName("viewer_id")
    val viewerId: String,

    @SerializedName("viewed_at")
    val viewedAt: Long = System.currentTimeMillis(),

    @SerializedName("view_duration")
    val viewDuration: Long = 0, // Duration in milliseconds

    @SerializedName("reaction_type")
    val reactionType: String? = null // Emoji reaction
)

/**
 * Story highlight model representing a collection of stories.
 */
@Entity(
    tableName = "story_highlights",
    indices = [
        Index("userId"),
        Index("createdAt")
    ]
)
data class StoryHighlight(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("title")
    val title: String,

    @SerializedName("cover_url")
    val coverUrl: String? = null,

    @SerializedName("story_ids")
    val storyIds: String, // Comma-separated list for Room compatibility

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @SerializedName("updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Get the list of story IDs.
     */
    fun getStoryIdsList(): List<String> {
        return storyIds.split(",").filter { it.isNotBlank() }
    }

    /**
     * Get the number of stories in the highlight.
     */
    val storyCount: Int
        get() = getStoryIdsList().size
}

/**
 * Story reaction model representing a user's reaction to a story.
 */
data class StoryReaction(
    @SerializedName("story_id")
    val storyId: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("reaction_type")
    val reactionType: String, // Emoji

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Story reply model representing a user's reply to a story.
 */
data class StoryReply(
    @SerializedName("id")
    val id: String,

    @SerializedName("story_id")
    val storyId: String,

    @SerializedName("sender_id")
    val senderId: String,

    @SerializedName("content")
    val content: String,

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Story privacy settings model.
 */
data class StoryPrivacySettings(
    @SerializedName("default_privacy_type")
    val defaultPrivacyType: String = "contacts",

    @SerializedName("allow_replies")
    val allowReplies: Boolean = true,

    @SerializedName("allow_sharing")
    val allowSharing: Boolean = true,

    @SerializedName("close_friends_list")
    val closeFriendsList: List<String> = emptyList(),

    @SerializedName("blocked_users")
    val blockedUsers: List<String> = emptyList()
)
