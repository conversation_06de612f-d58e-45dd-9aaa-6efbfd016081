package com.tfkcolin.meena.data.api

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * API interface for call endpoints.
 */
interface CallApi {
    
    /**
     * Get call logs for the current user.
     * 
     * @param limit The maximum number of call logs to return.
     * @param offset The offset for pagination.
     * @return The call logs response.
     */
    @GET("/api/v1/calls/logs")
    suspend fun getCallLogs(
        @Query("limit") limit: Int = 20,
        @Query("offset") offset: Int = 0
    ): Response<CallLogsResponse>
    
    /**
     * Initiate a call.
     * 
     * @param request The initiate call request.
     * @return The initiate call response.
     */
    @POST("/api/v1/calls/initiate")
    suspend fun initiateCall(@Body request: InitiateCallRequest): Response<InitiateCallResponse>
    
    /**
     * End a call.
     * 
     * @param callId The call ID.
     * @param request The end call request.
     * @return A response indicating success or failure.
     */
    @POST("/api/v1/calls/{call_id}/end")
    suspend fun endCall(
        @Path("call_id") callId: String,
        @Body request: EndCallRequest
    ): Response<Unit>
}

/**
 * Call logs response.
 */
data class CallLogsResponse(
    val call_logs: List<CallLog>,
    val total_count: Int,
    val limit: Int,
    val offset: Int
)

/**
 * Call log.
 */
data class CallLog(
    val call_log_id: String,
    val other_party: UserInfo,
    val type: String, // "audio", "video"
    val status: String, // "initiated", "ringing", "in_progress", "completed", "missed", "rejected"
    val start_time: Long,
    val duration_seconds: Int?
)

/**
 * Initiate call request.
 */
data class InitiateCallRequest(
    val callee_user_handle: String,
    val call_type: String // "audio", "video"
)

/**
 * Initiate call response.
 */
data class InitiateCallResponse(
    val call_id: String,
    val signaling_server_url: String,
    val ice_servers: List<IceServer>,
    val token: String
)

/**
 * ICE server.
 */
data class IceServer(
    val urls: List<String>,
    val username: String?,
    val credential: String?
)

/**
 * End call request.
 */
data class EndCallRequest(
    val duration_seconds: Int
)
