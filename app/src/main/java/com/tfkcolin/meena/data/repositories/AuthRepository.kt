package com.tfkcolin.meena.data.repositories

import com.google.gson.Gson
import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository implementation for authentication operations.
 */
@Singleton
class AuthRepository @Inject constructor(
    private val authApi: AuthApi,
    private val userDao: UserDao,
    private val tokenManager: TokenManager,
    private val gson: Gson,
    private val errorHandler: ErrorHandler
) : IAuthRepository, BaseRepository {

    override suspend fun register(request: RegisterRequest): Result<AuthResponse> {
        return executeNetworkOperation {
            println("AuthRepository: Calling register API with userHandle: ${request.userHandle}")
            val response = authApi.register(request)
            println("AuthRepository: Register API response - isSuccessful: ${response.isSuccessful}, code: ${response.code()}")

            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!

                // Save tokens
                tokenManager.saveTokens(
                    authResponse.accessToken,
                    authResponse.refreshToken
                )

                // Save user ID and user handle
                tokenManager.saveUserId(authResponse.user.id)
                tokenManager.saveUserHandle(authResponse.user.userHandle)

                // Save user to database
                executeDatabaseOperation {
                    userDao.insertUser(
                        User(
                            userId = authResponse.user.id,
                            userHandle = authResponse.user.userHandle,
                            displayName = authResponse.user.displayName,
                            profilePictureUrl = authResponse.user.avatarUrl,
                            bio = authResponse.user.bio,
                            phoneNumber = request.phoneNumber,
                            email = request.email,
                            subscriptionTier = if (authResponse.user.isGoldMember) "gold" else "free",
                            verificationStatus = authResponse.user.verificationStatus,
                            isActive = true,
                            createdAt = authResponse.user.createdAt,
                            lastSeenAt = authResponse.user.lastActive
                        )
                    )
                }

                // Mark registration as complete
                tokenManager.markRegistrationComplete()

                authResponse
            } else {
                val errorBody = response.errorBody()?.string()
                println("AuthRepository: Register API failed - errorBody: $errorBody")
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    println("AuthRepository: Failed to parse error response: ${e.message}")
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                val errorMessage = errorResponse.message
                println("AuthRepository: Throwing exception with message: $errorMessage")
                throw Exception(errorMessage)
            }
        }
    }

    override suspend fun login(request: LoginRequest): Result<AuthResponse> {
        return try {
            val response = authApi.login(request)

            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!

                // If 2FA is required, return the response without saving tokens
                if (authResponse.requires2fa) {
                    return Result.success(authResponse)
                }

                // Save tokens
                tokenManager.saveTokens(
                    authResponse.accessToken,
                    authResponse.refreshToken
                )

                // Save user ID and user handle
                tokenManager.saveUserId(authResponse.user.id)
                tokenManager.saveUserHandle(authResponse.user.userHandle)

                // Save user to database
                userDao.insertUser(
                    User(
                        userId = authResponse.user.id,
                        userHandle = authResponse.user.userHandle,
                        displayName = authResponse.user.displayName,
                        profilePictureUrl = authResponse.user.avatarUrl,
                        bio = authResponse.user.bio,
                        phoneNumber = null,
                        email = null,
                        subscriptionTier = if (authResponse.user.isGoldMember) "gold" else "free",
                        verificationStatus = authResponse.user.verificationStatus,
                        isActive = true,
                        createdAt = authResponse.user.createdAt,
                        lastSeenAt = authResponse.user.lastActive
                    )
                )

                // Mark registration as complete
                tokenManager.markRegistrationComplete()

                Result.success(authResponse)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun twoFactorAuth(request: TwoFactorAuthRequest): Result<AuthResponse> {
        return try {
            val response = authApi.twoFactorAuth(request)

            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!

                // Save tokens
                tokenManager.saveTokens(
                    authResponse.accessToken,
                    authResponse.refreshToken
                )

                // Save user ID and user handle
                tokenManager.saveUserId(authResponse.user.id)
                tokenManager.saveUserHandle(authResponse.user.userHandle)

                // Save user to database
                userDao.insertUser(
                    User(
                        userId = authResponse.user.id,
                        userHandle = authResponse.user.userHandle,
                        displayName = authResponse.user.displayName,
                        profilePictureUrl = authResponse.user.avatarUrl,
                        bio = authResponse.user.bio,
                        phoneNumber = null,
                        email = null,
                        subscriptionTier = if (authResponse.user.isGoldMember) "gold" else "free",
                        verificationStatus = authResponse.user.verificationStatus,
                        isActive = true,
                        createdAt = authResponse.user.createdAt,
                        lastSeenAt = authResponse.user.lastActive
                    )
                )

                // Mark registration as complete
                tokenManager.markRegistrationComplete()

                Result.success(authResponse)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun refreshToken(): Result<AuthResponse> {
        val refreshToken = tokenManager.getRefreshToken() ?: return Result.failure(Exception("No refresh token"))

        return try {
            val response = authApi.refreshToken(TokenRefreshRequest(refreshToken))

            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!

                // Save tokens
                tokenManager.saveTokens(
                    authResponse.accessToken,
                    authResponse.refreshToken
                )

                // Save user ID and user handle
                tokenManager.saveUserId(authResponse.user.id)
                tokenManager.saveUserHandle(authResponse.user.userHandle)

                Result.success(authResponse)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun recoverAccount(request: AccountRecoveryRequest): Result<AuthResponse> {
        return try {
            // Step 1: Request recovery token
            val recoveryResponse = authApi.requestRecovery(request)

            if (!recoveryResponse.isSuccessful || recoveryResponse.body() == null) {
                val errorBody = recoveryResponse.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", recoveryResponse.code())
                }

                return Result.failure(Exception(errorResponse.message))
            }

            // Step 2: Confirm recovery with the token
            val recoveryToken = recoveryResponse.body()!!.recoveryToken
            val confirmRequest = RecoveryConfirmRequest(
                recoveryToken = recoveryToken,
                newPassword = request.newPassword
            )

            val confirmResponse = authApi.confirmRecovery(confirmRequest)

            if (confirmResponse.isSuccessful && confirmResponse.body() != null) {
                val authResponse = confirmResponse.body()!!

                // Save tokens
                tokenManager.saveTokens(
                    authResponse.accessToken,
                    authResponse.refreshToken
                )

                // Save user ID and user handle
                tokenManager.saveUserId(authResponse.user.id)
                tokenManager.saveUserHandle(authResponse.user.userHandle)

                // Save user to database
                userDao.insertUser(
                    User(
                        userId = authResponse.user.id,
                        userHandle = authResponse.user.userHandle,
                        displayName = authResponse.user.displayName,
                        profilePictureUrl = authResponse.user.avatarUrl,
                        bio = authResponse.user.bio,
                        phoneNumber = null,
                        email = null,
                        subscriptionTier = if (authResponse.user.isGoldMember) "gold" else "free",
                        verificationStatus = authResponse.user.verificationStatus,
                        isActive = true,
                        createdAt = authResponse.user.createdAt,
                        lastSeenAt = authResponse.user.lastActive
                    )
                )

                // Mark registration as complete
                tokenManager.markRegistrationComplete()

                Result.success(authResponse)
            } else {
                val errorBody = confirmResponse.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", confirmResponse.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun changePassword(request: PasswordChangeRequest): Result<Unit> {
        return try {
            val response = authApi.changePassword(request)

            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                val errorBody = response.errorBody()?.string()
                val errorResponse = try {
                    gson.fromJson(errorBody, ErrorResponse::class.java)
                } catch (e: Exception) {
                    ErrorResponse("Error", errorBody ?: "Unknown error", response.code())
                }

                Result.failure(Exception(errorResponse.message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> {
        val userId = tokenManager.getUserId()

        return if (userId != null) {
            userDao.getCurrentUserFlow(userId)
        } else {
            flow { emit(null) }
        }
    }

    override fun isLoggedIn(): Boolean {
        return tokenManager.isLoggedIn()
    }

    override suspend fun logout() {
        // Clear tokens
        tokenManager.clearTokens()

        // Clear user data
        val userId = tokenManager.getUserId()
        if (userId != null) {
            userDao.deleteUser(userId)
        }
    }
}
