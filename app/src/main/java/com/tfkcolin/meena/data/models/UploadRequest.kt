package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Request to get a pre-signed URL for uploading media.
 */
data class UploadRequest(
    @SerializedName("file_name")
    val fileName: String,
    
    @SerializedName("content_type")
    val contentType: String,
    
    @SerializedName("file_size")
    val fileSize: Long,
    
    @SerializedName("purpose")
    val purpose: String
)
