package com.tfkcolin.meena.data.models

/**
 * Extension functions for the Chat model to provide type-specific functionality
 * while maintaining compatibility with the API contract.
 */

/**
 * Get the other participant's ID in a one-to-one chat.
 *
 * @param currentUserId The current user's ID.
 * @return The other participant's ID, or null if this is not a one-to-one chat.
 */
fun Chat.getOtherParticipantId(currentUserId: String): String? {
    if (!isOneToOne()) return null
    return getParticipantIdsList().firstOrNull { it != currentUserId }
}

/**
 * Check if the current user can modify this group.
 *
 * @param currentUserId The current user's ID.
 * @return True if the user is an admin or the creator of the group.
 */
fun Chat.canUserModifyGroup(currentUserId: String): Boolean {
    if (!isGroup()) return false
    return isUserAdmin(currentUserId) || createdBy == currentUserId
}

/**
 * Get the display name for this chat.
 *
 * @param currentUserId The current user's ID.
 * @param contactNameMap A map of user IDs to display names.
 * @return The display name for the chat.
 */
fun Chat.getDisplayName(currentUserId: String, contactNameMap: Map<String, String>): String {
    return when {
        isOneToOne() -> {
            val otherParticipantId = getOtherParticipantId(currentUserId) ?: ""
            contactNameMap[otherParticipantId] ?: "Unknown"
        }
        else -> name ?: "Unnamed Group"
    }
}

/**
 * Get the avatar URL for this chat.
 *
 * @param currentUserId The current user's ID.
 * @param contactAvatarMap A map of user IDs to avatar URLs.
 * @return The avatar URL for the chat.
 */
fun Chat.getAvatarUrl(currentUserId: String, contactAvatarMap: Map<String, String?>): String? {
    return when {
        isOneToOne() -> {
            val otherParticipantId = getOtherParticipantId(currentUserId) ?: ""
            contactAvatarMap[otherParticipantId]
        }
        else -> avatarUrl
    }
}

/**
 * Check if this chat has the specified participant.
 *
 * @param userId The user ID to check.
 * @return True if the user is a participant in this chat.
 */
fun Chat.hasParticipant(userId: String): Boolean {
    return getParticipantIdsList().contains(userId)
}

/**
 * Get the number of participants in this chat.
 *
 * @return The number of participants.
 */
fun Chat.getParticipantCount(): Int {
    return getParticipantIdsList().size
}

/**
 * Check if this chat is a secret chat.
 *
 * @return True if this is a secret chat.
 */
fun Chat.isSecret(): Boolean {
    return getPrivacyType() == PrivacyType.SECRET
}

/**
 * Check if this chat is a public chat.
 *
 * @return True if this is a public chat.
 */
fun Chat.isPublic(): Boolean {
    return getPrivacyType() == PrivacyType.PUBLIC
}

/**
 * Check if this chat is a private chat.
 *
 * @return True if this is a private chat.
 */
fun Chat.isPrivate(): Boolean {
    return getPrivacyType() == PrivacyType.PRIVATE
}
