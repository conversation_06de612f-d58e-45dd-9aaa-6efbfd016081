package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Media attachment types.
 */
enum class MediaType {
    IMAGE,
    VIDEO,
    AUDIO,
    DOCUMENT,
    LOCATION
}

/**
 * Media attachment model representing a file or media attachment in a message.
 */
@Entity(
    tableName = "media_attachments",
    foreignKeys = [
        ForeignKey(
            entity = Message::class,
            parentColumns = ["id"],
            childColumns = ["messageId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("messageId")
    ]
)
data class MediaAttachment(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("message_id")
    val messageId: String,

    @SerializedName("type")
    val type: String, // "image", "video", "audio", "document", "location"

    @SerializedName("url")
    val url: String,

    @SerializedName("thumbnail_url")
    val thumbnailUrl: String? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("size")
    val size: Long? = null,

    @SerializedName("duration")
    val duration: Long? = null, // For audio/video

    @SerializedName("width")
    val width: Int? = null, // For images/videos

    @SerializedName("height")
    val height: Int? = null, // For images/videos

    @SerializedName("latitude")
    val latitude: Double? = null, // For location

    @SerializedName("longitude")
    val longitude: Double? = null, // For location

    @SerializedName("created_at")
    val createdAt: Long = System.currentTimeMillis(),

    // Local properties (not sent to the server)
    @SerializedName("upload_id")
    val uploadId: String? = null,

    @SerializedName("is_uploaded")
    val isUploaded: Boolean = false,

    @SerializedName("file_name")
    val fileName: String? = null,

    @SerializedName("file_size")
    val fileSize: Long? = null
)
