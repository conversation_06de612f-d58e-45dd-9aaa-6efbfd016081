package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Contact model representing a contact in the user's contact list.
 * This model is used for both API responses and local database storage.
 */
@Entity(
    tableName = "contacts",
    foreignKeys = [
        ForeignKey(
            entity = User::class,
            parentColumns = ["userId"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = User::class,
            parentColumns = ["userId"],
            childColumns = ["contactId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("userId"),
        Index("contactId")
    ]
)
data class Contact(
    @PrimaryKey
    @SerializedName("id")
    val id: String,

    @SerializedName("user_id")
    val userId: String,

    @SerializedName("contact_id")
    val contactId: String,

    @SerializedName("display_name")
    val displayName: String?,

    @SerializedName("relationship")
    val relationship: String, // "friend", "blocked", "pending"

    @SerializedName("notes")
    val notes: String? = null,

    @SerializedName("created_at")
    val createdAt: String?,

    @SerializedName("updated_at")
    val updatedAt: String?,

    @SerializedName("is_favorite")
    val isFavorite: Boolean = false,

    @SerializedName("last_interaction_at")
    val lastInteractionAt: String? = null,

    @SerializedName("user")
    val user: UserProfile? = null
) {
    /**
     * Check if the contact is blocked.
     *
     * @return True if the contact is blocked, false otherwise.
     */
    fun isBlocked(): Boolean {
        return relationship == "blocked"
    }

    /**
     * Check if the contact is a friend.
     *
     * @return True if the contact is a friend, false otherwise.
     */
    fun isFriend(): Boolean {
        return relationship == "friend"
    }

    /**
     * Check if the contact request is pending.
     *
     * @return True if the contact request is pending, false otherwise.
     */
    fun isPending(): Boolean {
        return relationship == "pending"
    }

    /**
     * Get the display name or the user's display name if not set.
     *
     * @return The display name to show.
     */
    fun getDisplayNameOrDefault(): String {
        return displayName ?: user?.displayName ?: "Unknown"
    }
}
