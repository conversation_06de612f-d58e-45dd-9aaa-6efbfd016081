package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * Authentication response model containing tokens and user information.
 * Matches the backend response structure for auth endpoints.
 */
data class AuthResponse(
    @SerializedName("user")
    val user: AuthUserProfile,

    @SerializedName("access_token")
    val accessToken: String,

    @SerializedName("refresh_token")
    val refreshToken: String,

    @SerializedName("expires_in")
    val expiresIn: Long,

    @SerializedName("recovery_phrase")
    val recoveryPhrase: String? = null,

    // This field is not in the backend response but needed for 2FA flow
    // It will be false by default
    val requires2fa: Boolean = false
)

/**
 * User profile model containing user information from authentication response.
 */
data class AuthUserProfile(
    @SerializedName("id")
    val id: String,

    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String,

    @SerializedName("bio")
    val bio: String,

    @SerializedName("avatar_url")
    val avatarUrl: String,

    @SerializedName("verification_status")
    val verificationStatus: String = "none",

    @SerializedName("is_verified")
    val isVerified: Boolean,

    @SerializedName("is_gold_member")
    val isGoldMember: Boolean,

    @SerializedName("last_active")
    val lastActive: String,

    @SerializedName("created_at")
    val createdAt: String,

    @SerializedName("follower_count")
    val followerCount: Int,

    @SerializedName("following_count")
    val followingCount: Int
)

/**
 * Error response model for API errors.
 */
data class ErrorResponse(
    @SerializedName("error")
    val error: String,

    @SerializedName("message")
    val message: String,

    @SerializedName("status")
    val status: Int
)
