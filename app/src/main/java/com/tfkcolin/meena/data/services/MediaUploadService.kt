package com.tfkcolin.meena.data.services

import android.content.Context
import android.net.Uri
import android.util.Log
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaType
import com.tfkcolin.meena.data.models.UploadProgress
import com.tfkcolin.meena.data.models.UploadRequest
import com.tfkcolin.meena.data.models.UploadResult
import com.tfkcolin.meena.utils.FileUtils
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okio.Buffer
import okio.BufferedSink
import okio.ForwardingSink
import okio.IOException
import okio.buffer
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for uploading media files to the server.
 */
@Singleton
class MediaUploadService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val mediaApi: MediaApi,
    private val okHttpClient: OkHttpClient,
    private val fileUtils: FileUtils,
    private val mediaAttachmentHelper: MediaAttachmentHelper
) {
    companion object {
        private const val TAG = "MediaUploadService"
    }

    // Map of media ID to upload progress
    private val _uploadProgressMap = MutableStateFlow<Map<String, UploadProgress>>(emptyMap())
    val uploadProgressMap = _uploadProgressMap.asStateFlow()

    // Map of media ID to active upload calls
    private val activeUploads = mutableMapOf<String, okhttp3.Call>()

    /**
     * Get the upload progress for a specific media attachment.
     *
     * @param mediaId The media ID.
     * @return The upload progress flow.
     */
    fun getUploadProgress(mediaId: String): Flow<UploadProgress?> {
        return MutableStateFlow(uploadProgressMap.value[mediaId])
    }

    /**
     * Upload a media file to the server.
     *
     * @param uri The URI of the media file.
     * @param purpose The purpose of the upload (e.g., "message_media", "profile_picture").
     * @return The upload result.
     */
    suspend fun uploadMedia(uri: Uri, purpose: String): UploadResult = withContext(Dispatchers.IO) {
        try {
            // Create a temporary attachment to get metadata
            val tempAttachment = mediaAttachmentHelper.createAttachmentFromUri(uri)
                ?: return@withContext UploadResult.Error("Failed to create attachment from URI")

            // Update progress to indicate we're preparing the upload
            updateProgress(tempAttachment.id, UploadProgress.Preparing(tempAttachment.id))

            // Get file metadata
            val fileName = fileUtils.getFileName(uri)
            val fileSize = fileUtils.getFileSize(uri) ?: 0L
            val mimeType = fileUtils.getMimeType(uri) ?: "application/octet-stream"

            // Request upload URL from the server
            val uploadRequest = UploadRequest(
                fileName = fileName,
                contentType = mimeType,
                fileSize = fileSize,
                purpose = purpose
            )

            // Update progress to indicate we're requesting upload URL
            updateProgress(tempAttachment.id, UploadProgress.RequestingUploadUrl(tempAttachment.id))

            // Get upload URL from the server
            val uploadUrlResponse = mediaApi.requestUploadUrl(uploadRequest)
            val mediaId = uploadUrlResponse.mediaId
            val uploadUrl = uploadUrlResponse.uploadUrl

            // Copy the file to a temporary location
            val file = fileUtils.copyFileToCache(uri)
                ?: return@withContext UploadResult.Error("Failed to copy file to cache")

            // Create a request body from the file
            val requestBody = file.asRequestBody(mimeType.toMediaTypeOrNull())

            // Create a progress tracking request body
            val progressRequestBody = ProgressRequestBody(
                requestBody = requestBody,
                contentType = mimeType.toMediaTypeOrNull(),
                onProgressUpdate = { bytesWritten, contentLength ->
                    // Calculate percentage safely
                    val progressPercent = if (contentLength > 0) {
                        (bytesWritten * 100 / contentLength).toInt()
                    } else {
                        -1 // Indeterminate progress
                    }

                    if (progressPercent != -1) {
                        Log.d(TAG, "Upload Progress ($mediaId): $progressPercent% ($bytesWritten/$contentLength)")
                        updateProgress(mediaId, UploadProgress.Uploading(mediaId, progressPercent))
                    } else {
                        Log.d(TAG, "Upload Progress ($mediaId): $bytesWritten bytes (unknown total size)")
                        // Show 0% if length unknown
                        updateProgress(mediaId, UploadProgress.Uploading(mediaId, 0))
                    }
                }
            )

            // Create a multipart body part
            val filePart = MultipartBody.Part.createFormData(
                "file",
                fileName,
                progressRequestBody
            )

            // Create the request
            val request = Request.Builder()
                .url(uploadUrl)
                .post(filePart.body)
                .build()

            // Create the call and store it in activeUploads
            val call = okHttpClient.newCall(request)
            synchronized(activeUploads) {
                activeUploads[mediaId] = call
            }

            // Execute the call
            val response = call.execute()

            // Remove the call from activeUploads
            synchronized(activeUploads) {
                activeUploads.remove(mediaId)
            }

            // Check if the response was successful
            if (!response.isSuccessful) {
                throw IOException("Unexpected response code: ${response.code}")
            }

            // Update progress to indicate we're completing the upload
            updateProgress(mediaId, UploadProgress.Completing(mediaId))

            // Notify the server that the upload is complete
            val completeResponse = mediaApi.completeUpload(mediaId)

            // Create a media attachment from the response
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "", // Will be set when attached to a message
                type = getMediaTypeFromMimeType(mimeType).name.lowercase(),
                url = completeResponse.fileUrl,
                thumbnailUrl = completeResponse.thumbnailUrl,
                name = fileName,
                size = fileSize,
                duration = tempAttachment.duration,
                width = tempAttachment.width,
                height = tempAttachment.height,
                latitude = tempAttachment.latitude,
                longitude = tempAttachment.longitude
            )

            // Update progress to indicate the upload is complete
            updateProgress(mediaId, UploadProgress.Complete(mediaId))

            return@withContext UploadResult.Success(mediaAttachment)
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading media", e)
            return@withContext UploadResult.Error(e.message ?: "Unknown error")
        }
    }

    /**
     * Upload a local media attachment to the server.
     *
     * @param attachment The media attachment to upload.
     * @param purpose The purpose of the upload (e.g., "message_media", "profile_picture").
     * @return The upload result.
     */
    suspend fun uploadMediaAttachment(
        attachment: MediaAttachment,
        purpose: String
    ): UploadResult = withContext(Dispatchers.IO) {
        try {
            // Update progress to indicate we're preparing the upload
            updateProgress(attachment.id, UploadProgress.Preparing(attachment.id))

            // Get file metadata
            val file = File(attachment.url)
            if (!file.exists()) {
                return@withContext UploadResult.Error("File does not exist")
            }

            val fileName = attachment.name ?: file.name
            val fileSize = attachment.size ?: file.length()
            val mimeType = when (attachment.type) {
                "image" -> "image/jpeg"
                "video" -> "video/mp4"
                "audio" -> "audio/mpeg"
                "document" -> "application/octet-stream"
                else -> "application/octet-stream"
            }

            // Request upload URL from the server
            val uploadRequest = UploadRequest(
                fileName = fileName,
                contentType = mimeType,
                fileSize = fileSize,
                purpose = purpose
            )

            // Update progress to indicate we're requesting upload URL
            updateProgress(attachment.id, UploadProgress.RequestingUploadUrl(attachment.id))

            // Get upload URL from the server
            val uploadUrlResponse = mediaApi.requestUploadUrl(uploadRequest)
            val mediaId = uploadUrlResponse.mediaId
            val uploadUrl = uploadUrlResponse.uploadUrl

            // Create a request body from the file
            val requestBody = file.asRequestBody(mimeType.toMediaTypeOrNull())

            // Create a progress tracking request body
            val progressRequestBody = ProgressRequestBody(
                requestBody = requestBody,
                contentType = mimeType.toMediaTypeOrNull(),
                onProgressUpdate = { bytesWritten, contentLength ->
                    // Calculate percentage safely
                    val progressPercent = if (contentLength > 0) {
                        (bytesWritten * 100 / contentLength).toInt()
                    } else {
                        -1 // Indeterminate progress
                    }

                    if (progressPercent != -1) {
                        Log.d(TAG, "Upload Progress ($mediaId): $progressPercent% ($bytesWritten/$contentLength)")
                        updateProgress(mediaId, UploadProgress.Uploading(mediaId, progressPercent))
                    } else {
                        Log.d(TAG, "Upload Progress ($mediaId): $bytesWritten bytes (unknown total size)")
                        // Show 0% if length unknown
                        updateProgress(mediaId, UploadProgress.Uploading(mediaId, 0))
                    }
                }
            )

            // Create a multipart body part
            val filePart = MultipartBody.Part.createFormData(
                "file",
                fileName,
                progressRequestBody
            )

            // Create the request
            val request = Request.Builder()
                .url(uploadUrl)
                .post(filePart.body)
                .build()

            // Create the call and store it in activeUploads
            val call = okHttpClient.newCall(request)
            synchronized(activeUploads) {
                activeUploads[mediaId] = call
            }

            // Execute the call
            val response = call.execute()

            // Remove the call from activeUploads
            synchronized(activeUploads) {
                activeUploads.remove(mediaId)
            }

            // Check if the response was successful
            if (!response.isSuccessful) {
                throw IOException("Unexpected response code: ${response.code}")
            }

            // Update progress to indicate we're completing the upload
            updateProgress(mediaId, UploadProgress.Completing(mediaId))

            // Notify the server that the upload is complete
            val completeResponse = mediaApi.completeUpload(mediaId)

            // Create a media attachment from the response
            val mediaAttachment = attachment.copy(
                id = mediaId,
                url = completeResponse.fileUrl,
                thumbnailUrl = completeResponse.thumbnailUrl ?: attachment.thumbnailUrl
            )

            // Update progress to indicate the upload is complete
            updateProgress(mediaId, UploadProgress.Complete(mediaId))

            return@withContext UploadResult.Success(mediaAttachment)
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading media attachment", e)
            updateProgress(attachment.id, UploadProgress.Error(attachment.id, e.message ?: "Unknown error"))
            return@withContext UploadResult.Error(e.message ?: "Unknown error")
        }
    }

    /**
     * Update the progress of an upload.
     *
     * @param mediaId The media ID.
     * @param progress The upload progress.
     */
    private fun updateProgress(mediaId: String, progress: UploadProgress) {
        _uploadProgressMap.value = _uploadProgressMap.value.toMutableMap().apply {
            this[mediaId] = progress
        }
    }

    /**
     * Cancel an upload.
     *
     * @param mediaId The media ID of the upload to cancel.
     * @return True if the upload was cancelled, false otherwise.
     */
    suspend fun cancelUpload(mediaId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // Get the active call
            val call = synchronized(activeUploads) {
                activeUploads[mediaId]
            }

            if (call != null) {
                // Cancel the call
                call.cancel()

                // Remove the call from activeUploads
                synchronized(activeUploads) {
                    activeUploads.remove(mediaId)
                }

                // Update progress to indicate the upload was cancelled
                updateProgress(mediaId, UploadProgress.Error(mediaId, "Upload cancelled"))

                return@withContext true
            } else {
                // No active call found, check if we have a progress entry
                val progress = _uploadProgressMap.value[mediaId]
                if (progress != null) {
                    // Update progress to indicate the upload was cancelled
                    updateProgress(mediaId, UploadProgress.Error(mediaId, "Upload cancelled"))
                    return@withContext true
                }
            }

            return@withContext false
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling upload", e)
            return@withContext false
        }
    }

    /**
     * Get the media type from a MIME type.
     *
     * @param mimeType The MIME type.
     * @return The media type.
     */
    private fun getMediaTypeFromMimeType(mimeType: String): MediaType {
        return when {
            mimeType.startsWith("image/") -> MediaType.IMAGE
            mimeType.startsWith("video/") -> MediaType.VIDEO
            mimeType.startsWith("audio/") -> MediaType.AUDIO
            else -> MediaType.DOCUMENT
        }
    }

    /**
     * A request body that tracks upload progress.
     * This implementation follows the standard pattern using Okio's ForwardingSink.
     *
     * @param requestBody The original request body (e.g., from a file).
     * @param contentType The content type of the body.
     * @param onProgressUpdate Callback invoked with progress updates (bytesWritten, totalBytes).
     */
    private class ProgressRequestBody(
        private val requestBody: RequestBody,
        private val contentType: okhttp3.MediaType?,
        private val onProgressUpdate: (bytesWritten: Long, contentLength: Long) -> Unit
    ) : RequestBody() {

        override fun contentType(): okhttp3.MediaType? = contentType

        override fun contentLength(): Long {
            // Return the actual content length, handling potential errors from delegate.
            return try {
                requestBody.contentLength()
            } catch (e: IOException) {
                Log.e("ProgressRequestBody", "Could not get content length", e)
                -1 // Indicate unknown length on error
            }
        }

        @Throws(IOException::class)
        override fun writeTo(sink: BufferedSink) {
            val actualContentLength = contentLength() // Get length once

            // Create a ForwardingSink that wraps the original sink
            val countingSink = object : ForwardingSink(sink) {
                var bytesWritten = 0L

                @Throws(IOException::class)
                override fun write(source: Buffer, byteCount: Long) {
                    super.write(source, byteCount) // Write to the underlying sink first
                    bytesWritten += byteCount
                    // Report progress, ensure total bytes is valid for percentage calculation
                    onProgressUpdate(bytesWritten, actualContentLength)
                }
            }

            // Create a BufferedSink that writes to our countingSink
            // This buffering is important for performance, especially for file uploads.
            val bufferedCountingSink = countingSink.buffer()

            // Write the original request body's data to our buffered counting sink.
            requestBody.writeTo(bufferedCountingSink)

            // Ensure all buffered data is flushed down to the original sink.
            // This is crucial to ensure the request completes.
            bufferedCountingSink.flush()
        }
    }
}
