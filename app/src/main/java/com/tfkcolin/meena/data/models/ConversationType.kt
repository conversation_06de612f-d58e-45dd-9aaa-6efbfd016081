package com.tfkcolin.meena.data.models

/**
 * Enum representing the different types of conversations.
 */
enum class ConversationType(val value: String) {
    ONE_TO_ONE("one_to_one"),
    GROUP("group"),
    CHANNEL("channel");

    companion object {
        /**
         * Get the conversation type from a string value.
         *
         * @param value The string value.
         * @return The conversation type, or ONE_TO_ONE if the value is not recognized.
         */
        fun fromString(value: String?): ConversationType {
            return values().find { it.value == value } ?: ONE_TO_ONE
        }
    }
}
