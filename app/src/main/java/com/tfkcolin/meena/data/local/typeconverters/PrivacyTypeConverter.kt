package com.tfkcolin.meena.data.local.typeconverters

import androidx.room.TypeConverter
import com.tfkcolin.meena.data.models.PrivacyType

/**
 * Type converter for PrivacyType enum.
 */
class PrivacyTypeConverter {
    
    /**
     * Convert a PrivacyType to a String.
     *
     * @param privacyType The PrivacyType to convert.
     * @return The String representation of the PrivacyType.
     */
    @TypeConverter
    fun fromPrivacyType(privacyType: PrivacyType?): String? {
        return privacyType?.value
    }
    
    /**
     * Convert a String to a PrivacyType.
     *
     * @param value The String to convert.
     * @return The PrivacyType.
     */
    @TypeConverter
    fun toPrivacyType(value: String?): PrivacyType? {
        return value?.let { PrivacyType.fromString(it) }
    }
}
