package com.tfkcolin.meena.data.services

import android.content.Context
import android.os.Environment
import android.util.Log
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.DownloadResult
import com.tfkcolin.meena.data.models.MediaAttachment
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for downloading media files from the server.
 */
@Singleton
class MediaDownloadService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val okHttpClient: OkHttpClient
) {
    companion object {
        private const val TAG = "MediaDownloadService"
    }

    // Map of media ID to download progress
    private val _downloadProgressMap = MutableStateFlow<Map<String, DownloadProgress>>(emptyMap())
    val downloadProgressMap = _downloadProgressMap.asStateFlow()

    // Map of media ID to active download calls
    private val activeDownloads = mutableMapOf<String, okhttp3.Call>()

    /**
     * Get the download progress for a specific media attachment.
     *
     * @param mediaId The media ID.
     * @return The download progress flow.
     */
    fun getDownloadProgress(mediaId: String): Flow<DownloadProgress?> {
        return MutableStateFlow(downloadProgressMap.value[mediaId])
    }

    /**
     * Download a media file from the server.
     *
     * @param attachment The media attachment to download.
     * @param saveToGallery Whether to save the file to the gallery.
     * @return The download result.
     */
    suspend fun downloadMedia(
        attachment: MediaAttachment,
        saveToGallery: Boolean = false
    ): DownloadResult = withContext(Dispatchers.IO) {
        try {
            // Check if the file is already downloaded
            val cachedFile = getCachedFile(attachment)
            if (cachedFile.exists()) {
                updateProgress(attachment.id, DownloadProgress.Complete(attachment.id))
                return@withContext DownloadResult.Success(cachedFile)
            }

            // Update progress to indicate we're preparing the download
            updateProgress(attachment.id, DownloadProgress.Preparing(attachment.id))

            // Create the parent directory if it doesn't exist
            cachedFile.parentFile?.mkdirs()

            // Create the request
            val request = Request.Builder()
                .url(attachment.url)
                .build()

            // Create the call and store it in activeDownloads
            val call = okHttpClient.newCall(request)
            synchronized(activeDownloads) {
                activeDownloads[attachment.id] = call
            }

            // Execute the call
            call.execute().use { response ->
                // Remove the call from activeDownloads
                synchronized(activeDownloads) {
                    activeDownloads.remove(attachment.id)
                }

                if (!response.isSuccessful) {
                    throw IOException("Unexpected response code: ${response.code}")
                }

                // Get the content length
                val contentLength = response.body?.contentLength() ?: -1L

                // Create the output stream
                val outputStream = FileOutputStream(cachedFile)

                // Download the file
                response.body?.let { body ->
                    val buffer = ByteArray(8192) // 8 KB buffer
                    var bytesRead: Int
                    var bytesDownloaded = 0L

                    body.byteStream().use { inputStream ->
                        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                            outputStream.write(buffer, 0, bytesRead)
                            bytesDownloaded += bytesRead

                            // Update progress
                            if (contentLength > 0) {
                                val progress = (bytesDownloaded * 100 / contentLength).toInt()
                                updateProgress(attachment.id, DownloadProgress.Downloading(attachment.id, progress))
                            }
                        }
                    }

                    outputStream.flush()
                    outputStream.close()
                }
            }

            // Save to gallery if requested
            if (saveToGallery) {
                saveToGallery(attachment, cachedFile)
            }

            // Update progress to indicate the download is complete
            updateProgress(attachment.id, DownloadProgress.Complete(attachment.id))

            return@withContext DownloadResult.Success(cachedFile)
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading media", e)
            updateProgress(attachment.id, DownloadProgress.Error(attachment.id, e.message ?: "Unknown error"))
            return@withContext DownloadResult.Error(e.message ?: "Unknown error")
        }
    }

    /**
     * Get the cached file for a media attachment.
     *
     * @param attachment The media attachment.
     * @return The cached file.
     */
    fun getCachedFile(attachment: MediaAttachment): File {
        val cacheDir = File(context.cacheDir, "media")
        return File(cacheDir, "${attachment.id}_${attachment.name ?: "file"}")
    }

    /**
     * Save a file to the gallery.
     *
     * @param attachment The media attachment.
     * @param file The file to save.
     */
    private fun saveToGallery(attachment: MediaAttachment, file: File) {
        try {
            val fileName = attachment.name ?: "file"
            val mimeType = when (attachment.type) {
                "image" -> "image/jpeg"
                "video" -> "video/mp4"
                "audio" -> "audio/mpeg"
                else -> "application/octet-stream"
            }

            val directory = when (attachment.type) {
                "image" -> Environment.DIRECTORY_PICTURES
                "video" -> Environment.DIRECTORY_MOVIES
                "audio" -> Environment.DIRECTORY_MUSIC
                else -> Environment.DIRECTORY_DOWNLOADS
            }

            val contentValues = android.content.ContentValues().apply {
                put(android.provider.MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                put(android.provider.MediaStore.MediaColumns.MIME_TYPE, mimeType)
                put(android.provider.MediaStore.MediaColumns.SIZE, file.length())
            }

            val resolver = context.contentResolver
            val uri = resolver.insert(
                android.provider.MediaStore.Files.getContentUri("external"),
                contentValues
            ) ?: return

            resolver.openOutputStream(uri)?.use { outputStream ->
                file.inputStream().use { inputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to gallery", e)
        }
    }

    /**
     * Update the progress of a download.
     *
     * @param mediaId The media ID.
     * @param progress The download progress.
     */
    private fun updateProgress(mediaId: String, progress: DownloadProgress) {
        _downloadProgressMap.value = _downloadProgressMap.value.toMutableMap().apply {
            this[mediaId] = progress
        }
    }

    /**
     * Cancel a download.
     *
     * @param mediaId The media ID of the download to cancel.
     * @return True if the download was cancelled, false otherwise.
     */
    suspend fun cancelDownload(mediaId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // Get the active call
            val call = synchronized(activeDownloads) {
                activeDownloads[mediaId]
            }

            if (call != null) {
                // Cancel the call
                call.cancel()

                // Remove the call from activeDownloads
                synchronized(activeDownloads) {
                    activeDownloads.remove(mediaId)
                }

                // Update progress to indicate the download was cancelled
                updateProgress(mediaId, DownloadProgress.Error(mediaId, "Download cancelled"))

                return@withContext true
            } else {
                // No active call found, check if we have a progress entry
                val progress = _downloadProgressMap.value[mediaId]
                if (progress != null) {
                    // Update progress to indicate the download was cancelled
                    updateProgress(mediaId, DownloadProgress.Error(mediaId, "Download cancelled"))
                    return@withContext true
                }
            }

            return@withContext false
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling download", e)
            return@withContext false
        }
    }
}
