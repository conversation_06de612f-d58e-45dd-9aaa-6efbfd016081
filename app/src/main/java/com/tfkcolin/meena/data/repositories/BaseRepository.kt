package com.tfkcolin.meena.data.repositories

import com.tfkcolin.meena.utils.AppError
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.toAppError
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart

/**
 * Base interface for all repositories.
 * Provides common functionality for repositories.
 */
interface BaseRepository {
    /**
     * Execute a network operation and return a Result.
     *
     * @param operation The operation to execute.
     * @return A Result containing the operation result or an exception.
     */
    suspend fun <T> executeNetworkOperation(operation: suspend () -> T): Result<T> {
        return try {
            Result.success(operation())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Execute a database operation and return a Result.
     *
     * @param operation The operation to execute.
     * @return A Result containing the operation result or an exception.
     */
    suspend fun <T> executeDatabaseOperation(operation: suspend () -> T): Result<T> {
        return try {
            Result.success(operation())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Execute a file operation and return a Result.
     *
     * @param operation The operation to execute.
     * @return A Result containing the operation result or an exception.
     */
    suspend fun <T> executeFileOperation(operation: suspend () -> T): Result<T> {
        return try {
            Result.success(operation())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * Extension function to map a Flow to a Result Flow.
 *
 * @param errorHandler The error handler to use.
 * @return A Flow of Results.
 */
fun <T> Flow<T>.asResultFlow(errorHandler: ErrorHandler): Flow<Result<T>> {
    return this
        .map { Result.success(it) }
        .catch { e -> emit(Result.failure(e)) }
}

/**
 * Extension function to map a Flow to a Resource Flow.
 *
 * @return A Flow of Resources.
 */
fun <T> Flow<T>.asResourceFlow(): Flow<Resource<T>> {
    return this
        .map { Resource.Success(it) as Resource<T> }
        .onStart { emit(Resource.Loading()) }
        .catch { e -> emit(Resource.Error(e.toAppError())) }
}

/**
 * Sealed class representing a resource that can be in one of three states: loading, success, or error.
 */
sealed class Resource<out T> {
    /**
     * Resource is in loading state.
     */
    class Loading<T> : Resource<T>()
    
    /**
     * Resource is in success state with data.
     *
     * @param data The data.
     */
    data class Success<T>(val data: T) : Resource<T>()
    
    /**
     * Resource is in error state with an error.
     *
     * @param error The error.
     */
    data class Error<T>(val error: AppError) : Resource<T>()
}
