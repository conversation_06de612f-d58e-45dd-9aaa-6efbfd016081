package com.tfkcolin.meena.data.models

/**
 * Enum representing the different privacy types for groups and channels.
 */
enum class PrivacyType(val value: String) {
    PUBLIC("public"),
    PRIVATE("private"),
    SECRET("secret");

    companion object {
        /**
         * Get the privacy type from a string value.
         *
         * @param value The string value.
         * @return The privacy type, or PRIVATE if the value is not recognized.
         */
        fun fromString(value: String?): PrivacyType {
            return values().find { it.value == value } ?: PRIVATE
        }
    }
}
