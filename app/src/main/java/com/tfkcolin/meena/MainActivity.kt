package com.tfkcolin.meena

import android.app.Activity
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts.StartIntentSenderForResult
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.tfkcolin.meena.ui.auth.AuthViewModel
import com.tfkcolin.meena.ui.navigation.AUTH_GRAPH_ROUTE
import com.tfkcolin.meena.ui.navigation.NavGraph
import com.tfkcolin.meena.ui.navigation.Screen
import com.tfkcolin.meena.ui.theme.MeenaTheme
import com.tfkcolin.meena.utils.TokenManager
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import android.util.Log
import android.widget.Toast
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.Button
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.InstallStatus
import com.tfkcolin.meena.ui.navigation.MAIN_GRAPH_ROUTE

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var tokenManager: TokenManager
    private lateinit var appUpdateManager: AppUpdateManager
    private lateinit var activityResultLauncher: ActivityResultLauncher<IntentSenderRequest>

    private val STALENESS_DAYS_THRESHOLD = 2
    private val TAG = "AppUpdate"

    private var showUpdateDownloadedDialog by mutableStateOf(false)

    private val installStateUpdatedListener = InstallStateUpdatedListener { state ->
        if (state.installStatus() == InstallStatus.DOWNLOADED) {
            // After the update is downloaded, show a notification and request user confirmation to restart the app.
            showUpdateDownloadedDialog = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        appUpdateManager = AppUpdateManagerFactory.create(this)
        activityResultLauncher =
            registerForActivityResult(StartIntentSenderForResult()) { result ->
                if (result.resultCode != Activity.RESULT_OK) {
                    Log.e(TAG, "Update flow failed! Result code: ${result.resultCode}")
                    // Optionally, show a message to the user that the update was cancelled or failed.
                    Toast.makeText(
                        this,
                        "An update failed or was cancelled.",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }

        checkForAppUpdate()

        enableEdgeToEdge()
        setContent {
            MeenaTheme {
                val navController = rememberNavController()
                val authViewModel: AuthViewModel = hiltViewModel()
                val authState by authViewModel.authState.collectAsState()

                // Determine start destination based on login state
                val startDestination = if (authState.isLoggedIn) {
                    // User is fully logged in
                    MAIN_GRAPH_ROUTE
                } else {
                    // User is not logged in or hasn't completed registration
                    // Always start with the auth graph, and the auth flow will handle
                    // directing the user to the appropriate screen
                    AUTH_GRAPH_ROUTE
                }

                // If the user has tokens but hasn't completed registration,
                // we'll handle this in the NavGraph component

                NavGraph(
                    navController = navController,
                    startDestination = startDestination
                )

                if (showUpdateDownloadedDialog) {
                    AlertDialog(
                        onDismissRequest = { /* Dialog is not dismissible by clicking outside */ },
                        title = { Text("Update Downloaded") },
                        text = { Text("An update has just been downloaded. Please restart the app to complete the update.") },
                        confirmButton = {
                            Button(onClick = {
                                appUpdateManager.completeUpdate()
                                showUpdateDownloadedDialog = false
                            }) {
                                Text("RESTART")
                            }
                        }
                    )
                }
            }
        }
    }

    private fun checkForAppUpdate() {
        // Returns an intent object that you use to check for an update.
        val appUpdateInfoTask = appUpdateManager.appUpdateInfo

        // Checks that the platform will allow the specified type of update.
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE) &&
                    (appUpdateInfo.clientVersionStalenessDays() ?: -1) >= STALENESS_DAYS_THRESHOLD) {
                    // Start a flexible update
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activityResultLauncher,
                        AppUpdateOptions.newBuilder(AppUpdateType.FLEXIBLE).build()
                    )
                    appUpdateManager.registerListener(installStateUpdatedListener)
                } else if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                    // Start an immediate update
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activityResultLauncher,
                        AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build()
                    )
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()

        appUpdateManager
            .appUpdateInfo
            .addOnSuccessListener { appUpdateInfo ->
                if (appUpdateInfo.updateAvailability()
                    == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS
                ) {
                    // If an in-app update is already running, resume the update.
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activityResultLauncher,
                        AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build())
                } else if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                    // If a flexible update is downloaded but not yet installed, prompt the user to complete it.
                    showUpdateDownloadedDialog = true
                }
            }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Unregister the listener to avoid memory leaks
        appUpdateManager.unregisterListener(installStateUpdatedListener)
    }
}
