package com.tfkcolin.meena

import android.app.Application
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.mock.MockInitializer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class MeenaApplication : Application() {

    @Inject
    lateinit var mockInitializer: MockInitializer

    override fun onCreate() {
        super.onCreate()

        // Initialize mock backend if enabled
        if (AppConfig.useMockBackend) {
            mockInitializer.initialize()
        }
    }
}
