package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import javax.inject.Inject

/**
 * Use case for updating a contact group.
 */
class UpdateContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Update a contact group.
     *
     * @param groupId The group ID.
     * @param name The new group name.
     * @param description The new group description.
     * @return The updated contact group.
     */
    suspend operator fun invoke(
        groupId: String,
        name: String,
        description: String? = null
    ): ContactGroup {
        return contactGroupRepository.updateContactGroup(groupId, name, description)
    }
}
