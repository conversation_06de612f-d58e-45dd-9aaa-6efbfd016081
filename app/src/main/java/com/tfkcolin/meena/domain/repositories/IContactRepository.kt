package com.tfkcolin.meena.domain.repositories

import com.tfkcolin.meena.data.models.*
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for contact operations.
 */
interface IContactRepository {

    /**
     * Get the user's contact list.
     *
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @param relationship Optional filter for relationship type.
     * @return The result of the get contacts operation.
     */
    suspend fun getContacts(
        limit: Int = 50,
        offset: Int = 0,
        relationship: String? = null
    ): Result<ContactListResponse>

    /**
     * Get the user's contact list as a flow.
     *
     * @return A flow of the user's contacts.
     */
    fun getContactsFlow(): Flow<List<Contact>>

    /**
     * Get contacts with a specific relationship as a flow.
     *
     * @param relationship The relationship type.
     * @return A flow of contacts with the specified relationship.
     */
    fun getContactsByRelationshipFlow(relationship: String): Flow<List<Contact>>

    /**
     * Get a contact by ID.
     *
     * @param contactId The contact ID.
     * @return The result of the get contact operation.
     */
    suspend fun getContactById(contactId: String): Result<ContactResponse>

    /**
     * Add a contact to the user's contact list.
     *
     * @param userHandle The user handle of the contact to add.
     * @param displayName Optional custom display name for the contact.
     * @param notes Optional notes about the contact.
     * @return The result of the add contact operation.
     */
    suspend fun addContact(
        userHandle: String,
        displayName: String? = null,
        notes: String? = null
    ): Result<ContactResponse>

    /**
     * Update a contact in the user's contact list.
     *
     * @param contactId The contact ID.
     * @param displayName Optional new display name.
     * @param notes Optional new notes.
     * @param relationship Optional new relationship.
     * @return The result of the update contact operation.
     */
    suspend fun updateContact(
        contactId: String,
        displayName: String? = null,
        notes: String? = null,
        relationship: String? = null
    ): Result<ContactResponse>

    /**
     * Delete a contact.
     *
     * @param contactId The contact ID.
     * @return The result of the delete contact operation.
     */
    suspend fun deleteContact(contactId: String): Result<Unit>

    /**
     * Search contacts.
     *
     * @param query The search query.
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The result of the search operation.
     */
    suspend fun searchContacts(
        query: String,
        limit: Int = 50,
        offset: Int = 0
    ): Result<ContactListResponse>

    /**
     * Get recent contacts.
     *
     * @param limit The maximum number of contacts to return.
     * @return The result of the get recent contacts operation.
     */
    suspend fun getRecentContacts(limit: Int = 10): Result<ContactListResponse>

    /**
     * Get favorite contacts.
     *
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The result of the get favorite contacts operation.
     */
    suspend fun getFavoriteContacts(
        limit: Int = 50,
        offset: Int = 0
    ): Result<ContactListResponse>

    /**
     * Add a contact to favorites.
     *
     * @param contactId The contact ID.
     * @return The result of the add to favorites operation.
     */
    suspend fun addToFavorites(contactId: String): Result<Unit>

    /**
     * Remove a contact from favorites.
     *
     * @param contactId The contact ID.
     * @return The result of the remove from favorites operation.
     */
    suspend fun removeFromFavorites(contactId: String): Result<Unit>

    /**
     * Block a contact.
     *
     * @param contactId The contact ID.
     * @return The result of the block contact operation.
     */
    suspend fun blockContact(contactId: String): Result<Unit>

    /**
     * Unblock a contact.
     *
     * @param contactId The contact ID.
     * @return The result of the unblock contact operation.
     */
    suspend fun unblockContact(contactId: String): Result<Unit>
}
