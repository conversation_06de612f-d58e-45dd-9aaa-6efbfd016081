package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for forwarding a message to another chat.
 */
class ForwardMessageUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Forward a message to another chat.
     *
     * @param messageId The message ID to forward.
     * @param chatId The chat ID to forward to.
     * @param additionalContent Additional content to add to the forwarded message.
     * @return The result of the forward operation.
     */
    suspend operator fun invoke(
        messageId: String,
        chatId: String,
        additionalContent: String? = null
    ): Result<Message> {
        return chatRepository.forwardMessage(
            messageId = messageId,
            chatId = chatId,
            additionalContent = additionalContent
        )
    }
}
