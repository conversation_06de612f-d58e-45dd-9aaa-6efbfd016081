package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for adding members to a group.
 */
class AddGroupChatParticipantsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Add members to a group.
     *
     * @param groupId The group ID.
     * @param userHandles The user handles to add.
     * @return The result of the add operation.
     */
    suspend operator fun invoke(
        groupId: String,
        userHandles: List<String>
    ): Result<GroupResponse> {
        return chatRepository.addGroupMembers(
            groupId = groupId,
            userHandles = userHandles
        )
    }
}
