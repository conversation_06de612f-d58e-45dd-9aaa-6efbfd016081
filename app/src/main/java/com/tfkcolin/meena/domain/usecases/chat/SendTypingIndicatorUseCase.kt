package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for sending typing indicators.
 */
class SendTypingIndicatorUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Send typing indicator for a chat.
     * 
     * @param chatId The chat ID.
     * @param isTyping Whether the user is typing.
     * @return The result of the operation.
     */
    suspend operator fun invoke(
        chatId: String,
        isTyping: Boolean
    ): Result<Unit> {
        return chatRepository.sendTypingIndicator(chatId, isTyping)
    }
}
