package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for logging out a user.
 */
class LogoutUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Log out the current user.
     */
    suspend operator fun invoke() {
        authRepository.logout()
    }
}
