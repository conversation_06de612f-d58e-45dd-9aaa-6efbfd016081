package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for updating a contact in the user's contact list.
 */
class UpdateContactUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {

    /**
     * Update a contact in the user's contact list.
     *
     * @param contactId The contact ID.
     * @param displayName Optional new display name.
     * @param notes Optional new notes.
     * @param relationship Optional new relationship.
     * @return The result of the update contact operation.
     */
    suspend operator fun invoke(
        contactId: String,
        displayName: String? = null,
        notes: String? = null,
        relationship: String? = null
    ): Result<ContactResponse> {
        return contactRepository.updateContact(
            contactId = contactId,
            displayName = displayName,
            notes = notes,
            relationship = relationship
        )
    }
}
