package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.utils.TokenManager
import javax.inject.Inject

/**
 * Use case for marking all messages in a chat as read in the local database.
 */
class MarkChatMessagesAsReadUseCase @Inject constructor(
    private val messageDao: MessageDao,
    private val chatDao: ChatDao,
    private val tokenManager: TokenManager
) {

    /**
     * Mark all messages in a chat as read.
     *
     * @param chatId The chat ID.
     */
    suspend operator fun invoke(chatId: String) {
        val currentUserId = tokenManager.getUserId() ?: return

        // Get all unread messages for the current user in this chat
        val unreadMessages = messageDao.getUnreadMessagesForChat(chatId, currentUserId)

        // Mark them as read and update in DB
        unreadMessages.forEach { message ->
            messageDao.insertMessage(message.copy(status = "read"))
        }

        // Update the chat's unread count to 0
        chatDao.updateChatUnreadCount(chatId, 0)
    }
}
