package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import com.tfkcolin.meena.utils.TokenManager
import javax.inject.Inject

/**
 * Use case for removing the current user from a group.
 * This is equivalent to leaving a group.
 */
class LeaveGroupChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository,
    private val tokenManager: TokenManager
) {

    /**
     * Leave a group.
     *
     * @param groupId The group ID.
     * @return The result of the leave operation.
     */
    suspend operator fun invoke(
        groupId: String
    ): Result<Unit> {
        // Get the current user's handle
        val userHandle = tokenManager.getUserHandle() ?: return Result.failure(
            Exception("User not authenticated")
        )

        // Remove the current user from the group
        return chatRepository.removeGroupMember(
            groupId = groupId,
            userHandle = userHandle
        )
    }
}
