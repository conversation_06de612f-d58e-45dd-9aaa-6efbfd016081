package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for blocking a contact.
 */
class BlockContactUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {

    /**
     * Block a contact.
     *
     * @param contactId The contact ID.
     * @return The result of the block contact operation.
     */
    suspend operator fun invoke(
        contactId: String
    ): Result<Unit> {
        return contactRepository.blockContact(contactId)
    }
}
