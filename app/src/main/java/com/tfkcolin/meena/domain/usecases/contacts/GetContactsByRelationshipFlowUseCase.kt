package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import javax.inject.Inject

/**
 * Use case for getting contacts with a specific relationship as a flow from the local database.
 */
class GetContactsByRelationshipFlowUseCase @Inject constructor(
    private val contactDao: ContactDao,
    private val tokenManager: TokenManager
) {

    /**
     * Get contacts with a specific relationship as a flow.
     *
     * @param relationship The relationship type.
     * @return A flow of contacts with the specified relationship.
     */
    operator fun invoke(relationship: String): Flow<List<Contact>> {
        val currentUserId = tokenManager.getUserId()
        return if (currentUserId != null) {
            contactDao.getContactsByRelationshipFlow(currentUserId, relationship)
        } else {
            emptyFlow() // Return an empty flow if no user is logged in
        }
    }
}
