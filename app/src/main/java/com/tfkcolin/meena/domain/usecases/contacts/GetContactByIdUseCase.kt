package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for getting a contact by ID.
 */
class GetContactByIdUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Get a contact by ID.
     * 
     * @param contactId The contact ID.
     * @return The result of the get contact operation.
     */
    suspend operator fun invoke(contactId: String): Result<ContactResponse> {
        return contactRepository.getContactById(contactId)
    }
}
