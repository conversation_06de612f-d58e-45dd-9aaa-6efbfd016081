package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting media attachments for a message.
 */
class GetAttachmentsForMessageUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Get media attachments for a message.
     *
     * @param messageId The message ID.
     * @return The media attachments.
     */
    suspend operator fun invoke(messageId: String): List<MediaAttachment> {
        return chatRepository.getAttachmentsForMessage(messageId)
    }

    /**
     * Get media attachments for a message as a flow.
     *
     * @param messageId The message ID.
     * @return A flow of media attachments.
     */
    fun getFlow(messageId: String): Flow<List<MediaAttachment>> {
        return chatRepository.getAttachmentsForMessageFlow(messageId)
    }
}
