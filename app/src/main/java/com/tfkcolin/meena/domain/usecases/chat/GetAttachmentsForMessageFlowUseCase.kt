package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting media attachments for a message as a flow.
 */
class GetAttachmentsForMessageFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get media attachments for a message as a flow.
     * 
     * @param messageId The message ID.
     * @return A flow of the message's attachments.
     */
    operator fun invoke(messageId: String): Flow<List<MediaAttachment>> {
        return chatRepository.getAttachmentsForMessageFlow(messageId)
    }
}
