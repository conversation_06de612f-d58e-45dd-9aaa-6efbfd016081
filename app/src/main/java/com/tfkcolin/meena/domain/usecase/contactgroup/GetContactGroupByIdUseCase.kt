package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for getting a contact group by ID.
 *
 * @property contactGroupRepository The contact group repository.
 */
class GetContactGroupByIdUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param groupId The ID of the group to get.
     * @return The contact group.
     */
    suspend operator fun invoke(groupId: String): ContactGroup {
        return contactGroupRepository.getContactGroupById(groupId)
    }
}
