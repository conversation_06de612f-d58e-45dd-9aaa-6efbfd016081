package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for creating a new contact group.
 *
 * @property contactGroupRepository The contact group repository.
 */
class CreateContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param name The name of the group.
     * @param description The description of the group.
     * @param memberIds The IDs of the initial members.
     * @return The created contact group.
     */
    suspend operator fun invoke(
        name: String,
        description: String? = null,
        memberIds: List<String> = emptyList()
    ): ContactGroup {
        return contactGroupRepository.createContactGroup(name, description, memberIds)
    }
}
