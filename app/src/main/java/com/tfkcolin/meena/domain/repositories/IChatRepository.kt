package com.tfkcolin.meena.domain.repositories

import com.tfkcolin.meena.data.api.ChatListResponse
import com.tfkcolin.meena.data.api.ChatResponse
import com.tfkcolin.meena.data.api.GroupMemberResponse
import com.tfkcolin.meena.data.api.GroupMembersResponse
import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.data.api.MessageListResponse
import com.tfkcolin.meena.data.api.MessageResponse
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for chat operations.
 */
interface IChatRepository {

    /**
     * Get all chats for the current user.
     *
     * @param limit The maximum number of chats to return.
     * @param offset The offset for pagination.
     * @return The result of the get chats operation.
     */
    suspend fun getChats(limit: Int = 50, offset: Int = 0): Result<ChatListResponse>

    /**
     * Get all chats for the current user as a flow.
     *
     * @return A flow of the user's chats.
     */
    fun getChatsFlow(): Flow<List<Chat>>

    /**
     * Get a chat by ID.
     *
     * @param chatId The chat ID.
     * @return The result of the get chat operation.
     */
    suspend fun getChatById(chatId: String): Result<ChatResponse>

    /**
     * Create a new chat.
     *
     * @param participantIds The participant IDs.
     * @return The result of the create chat operation.
     */
    suspend fun createChat(participantIds: List<String>): Result<ChatResponse>

    /**
     * Delete a chat.
     *
     * @param chatId The chat ID.
     * @return The result of the delete chat operation.
     */
    suspend fun deleteChat(chatId: String): Result<Unit>

    /**
     * Get messages for a chat.
     *
     * @param chatId The chat ID.
     * @param limit The maximum number of messages to return.
     * @param offset The offset for pagination.
     * @return The result of the get messages operation.
     */
    suspend fun getMessages(chatId: String, limit: Int = 50, offset: Int = 0): Result<MessageListResponse>

    /**
     * Get messages for a chat as a flow.
     *
     * @param chatId The chat ID.
     * @return A flow of the chat's messages.
     */
    fun getMessagesFlow(chatId: String): Flow<List<Message>>

    /**
     * Send a message.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The result of the send message operation.
     */
    suspend fun sendMessage(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): Result<MessageResponse>

    /**
     * Update a message's status.
     *
     * @param messageId The message ID.
     * @param status The new status.
     */
    suspend fun updateMessageStatus(messageId: String, status: String)

    /**
     * Mark all messages in a chat as read.
     *
     * @param chatId The chat ID.
     * @param currentUserId The current user ID.
     */
    suspend fun markChatMessagesAsRead(chatId: String, currentUserId: String)

    /**
     * Get or create a chat with a user.
     *
     * @param userId The user ID.
     * @return The chat.
     */
    suspend fun getOrCreateChatWithUser(userId: String): Result<Chat>

    /**
     * Send a message with media attachments.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param attachments The media attachments.
     * @return The result of the send message operation.
     */
    suspend fun sendMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>
    ): Result<MessageResponse>

    /**
     * Get media attachments for a message.
     *
     * @param messageId The message ID.
     * @return The media attachments.
     */
    suspend fun getAttachmentsForMessage(messageId: String): List<MediaAttachment>

    /**
     * Get media attachments for a message as a flow.
     *
     * @param messageId The message ID.
     * @return A flow of the message's attachments.
     */
    fun getAttachmentsForMessageFlow(messageId: String): Flow<List<MediaAttachment>>

    /**
     * Search for messages across all chats.
     *
     * @param query The search query.
     * @return A list of messages matching the query.
     */
    suspend fun searchMessages(query: String): List<Message>

    /**
     * Search for messages in a specific chat.
     *
     * @param chatId The chat ID.
     * @param query The search query.
     * @return A list of messages matching the query in the specified chat.
     */
    suspend fun searchMessagesInChat(chatId: String, query: String): List<Message>

    /**
     * Edit a message.
     *
     * @param messageId The message ID.
     * @param newContent The new content.
     * @return The result of the edit operation.
     */
    suspend fun editMessage(messageId: String, newContent: String): Result<Message>

    /**
     * Delete a message for self.
     *
     * @param messageId The message ID.
     * @return The result of the delete operation.
     */
    suspend fun deleteMessageForSelf(messageId: String): Result<Unit>

    /**
     * Delete a message for everyone.
     *
     * @param messageId The message ID.
     * @return The result of the delete operation.
     */
    suspend fun deleteMessageForEveryone(messageId: String): Result<Unit>

    /**
     * Archive a chat.
     *
     * @param chatId The chat ID.
     * @param isArchived Whether the chat should be archived.
     * @return The result of the archive operation.
     */
    suspend fun archiveChat(chatId: String, isArchived: Boolean): Result<Unit>

    /**
     * Mute a chat.
     *
     * @param chatId The chat ID.
     * @param isMuted Whether the chat should be muted.
     * @return The result of the mute operation.
     */
    suspend fun muteChat(chatId: String, isMuted: Boolean): Result<Unit>

    /**
     * Update chat settings.
     *
     * @param chatId The chat ID.
     * @param isArchived Whether the chat should be archived.
     * @param isMuted Whether the chat should be muted.
     * @param isPinned Whether the chat should be pinned.
     * @return The result of the update operation.
     */
    suspend fun updateChatSettings(
        chatId: String,
        isArchived: Boolean? = null,
        isMuted: Boolean? = null,
        isPinned: Boolean? = null
    ): Result<Unit>

    /**
     * Get non-archived chats for the current user.
     *
     * @return A flow of non-archived chats.
     */
    fun getNonArchivedChatsFlow(): Flow<List<Chat>>

    /**
     * Get archived chats for the current user.
     *
     * @return A flow of archived chats.
     */
    fun getArchivedChatsFlow(): Flow<List<Chat>>

    /**
     * Get group chats for the current user.
     *
     * @return A flow of group chats.
     */
    fun getGroupChatsFlow(): Flow<List<Chat>>

    /**
     * Forward a message to another chat.
     *
     * @param messageId The message ID to forward.
     * @param chatId The chat ID to forward to.
     * @param additionalContent Additional content to add to the forwarded message.
     * @return The result of the forward operation.
     */
    suspend fun forwardMessage(
        messageId: String,
        chatId: String,
        additionalContent: String? = null
    ): Result<Message>

    /**
     * Add a reaction to a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji reaction.
     * @return The result of the operation.
     */
    suspend fun addReaction(
        messageId: String,
        emoji: String
    ): Result<Unit>

    /**
     * Remove a reaction from a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji reaction.
     * @return The result of the operation.
     */
    suspend fun removeReaction(
        messageId: String,
        emoji: String
    ): Result<Unit>

    /**
     * Reply to a message.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The result of the operation.
     */
    suspend fun replyToMessage(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): Result<MessageResponse>

    /**
     * Reply to a message with attachments.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param attachments The media attachments.
     * @return The result of the operation.
     */
    suspend fun replyToMessageWithAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ): Result<MessageResponse>

    /**
     * Create a group.
     *
     * @param name The group name.
     * @param description The group description.
     * @param privacyType The privacy type ("public", "private", or "secret").
     * @param initialMembers The initial member user handles.
     * @return The result of the create operation.
     */
    suspend fun createGroup(
        name: String,
        description: String? = null,
        privacyType: String,
        initialMembers: List<String>? = null
    ): Result<String> // Returns the group ID

    /**
     * Get a group by ID.
     *
     * @param groupId The group ID.
     * @return The result of the get operation.
     */
    suspend fun getGroup(
        groupId: String
    ): Result<GroupResponse>

    /**
     * Update a group.
     *
     * @param groupId The group ID.
     * @param name The new name.
     * @param description The new description.
     * @param pictureUrl The new picture URL.
     * @return The result of the update operation.
     */
    suspend fun updateGroup(
        groupId: String,
        name: String? = null,
        description: String? = null,
        pictureUrl: String? = null
    ): Result<GroupResponse>

    /**
     * Get the members of a group.
     *
     * @param groupId The group ID.
     * @param limit The maximum number of members to return.
     * @param offset The offset for pagination.
     * @return The result of the get operation.
     */
    suspend fun getGroupMembers(
        groupId: String,
        limit: Int = 50,
        offset: Int = 0
    ): Result<GroupMembersResponse>

    /**
     * Add members to a group.
     *
     * @param groupId The group ID.
     * @param userHandles The user handles to add.
     * @return The result of the add operation.
     */
    suspend fun addGroupMembers(
        groupId: String,
        userHandles: List<String>
    ): Result<GroupResponse>

    /**
     * Remove a member from a group.
     *
     * @param groupId The group ID.
     * @param userHandle The user handle to remove.
     * @return The result of the remove operation.
     */
    suspend fun removeGroupMember(
        groupId: String,
        userHandle: String
    ): Result<Unit>

    /**
     * Update a member's role in a group.
     *
     * @param groupId The group ID.
     * @param userHandle The user handle to update.
     * @param role The new role ("admin" or "member").
     * @return The result of the update operation.
     */
    suspend fun updateGroupMemberRole(
        groupId: String,
        userHandle: String,
        role: String
    ): Result<GroupMemberResponse>

    // Removed duplicate updateConversationSettings method
}
