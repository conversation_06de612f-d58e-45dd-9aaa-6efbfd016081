package com.tfkcolin.meena.domain.repositories

import com.tfkcolin.meena.data.api.StoriesListResponse
import com.tfkcolin.meena.data.api.StoryResponse
import com.tfkcolin.meena.data.api.StoryViewResponse
import com.tfkcolin.meena.data.api.StoryHighlightResponse
import com.tfkcolin.meena.data.api.StoryHighlightsListResponse
import com.tfkcolin.meena.data.models.Story
import com.tfkcolin.meena.data.models.StoryView
import com.tfkcolin.meena.data.models.StoryHighlight
import kotlinx.coroutines.flow.Flow
import android.net.Uri

/**
 * Repository interface for stories operations.
 */
interface IStoriesRepository {

    /**
     * Get all stories for the current user's feed.
     *
     * @param limit The maximum number of stories to return.
     * @param offset The offset for pagination.
     * @return The result of the get stories operation.
     */
    suspend fun getStories(limit: Int = 50, offset: Int = 0): Result<StoriesListResponse>

    /**
     * Get all stories as a flow for real-time updates.
     *
     * @return A flow of stories.
     */
    fun getStoriesFlow(): Flow<List<Story>>

    /**
     * Get stories by a specific user.
     *
     * @param userId The user ID.
     * @param limit The maximum number of stories to return.
     * @param offset The offset for pagination.
     * @return The result of the get user stories operation.
     */
    suspend fun getUserStories(userId: String, limit: Int = 50, offset: Int = 0): Result<StoriesListResponse>

    /**
     * Get a story by ID.
     *
     * @param storyId The story ID.
     * @return The result of the get story operation.
     */
    suspend fun getStoryById(storyId: String): Result<StoryResponse>

    /**
     * Create a new story.
     *
     * @param mediaUri The media URI.
     * @param mediaType The media type ("image" or "video").
     * @param caption The story caption.
     * @param privacyType The privacy type ("public", "contacts", "close_friends", "custom").
     * @param allowedViewerIds The list of allowed viewer IDs (for custom privacy).
     * @param backgroundColor The background color (for text stories).
     * @param textColor The text color (for text stories).
     * @return The result of the create story operation.
     */
    suspend fun createStory(
        mediaUri: Uri,
        mediaType: String,
        caption: String? = null,
        privacyType: String = "contacts",
        allowedViewerIds: List<String> = emptyList(),
        backgroundColor: String? = null,
        textColor: String? = null
    ): Result<StoryResponse>

    /**
     * Delete a story.
     *
     * @param storyId The story ID.
     * @return The result of the delete story operation.
     */
    suspend fun deleteStory(storyId: String): Result<Unit>

    /**
     * View a story (mark as viewed and track analytics).
     *
     * @param storyId The story ID.
     * @param viewDuration The view duration in milliseconds.
     * @return The result of the view story operation.
     */
    suspend fun viewStory(storyId: String, viewDuration: Long = 0): Result<StoryViewResponse>

    /**
     * Get story views for a specific story.
     *
     * @param storyId The story ID.
     * @param limit The maximum number of views to return.
     * @param offset The offset for pagination.
     * @return The result of the get story views operation.
     */
    suspend fun getStoryViews(storyId: String, limit: Int = 50, offset: Int = 0): Result<List<StoryView>>

    /**
     * React to a story.
     *
     * @param storyId The story ID.
     * @param reactionType The reaction type (emoji).
     * @return The result of the react to story operation.
     */
    suspend fun reactToStory(storyId: String, reactionType: String): Result<Unit>

    /**
     * Reply to a story.
     *
     * @param storyId The story ID.
     * @param content The reply content.
     * @return The result of the reply to story operation.
     */
    suspend fun replyToStory(storyId: String, content: String): Result<Unit>

    /**
     * Get story highlights for a user.
     *
     * @param userId The user ID.
     * @return The result of the get story highlights operation.
     */
    suspend fun getStoryHighlights(userId: String): Result<StoryHighlightsListResponse>

    /**
     * Create a story highlight.
     *
     * @param title The highlight title.
     * @param storyIds The list of story IDs to include.
     * @param coverUrl The cover image URL.
     * @return The result of the create highlight operation.
     */
    suspend fun createStoryHighlight(
        title: String,
        storyIds: List<String>,
        coverUrl: String? = null
    ): Result<StoryHighlightResponse>

    /**
     * Update a story highlight.
     *
     * @param highlightId The highlight ID.
     * @param title The new title.
     * @param storyIds The new list of story IDs.
     * @param coverUrl The new cover image URL.
     * @return The result of the update highlight operation.
     */
    suspend fun updateStoryHighlight(
        highlightId: String,
        title: String? = null,
        storyIds: List<String>? = null,
        coverUrl: String? = null
    ): Result<StoryHighlightResponse>

    /**
     * Delete a story highlight.
     *
     * @param highlightId The highlight ID.
     * @return The result of the delete highlight operation.
     */
    suspend fun deleteStoryHighlight(highlightId: String): Result<Unit>

    /**
     * Get stories that are about to expire (for cleanup).
     *
     * @return A list of story IDs that are about to expire.
     */
    suspend fun getExpiringStories(): Result<List<String>>

    /**
     * Clean up expired stories.
     *
     * @return The result of the cleanup operation.
     */
    suspend fun cleanupExpiredStories(): Result<Unit>

    /**
     * Update story privacy settings.
     *
     * @param storyId The story ID.
     * @param privacyType The new privacy type.
     * @param allowedViewerIds The new list of allowed viewer IDs.
     * @return The result of the update privacy operation.
     */
    suspend fun updateStoryPrivacy(
        storyId: String,
        privacyType: String,
        allowedViewerIds: List<String> = emptyList()
    ): Result<Unit>

    /**
     * Get story analytics for the current user.
     *
     * @param storyId The story ID (optional, for specific story analytics).
     * @return The result of the get analytics operation.
     */
    suspend fun getStoryAnalytics(storyId: String? = null): Result<Map<String, Any>>

    /**
     * Search stories by content or user.
     *
     * @param query The search query.
     * @param limit The maximum number of results to return.
     * @param offset The offset for pagination.
     * @return The result of the search operation.
     */
    suspend fun searchStories(query: String, limit: Int = 20, offset: Int = 0): Result<StoriesListResponse>
}
