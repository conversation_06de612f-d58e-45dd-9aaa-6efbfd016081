package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactListResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for getting favorite contacts.
 */
class GetFavoriteContactsUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Get favorite contacts.
     * 
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The result of the get favorite contacts operation.
     */
    suspend operator fun invoke(
        limit: Int = 50,
        offset: Int = 0
    ): Result<ContactListResponse> {
        return contactRepository.getFavoriteContacts(limit, offset)
    }
}
