package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.data.models.TwoFactorAuthRequest
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for completing two-factor authentication.
 */
class TwoFactorAuthUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Complete two-factor authentication.
     * 
     * @param userId The user ID.
     * @param code The two-factor authentication code.
     * @param method The two-factor authentication method.
     * @return The result of the two-factor authentication operation.
     */
    suspend operator fun invoke(
        userId: String,
        code: String,
        method: String = "totp"
    ): Result<AuthResponse> {
        val request = TwoFactorAuthRequest(
            userId = userId,
            code = code,
            method = method
        )
        
        return authRepository.twoFactorAuth(request)
    }
}
