package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for adding a contact to a group.
 *
 * @property contactGroupRepository The contact group repository.
 */
class AddContactToGroupUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param groupId The ID of the group to add the contact to.
     * @param contactId The ID of the contact to add.
     * @return The updated contact group.
     */
    suspend operator fun invoke(
        groupId: String,
        contactId: String
    ): ContactGroup {
        return contactGroupRepository.addContactToGroup(groupId, contactId)
    }
}
