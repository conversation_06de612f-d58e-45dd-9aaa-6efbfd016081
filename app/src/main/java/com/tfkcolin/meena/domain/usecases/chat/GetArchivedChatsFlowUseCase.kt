package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting a flow of archived chats.
 */
class GetArchivedChatsFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get a flow of archived chats.
     * 
     * @return A flow of archived chats.
     */
    operator fun invoke(): Flow<List<Chat>> {
        return chatRepository.getArchivedChatsFlow()
    }
}
