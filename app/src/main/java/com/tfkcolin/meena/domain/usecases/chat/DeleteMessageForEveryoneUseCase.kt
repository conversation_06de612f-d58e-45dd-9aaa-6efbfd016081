package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for deleting a message for everyone.
 */
class DeleteMessageForEveryoneUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Delete a message for everyone.
     * 
     * @param messageId The message ID.
     * @return The result of the delete operation.
     */
    suspend operator fun invoke(messageId: String): Result<Unit> {
        return chatRepository.deleteMessageForEveryone(messageId)
    }
}
