package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * Use case for getting a contact group by ID.
 */
class GetContactGroupByIdUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Get a contact group by ID.
     *
     * @param groupId The group ID.
     * @return The contact group, or null if not found.
     */
    suspend operator fun invoke(groupId: String): ContactGroup? {
        return contactGroupRepository.getContactGroupById(groupId)
    }

    /**
     * Get a contact group by ID as a flow.
     *
     * @param groupId The group ID.
     * @return A flow of the contact group.
     */
    fun getFlow(groupId: String): Flow<ContactGroup?> {
        // Since there's no getContactGroupByIdFlow method in the repository,
        // we can use the getAllFlow method and filter it
        return contactGroupRepository.getContactGroupsFlow()
            .map { groups -> groups.find { it.id == groupId } }
    }
}
