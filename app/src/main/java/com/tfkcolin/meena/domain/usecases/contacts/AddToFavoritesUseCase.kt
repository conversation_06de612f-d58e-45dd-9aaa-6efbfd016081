package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for adding a contact to favorites.
 */
class AddToFavoritesUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Add a contact to favorites.
     * 
     * @param contactId The contact ID.
     * @return The result of the add to favorites operation.
     */
    suspend operator fun invoke(contactId: String): Result<Unit> {
        return contactRepository.addToFavorites(contactId)
    }
}
