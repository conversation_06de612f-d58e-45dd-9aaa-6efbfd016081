package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import javax.inject.Inject

/**
 * Use case for getting the user's contact list as a flow from the local database.
 */
class GetContactsFlowUseCase @Inject constructor(
    private val contactDao: ContactDao,
    private val tokenManager: TokenManager
) {

    /**
     * Get the user's contact list as a flow.
     *
     * @return A flow of the user's contacts.
     */
    operator fun invoke(): Flow<List<Contact>> {
        val currentUserId = tokenManager.getUserId()
        return if (currentUserId != null) {
            contactDao.getContactsFlow(currentUserId)
        } else {
            emptyFlow() // Return an empty flow if no user is logged in
        }
    }
}
