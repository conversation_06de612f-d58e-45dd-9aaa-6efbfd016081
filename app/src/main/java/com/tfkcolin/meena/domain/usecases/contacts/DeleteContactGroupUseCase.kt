package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.repository.ContactGroupRepository
import javax.inject.Inject

/**
 * Use case for deleting a contact group.
 */
class DeleteContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Delete a contact group.
     *
     * @param groupId The group ID.
     */
    suspend operator fun invoke(groupId: String) {
        contactGroupRepository.deleteContactGroup(groupId)
    }
}
