package com.tfkcolin.meena.domain.repositories

import com.tfkcolin.meena.data.models.User
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for user operations.
 */
interface IUserRepository {

    /**
     * Get a user by ID.
     *
     * @param userId The user ID.
     * @return The result of the get user operation.
     */
    suspend fun getUserById(userId: String): Result<User>

    /**
     * Get a user by handle.
     *
     * @param userHandle The user handle.
     * @return The result of the get user operation.
     */
    suspend fun getUserByHandle(userHandle: String): Result<User>

    /**
     * Get the current user.
     *
     * @return A flow of the current user.
     */
    fun getCurrentUser(): Flow<User?>

    /**
     * Update the current user's profile.
     *
     * @param displayName The new display name.
     * @param bio The new bio.
     * @param avatarUrl The new avatar URL.
     * @return The result of the update profile operation.
     */
    suspend fun updateProfile(
        displayName: String?,
        bio: String?,
        avatarUrl: String? = null
    ): Result<User>
}
