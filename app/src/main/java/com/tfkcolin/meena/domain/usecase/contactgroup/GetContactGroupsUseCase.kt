package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroupListResponse
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for getting all contact groups.
 *
 * @property contactGroupRepository The contact group repository.
 */
class GetContactGroupsUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param limit The maximum number of groups to return.
     * @param offset The offset for pagination.
     * @return The contact group list response.
     */
    suspend operator fun invoke(
        limit: Int = 50,
        offset: Int = 0
    ): ContactGroupListResponse {
        return contactGroupRepository.getContactGroups(limit, offset)
    }
}
