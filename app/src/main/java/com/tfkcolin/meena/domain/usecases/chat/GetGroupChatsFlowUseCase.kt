package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting group chats flow.
 */
class GetGroupChatsFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get group chats flow.
     * 
     * @return A flow of group chats.
     */
    operator fun invoke(): Flow<List<Chat>> {
        return chatRepository.getGroupChatsFlow()
    }
}
