package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import javax.inject.Inject

/**
 * Use case for adding a contact to a group.
 */
class AddContactToGroupUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Add a contact to a group.
     *
     * @param groupId The group ID.
     * @param contactId The contact ID.
     * @return The updated contact group.
     */
    suspend operator fun invoke(groupId: String, contactId: String): ContactGroup {
        return contactGroupRepository.addContactToGroup(groupId, contactId)
    }
}
