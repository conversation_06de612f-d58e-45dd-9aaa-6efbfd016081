package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for searching messages across all chats.
 */
class SearchMessagesUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Search for messages across all chats.
     * 
     * @param query The search query.
     * @return A list of messages matching the query.
     */
    suspend operator fun invoke(query: String): List<Message> {
        return chatRepository.searchMessages(query)
    }
}
