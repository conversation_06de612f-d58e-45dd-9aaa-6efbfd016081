package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for creating a group.
 */
class CreateGroupChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Create a group.
     *
     * @param name The group name.
     * @param description The group description.
     * @param privacyType The privacy type ("public", "private", or "secret").
     * @param initialMembers The initial member user handles.
     * @return The result of the create operation.
     */
    suspend operator fun invoke(
        name: String,
        description: String? = null,
        privacyType: String = "private",
        initialMembers: List<String>? = null
    ): Result<String> {
        return chatRepository.createGroup(
            name = name,
            description = description,
            privacyType = privacyType,
            initialMembers = initialMembers
        )
    }
}
