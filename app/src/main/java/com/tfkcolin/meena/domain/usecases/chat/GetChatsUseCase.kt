package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.ChatListResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for getting all chats for the current user.
 */
class GetChatsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get all chats for the current user.
     * 
     * @param limit The maximum number of chats to return.
     * @param offset The offset for pagination.
     * @return The result of the get chats operation.
     */
    suspend operator fun invoke(
        limit: Int = 50,
        offset: Int = 0
    ): Result<ChatListResponse> {
        return chatRepository.getChats(limit, offset)
    }
}
