package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for updating a group.
 */
class UpdateGroupChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Update a group.
     *
     * @param groupId The group ID.
     * @param name The new name.
     * @param description The new description.
     * @param pictureUrl The new picture URL.
     * @return The result of the update operation.
     */
    suspend operator fun invoke(
        groupId: String,
        name: String? = null,
        description: String? = null,
        pictureUrl: String? = null
    ): Result<GroupResponse> {
        return chatRepository.updateGroup(
            groupId = groupId,
            name = name,
            description = description,
            pictureUrl = pictureUrl
        )
    }
}
