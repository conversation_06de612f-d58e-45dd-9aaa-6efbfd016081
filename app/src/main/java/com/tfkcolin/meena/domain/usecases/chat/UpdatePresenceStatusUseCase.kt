package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for updating user presence status.
 */
class UpdatePresenceStatusUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Update user's presence status.
     * 
     * @param status The presence status ("online", "offline", "away", "busy").
     * @return The result of the operation.
     */
    suspend operator fun invoke(
        status: String
    ): Result<Unit> {
        return chatRepository.updatePresenceStatus(status)
    }
}
