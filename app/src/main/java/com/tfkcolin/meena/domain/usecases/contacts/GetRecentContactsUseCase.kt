package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactListResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for getting recent contacts.
 */
class GetRecentContactsUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Get recent contacts.
     * 
     * @param limit The maximum number of contacts to return.
     * @return The result of the get recent contacts operation.
     */
    suspend operator fun invoke(limit: Int = 10): Result<ContactListResponse> {
        return contactRepository.getRecentContacts(limit)
    }
}
