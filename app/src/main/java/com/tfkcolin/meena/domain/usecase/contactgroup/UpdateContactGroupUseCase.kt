package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for updating a contact group.
 *
 * @property contactGroupRepository The contact group repository.
 */
class UpdateContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param groupId The ID of the group to update.
     * @param name The new name of the group.
     * @param description The new description of the group.
     * @return The updated contact group.
     */
    suspend operator fun invoke(
        groupId: String,
        name: String,
        description: String? = null
    ): ContactGroup {
        return contactGroupRepository.updateContactGroup(groupId, name, description)
    }
}
