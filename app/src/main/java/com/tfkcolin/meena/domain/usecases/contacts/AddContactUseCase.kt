package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for adding a contact to the user's contact list.
 */
class AddContactUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {

    /**
     * Add a contact to the user's contact list.
     *
     * @param userHandle The user handle of the contact to add.
     * @param displayName The custom display name for the contact (optional).
     * @param notes Optional notes about the contact.
     * @return The result of the add contact operation.
     */
    suspend operator fun invoke(
        userHandle: String,
        displayName: String? = null,
        notes: String? = null
    ): Result<ContactResponse> {
        return contactRepository.addContact(
            userHandle = userHandle,
            displayName = displayName,
            notes = notes
        )
    }
}
