package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for searching messages in a specific chat.
 */
class SearchMessagesInChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Search for messages in a specific chat.
     * 
     * @param chatId The chat ID.
     * @param query The search query.
     * @return A list of messages matching the query in the specified chat.
     */
    suspend operator fun invoke(chatId: String, query: String): List<Message> {
        return chatRepository.searchMessagesInChat(chatId, query)
    }
}
