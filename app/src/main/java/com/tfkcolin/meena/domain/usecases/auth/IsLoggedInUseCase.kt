package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for checking if a user is logged in.
 */
class IsLoggedInUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Check if the user is logged in.
     * 
     * @return True if the user is logged in, false otherwise.
     */
    operator fun invoke(): <PERSON><PERSON><PERSON> {
        return authRepository.isLoggedIn()
    }
}
