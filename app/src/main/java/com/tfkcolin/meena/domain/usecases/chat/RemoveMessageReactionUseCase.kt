package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.MessageReactionRequest
import com.tfkcolin.meena.data.repositories.MessageRepository
import javax.inject.Inject

/**
 * Use case for removing a reaction from a message.
 */
class RemoveMessageReactionUseCase @Inject constructor(
    private val messageRepository: MessageRepository
) {
    /**
     * Remove a reaction from a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji to remove.
     * @return The result of the operation.
     */
    suspend operator fun invoke(messageId: String, emoji: String): Result<Unit> {
        return messageRepository.removeReaction(
            MessageReactionRequest(
                messageId = messageId,
                emoji = emoji
            )
        )
    }
}
