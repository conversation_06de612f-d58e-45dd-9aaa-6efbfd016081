package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for deleting a contact group.
 *
 * @property contactGroupRepository The contact group repository.
 */
class DeleteContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param groupId The ID of the group to delete.
     */
    suspend operator fun invoke(groupId: String) {
        contactGroupRepository.deleteContactGroup(groupId)
    }
}
