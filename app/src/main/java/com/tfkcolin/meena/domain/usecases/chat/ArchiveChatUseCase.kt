package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for archiving or unarchiving a chat.
 */
class ArchiveChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Archive or unarchive a chat.
     *
     * @param chatId The chat ID.
     * @param isArchived Whether the chat should be archived.
     * @return The result of the archive operation.
     */
    suspend operator fun invoke(chatId: String, isArchived: <PERSON><PERSON><PERSON>): Result<Unit> {
        return chatRepository.archiveChat(chatId, isArchived)
    }
}
