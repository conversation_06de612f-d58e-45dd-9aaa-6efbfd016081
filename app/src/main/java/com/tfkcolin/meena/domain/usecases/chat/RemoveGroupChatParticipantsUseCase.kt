package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for removing a member from a group.
 */
class RemoveGroupChatParticipantsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Remove a member from a group.
     *
     * @param groupId The group ID.
     * @param userHandle The user handle to remove.
     * @return The result of the remove operation.
     */
    suspend operator fun invoke(
        groupId: String,
        userHandle: String
    ): Result<Unit> {
        return chatRepository.removeGroupMember(
            groupId = groupId,
            userHandle = userHandle
        )
    }
}
