package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting presence status as a flow.
 */
class GetPresenceStatusFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get presence status for users as a flow.
     * 
     * @param userIds The list of user IDs to monitor.
     * @return A flow of user presence statuses (userId to status).
     */
    operator fun invoke(userIds: List<String>): Flow<Map<String, String>> {
        return chatRepository.getPresenceStatusFlow(userIds)
    }
}
