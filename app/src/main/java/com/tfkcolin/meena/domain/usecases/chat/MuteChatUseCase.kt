package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for muting or unmuting a chat.
 */
class MuteChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Mute or unmute a chat.
     *
     * @param chatId The chat ID.
     * @param isMuted Whether the chat should be muted.
     * @return The result of the mute operation.
     */
    suspend operator fun invoke(chatId: String, isMuted: Boolean): Result<Unit> {
        return chatRepository.muteChat(chatId, isMuted)
    }
}
