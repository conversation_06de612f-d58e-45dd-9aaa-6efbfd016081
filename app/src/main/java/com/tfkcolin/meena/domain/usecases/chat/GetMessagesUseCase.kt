package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.MessageListResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for getting messages for a chat.
 */
class GetMessagesUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get messages for a chat.
     * 
     * @param chatId The chat ID.
     * @param limit The maximum number of messages to return.
     * @param offset The offset for pagination.
     * @return The result of the get messages operation.
     */
    suspend operator fun invoke(
        chatId: String,
        limit: Int = 50,
        offset: Int = 0
    ): Result<MessageListResponse> {
        return chatRepository.getMessages(chatId, limit, offset)
    }
}
