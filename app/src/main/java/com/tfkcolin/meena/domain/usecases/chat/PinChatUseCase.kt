package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for pinning or unpinning a chat.
 */
class PinChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Pin or unpin a chat.
     *
     * @param chatId The chat ID.
     * @param isPinned Whether the chat should be pinned.
     * @return The result of the pin operation.
     */
    suspend operator fun invoke(chatId: String, isPinned: Boolean): Result<Unit> {
        return chatRepository.updateChatSettings(
            chatId = chatId,
            isArchived = null,
            isMuted = null,
            isPinned = isPinned
        )
    }
}
