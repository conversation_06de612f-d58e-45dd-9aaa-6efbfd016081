package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.models.Message
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting messages for a chat as a flow from the local database.
 */
class GetMessagesFlowUseCase @Inject constructor(
    private val messageDao: MessageDao
) {

    /**
     * Get messages for a chat as a flow.
     *
     * @param chatId The chat ID.
     * @return A flow of the chat's messages.
     */
    operator fun invoke(chatId: String): Flow<List<Message>> {
        return messageDao.getMessagesForChatFlow(chatId)
    }
}
