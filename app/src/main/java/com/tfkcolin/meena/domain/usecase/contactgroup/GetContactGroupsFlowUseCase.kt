package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting all contact groups as a flow.
 *
 * @property contactGroupRepository The contact group repository.
 */
class GetContactGroupsFlowUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @return A flow of contact groups.
     */
    operator fun invoke(): Flow<List<ContactGroup>> {
        return contactGroupRepository.getContactGroupsFlow()
    }
}
