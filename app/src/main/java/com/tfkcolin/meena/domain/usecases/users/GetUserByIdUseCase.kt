package com.tfkcolin.meena.domain.usecases.users

import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.domain.repositories.IUserRepository
import javax.inject.Inject

/**
 * Use case for getting a user by ID.
 */
class GetUserByIdUseCase @Inject constructor(
    private val userRepository: IUserRepository
) {
    
    /**
     * Get a user by ID.
     * 
     * @param userId The user ID.
     * @return The result of the get user operation.
     */
    suspend operator fun invoke(userId: String): Result<User> {
        return userRepository.getUserById(userId)
    }
}
