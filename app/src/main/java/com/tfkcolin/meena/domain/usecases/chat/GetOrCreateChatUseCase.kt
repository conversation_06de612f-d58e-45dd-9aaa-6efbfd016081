package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for getting or creating a chat with a user.
 */
class GetOrCreateChatUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get or create a chat with a user.
     * 
     * @param userId The user ID.
     * @return The chat.
     */
    suspend operator fun invoke(userId: String): Result<Chat> {
        return chatRepository.getOrCreateChatWithUser(userId)
    }
}
