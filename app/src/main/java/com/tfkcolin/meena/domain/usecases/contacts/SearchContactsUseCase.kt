package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactListResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for searching contacts.
 */
class SearchContactsUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Search contacts.
     * 
     * @param query The search query.
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @return The result of the search operation.
     */
    suspend operator fun invoke(
        query: String,
        limit: Int = 50,
        offset: Int = 0
    ): Result<ContactListResponse> {
        return contactRepository.searchContacts(query, limit, offset)
    }
}
