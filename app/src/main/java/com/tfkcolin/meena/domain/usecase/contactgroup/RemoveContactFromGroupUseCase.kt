package com.tfkcolin.meena.domain.usecase.contactgroup

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.IContactGroupRepository
import javax.inject.Inject

/**
 * Use case for removing a contact from a group.
 *
 * @property contactGroupRepository The contact group repository.
 */
class RemoveContactFromGroupUseCase @Inject constructor(
    private val contactGroupRepository: IContactGroupRepository
) {

    /**
     * Execute the use case.
     *
     * @param groupId The ID of the group to remove the contact from.
     * @param contactId The ID of the contact to remove.
     * @return The updated contact group.
     */
    suspend operator fun invoke(
        groupId: String,
        contactId: String
    ): ContactGroup {
        return contactGroupRepository.removeContactFromGroup(groupId, contactId)
    }
}
