package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.MessageResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for sending a message.
 */
class SendMessageUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Send a message.
     * 
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The result of the send message operation.
     */
    suspend operator fun invoke(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): Result<MessageResponse> {
        return chatRepository.sendMessage(
            chatId = chatId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl
        )
    }
}
