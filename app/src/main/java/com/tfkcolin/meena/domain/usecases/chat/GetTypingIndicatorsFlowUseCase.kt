package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting typing indicators as a flow.
 */
class GetTypingIndicatorsFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get typing indicators for a chat as a flow.
     * 
     * @param chatId The chat ID.
     * @return A flow of typing users (userId to isTyping).
     */
    operator fun invoke(chatId: String): Flow<Map<String, Boolean>> {
        return chatRepository.getTypingIndicatorsFlow(chatId)
    }
}
