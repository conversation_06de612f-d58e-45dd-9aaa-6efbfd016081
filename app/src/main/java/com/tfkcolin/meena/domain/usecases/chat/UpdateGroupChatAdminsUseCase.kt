package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.GroupMemberResponse
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for updating a member's role in a group.
 */
class UpdateGroupChatAdminsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Update a member's role in a group.
     *
     * @param groupId The group ID.
     * @param userHandle The user handle to update.
     * @param role The new role ("admin" or "member").
     * @return The result of the update operation.
     */
    suspend operator fun invoke(
        groupId: String,
        userHandle: String,
        role: String
    ): Result<GroupMemberResponse> {
        return chatRepository.updateGroupMemberRole(
            groupId = groupId,
            userHandle = userHandle,
            role = role
        )
    }
}
