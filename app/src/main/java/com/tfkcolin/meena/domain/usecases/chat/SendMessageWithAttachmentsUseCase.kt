package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.api.MessageResponse
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for sending a message with media attachments.
 */
class SendMessageWithAttachmentsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Send a message with media attachments.
     * 
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param attachments The media attachments.
     * @return The result of the send message operation.
     */
    suspend operator fun invoke(
        chatId: String,
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>
    ): Result<MessageResponse> {
        return chatRepository.sendMessageWithAttachments(
            chatId = chatId,
            recipientId = recipientId,
            content = content,
            attachments = attachments
        )
    }
}
