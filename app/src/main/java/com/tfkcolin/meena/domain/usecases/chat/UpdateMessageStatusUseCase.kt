package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for updating a message's status.
 */
class UpdateMessageStatusUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Update a message's status.
     * 
     * @param messageId The message ID.
     * @param status The new status.
     */
    suspend operator fun invoke(messageId: String, status: String) {
        chatRepository.updateMessageStatus(messageId, status)
    }
}
