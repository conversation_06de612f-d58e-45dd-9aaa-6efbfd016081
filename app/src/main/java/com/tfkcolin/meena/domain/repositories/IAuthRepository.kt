package com.tfkcolin.meena.domain.repositories

import com.tfkcolin.meena.data.models.*
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for authentication operations.
 */
interface IAuthRepository {
    
    /**
     * Register a new user.
     * 
     * @param request The registration request.
     * @return The result of the registration operation.
     */
    suspend fun register(request: RegisterRequest): Result<AuthResponse>
    
    /**
     * Log in a user.
     * 
     * @param request The login request.
     * @return The result of the login operation.
     */
    suspend fun login(request: LoginRequest): Result<AuthResponse>
    
    /**
     * Complete two-factor authentication.
     * 
     * @param request The two-factor authentication request.
     * @return The result of the two-factor authentication operation.
     */
    suspend fun twoFactorAuth(request: TwoFactorAuthRequest): Result<AuthResponse>
    
    /**
     * Refresh the access token using a refresh token.
     * 
     * @return The result of the token refresh operation.
     */
    suspend fun refreshToken(): Result<AuthResponse>
    
    /**
     * Recover an account using the recovery phrase and PIN.
     * 
     * @param request The account recovery request.
     * @return The result of the account recovery operation.
     */
    suspend fun recoverAccount(request: AccountRecoveryRequest): Result<AuthResponse>
    
    /**
     * Change the user's password.
     * 
     * @param request The password change request.
     * @return The result of the password change operation.
     */
    suspend fun changePassword(request: PasswordChangeRequest): Result<Unit>
    
    /**
     * Get the current user.
     * 
     * @return A flow of the current user.
     */
    fun getCurrentUser(): Flow<User?>
    
    /**
     * Check if the user is logged in.
     * 
     * @return True if the user is logged in, false otherwise.
     */
    fun isLoggedIn(): Boolean
    
    /**
     * Log out the current user.
     */
    suspend fun logout()
}
