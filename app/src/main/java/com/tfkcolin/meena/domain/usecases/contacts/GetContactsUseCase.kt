package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactListResponse
import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for getting the user's contact list.
 */
class GetContactsUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {

    /**
     * Get the user's contact list.
     *
     * @param limit The maximum number of contacts to return.
     * @param offset The offset for pagination.
     * @param relationship Optional filter for relationship type.
     * @return The result of the get contacts operation.
     */
    suspend operator fun invoke(
        limit: Int = 50,
        offset: Int = 0,
        relationship: String? = null
    ): Result<ContactListResponse> {
        return contactRepository.getContacts(limit, offset, relationship)
    }
}
