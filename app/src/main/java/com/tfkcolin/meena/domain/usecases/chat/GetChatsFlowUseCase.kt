package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.repositories.IChatRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting all chats for the current user as a flow.
 */
class GetChatsFlowUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Get all chats for the current user as a flow.
     * 
     * @return A flow of the user's chats.
     */
    operator fun invoke(): Flow<List<Chat>> {
        return chatRepository.getChatsFlow()
    }
}
