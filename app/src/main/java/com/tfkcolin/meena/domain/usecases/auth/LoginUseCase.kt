package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.data.models.LoginRequest
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for logging in a user.
 */
class LoginUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Log in a user.
     * 
     * @param identifier The identifier (user handle, email, or phone).
     * @param password The password.
     * @return The result of the login operation.
     */
    suspend operator fun invoke(
        identifier: String,
        password: String
    ): Result<AuthResponse> {
        val request = LoginRequest(
            identifier = identifier,
            password = password
        )
        
        return authRepository.login(request)
    }
}
