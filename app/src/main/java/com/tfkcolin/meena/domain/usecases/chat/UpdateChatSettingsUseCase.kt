package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for updating chat settings.
 */
class UpdateChatSettingsUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {

    /**
     * Update chat settings.
     *
     * @param chatId The chat ID.
     * @param isArchived Whether the chat is archived.
     * @param isMuted Whether the chat is muted.
     * @param isPinned Whether the chat is pinned.
     * @return The result of the update operation.
     */
    suspend operator fun invoke(
        chatId: String,
        isArchived: Boolean? = null,
        isMuted: Boolean? = null,
        isPinned: Boolean? = null
    ): Result<Unit> {
        return chatRepository.updateChatSettings(
            chatId = chatId,
            isArchived = isArchived,
            isMuted = isMuted,
            isPinned = isPinned
        )
    }
}
