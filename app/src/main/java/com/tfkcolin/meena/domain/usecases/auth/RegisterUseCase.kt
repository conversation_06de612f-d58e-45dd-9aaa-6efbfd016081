package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.data.models.RegisterRequest
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for registering a new user.
 */
class RegisterUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {

    /**
     * Register a new user.
     *
     * @param userHandle The user handle (optional, server generates if missing).
     * @param password The password.
     * @param email The email (optional).
     * @param phoneNumber The phone number (optional).
     * @param recoveryPin The recovery PIN (optional).
     * @param recoveryPhrase The recovery phrase (optional, client generates/stores first).
     * @param displayName The display name (optional).
     * @return The result of the registration operation.
     */
    suspend operator fun invoke(
        userHandle: String? = null,
        password: String,
        email: String? = null,
        phoneNumber: String? = null,
        recoveryPin: String? = null,
        recoveryPhrase: String? = null,
        displayName: String? = null
    ): Result<AuthResponse> {
        val request = RegisterRequest(
            userHandle = userHandle ?: "user_${System.currentTimeMillis()}",
            password = password,
            email = email,
            phoneNumber = phoneNumber,
            recoveryPin = recoveryPin,
            recoveryPhrase = recoveryPhrase,
            displayName = displayName
        )

        return authRepository.register(request)
    }
}
