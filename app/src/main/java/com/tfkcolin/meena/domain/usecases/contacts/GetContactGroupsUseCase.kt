package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting all contact groups.
 */
class GetContactGroupsUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Get all contact groups.
     *
     * @return A flow of all contact groups.
     */
    operator fun invoke(): Flow<List<ContactGroup>> {
        return contactGroupRepository.getContactGroupsFlow()
    }

    /**
     * Get all contact groups as a one-time operation.
     *
     * @return A list of all contact groups.
     */
    suspend fun getAll(): List<ContactGroup> {
        return contactGroupRepository.getContactGroups().contactGroups
    }
}
