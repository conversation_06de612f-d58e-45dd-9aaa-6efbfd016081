package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for deleting a contact.
 */
class DeleteContactUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Delete a contact.
     * 
     * @param contactId The contact ID.
     * @return The result of the delete contact operation.
     */
    suspend operator fun invoke(contactId: String): Result<Unit> {
        return contactRepository.deleteContact(contactId)
    }
}
