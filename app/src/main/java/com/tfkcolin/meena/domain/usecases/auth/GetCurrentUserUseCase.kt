package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting the current user.
 */
class GetCurrentUserUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Get the current user.
     * 
     * @return A flow of the current user.
     */
    operator fun invoke(): Flow<User?> {
        return authRepository.getCurrentUser()
    }
}
