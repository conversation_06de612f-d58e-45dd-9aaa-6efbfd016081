package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.data.repositories.MessageRepository
import javax.inject.Inject

/**
 * Use case for replying to a message.
 */
class ReplyToMessageUseCase @Inject constructor(
    private val messageRepository: MessageRepository
) {
    /**
     * Reply to a message.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param contentType The content type.
     * @param mediaUrl The media URL.
     * @return The result of the operation.
     */
    suspend operator fun invoke(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): Result<Message> {
        return messageRepository.sendMessage(
            chatId = chatId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl,
            replyToMessageId = replyToMessageId
        )
    }

    /**
     * Reply to a message with attachments.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param attachments The list of attachments.
     * @return The result of the operation.
     */
    suspend fun withAttachments(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ): Result<Message> {
        return messageRepository.sendMessageWithAttachments(
            chatId = chatId,
            recipientId = recipientId,
            content = content,
            attachments = attachments,
            replyToMessageId = replyToMessageId
        )
    }
}
