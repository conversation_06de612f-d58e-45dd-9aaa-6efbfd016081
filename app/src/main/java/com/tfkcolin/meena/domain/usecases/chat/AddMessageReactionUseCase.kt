package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.MessageReactionRequest
import com.tfkcolin.meena.data.repositories.MessageRepository
import javax.inject.Inject

/**
 * Use case for adding a reaction to a message.
 */
class AddMessageReactionUseCase @Inject constructor(
    private val messageRepository: MessageRepository
) {
    /**
     * Add a reaction to a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji to add.
     * @return The result of the operation.
     */
    suspend operator fun invoke(messageId: String, emoji: String): Result<Unit> {
        return messageRepository.addReaction(
            MessageReactionRequest(
                messageId = messageId,
                emoji = emoji
            )
        )
    }
}
