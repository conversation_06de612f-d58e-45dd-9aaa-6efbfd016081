package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for removing a contact from favorites.
 */
class RemoveFromFavoritesUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Remove a contact from favorites.
     * 
     * @param contactId The contact ID.
     * @return The result of the remove from favorites operation.
     */
    suspend operator fun invoke(contactId: String): Result<Unit> {
        return contactRepository.removeFromFavorites(contactId)
    }
}
