package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.AccountRecoveryRequest
import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for recovering an account.
 */
class RecoverAccountUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Recover an account.
     * 
     * @param userHandle The user handle.
     * @param recoveryPhrase The recovery phrase.
     * @param recoveryPin The recovery PIN.
     * @param newPassword The new password.
     * @return The result of the account recovery operation.
     */
    suspend operator fun invoke(
        userHandle: String,
        recoveryPhrase: String,
        recoveryPin: String,
        newPassword: String
    ): Result<AuthResponse> {
        val request = AccountRecoveryRequest(
            userHandle = userHandle,
            recoveryPhrase = recoveryPhrase,
            recoveryPin = recoveryPin,
            newPassword = newPassword
        )
        
        return authRepository.recoverAccount(request)
    }
}
