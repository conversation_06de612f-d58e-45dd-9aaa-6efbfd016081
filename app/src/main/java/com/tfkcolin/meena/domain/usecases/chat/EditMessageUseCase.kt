package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for editing a message.
 */
class EditMessageUseCase @Inject constructor(
    private val chatRepository: IChatRepository
) {
    
    /**
     * Edit a message.
     * 
     * @param messageId The message ID.
     * @param newContent The new content.
     * @return The result of the edit operation.
     */
    suspend operator fun invoke(messageId: String, newContent: String): Result<Message> {
        return chatRepository.editMessage(messageId, newContent)
    }
}
