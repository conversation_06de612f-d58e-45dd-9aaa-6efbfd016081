package com.tfkcolin.meena.domain.usecases.auth

import com.tfkcolin.meena.data.models.PasswordChangeRequest
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import javax.inject.Inject

/**
 * Use case for changing a user's password.
 */
class ChangePasswordUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) {
    
    /**
     * Change a user's password.
     * 
     * @param currentPassword The current password.
     * @param newPassword The new password.
     * @return The result of the password change operation.
     */
    suspend operator fun invoke(
        currentPassword: String,
        newPassword: String
    ): Result<Unit> {
        val request = PasswordChangeRequest(
            currentPassword = currentPassword,
            newPassword = newPassword
        )
        
        return authRepository.changePassword(request)
    }
}
