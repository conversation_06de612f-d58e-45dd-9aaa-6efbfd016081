package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.domain.repositories.IContactRepository
import javax.inject.Inject

/**
 * Use case for unblocking a contact.
 */
class UnblockContactUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {

    /**
     * Unblock a contact.
     *
     * @param contactId The contact ID.
     * @return The result of the unblock contact operation.
     */
    suspend operator fun invoke(
        contactId: String
    ): Result<Unit> {
        return contactRepository.unblockContact(contactId)
    }
}
