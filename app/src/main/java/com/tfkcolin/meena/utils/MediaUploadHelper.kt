package com.tfkcolin.meena.utils

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import android.webkit.MimeTypeMap
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.models.MediaAttachment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream
import java.util.UUID

/**
 * Helper class for uploading media.
 */
class MediaUploadHelper(
    private val context: Context,
    private val mediaApi: MediaApi,
    private val tokenManager: TokenManager
) {
    /**
     * Upload a media file.
     *
     * @param uri The URI of the media file.
     * @return The uploaded media attachment.
     */
    suspend fun uploadMedia(uri: Uri): MediaAttachment? = withContext(Dispatchers.IO) {
        try {
            // Get file details
            val fileDetails = getFileDetails(uri)
            val file = createTempFile(uri)

            // Create multipart request
            val requestFile = file.asRequestBody(fileDetails.mimeType.toMediaTypeOrNull())
            val body = MultipartBody.Part.createFormData("file", file.name, requestFile)

            // Upload file
            val token = tokenManager.getAccessToken() ?: return@withContext null
            val response = mediaApi.uploadMedia("Bearer $token", body)

            if (response.isSuccessful && response.body() != null) {
                val mediaResponse = response.body()!!

                // Create media attachment
                return@withContext MediaAttachment(
                    id = mediaResponse.mediaId,
                    messageId = "", // Will be set when attached to a message
                    type = fileDetails.mediaType,
                    url = mediaResponse.url,
                    thumbnailUrl = mediaResponse.thumbnailUrl,
                    name = fileDetails.fileName,
                    size = fileDetails.fileSize,
                    duration = null,
                    width = fileDetails.width,
                    height = fileDetails.height,
                    latitude = null,
                    longitude = null
                )
            }

            return@withContext null
        } catch (e: Exception) {
            e.printStackTrace()
            return@withContext null
        }
    }

    /**
     * Get file details from a URI.
     *
     * @param uri The URI of the file.
     * @return The file details.
     */
    private fun getFileDetails(uri: Uri): FileDetails {
        val contentResolver = context.contentResolver
        val mimeType = contentResolver.getType(uri) ?: "application/octet-stream"
        val mediaType = when {
            mimeType.startsWith("image/") -> "image"
            mimeType.startsWith("video/") -> "video"
            mimeType.startsWith("audio/") -> "audio"
            else -> "document"
        }

        var fileName = ""
        var fileSize = 0L
        var width = 0
        var height = 0

        // Query for file name and size
        contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val nameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
                val sizeIndex = cursor.getColumnIndex(MediaStore.MediaColumns.SIZE)

                if (nameIndex != -1) {
                    fileName = cursor.getString(nameIndex)
                }

                if (sizeIndex != -1) {
                    fileSize = cursor.getLong(sizeIndex)
                }

                // Get dimensions for images and videos
                if (mediaType == "image" || mediaType == "video") {
                    val widthIndex = cursor.getColumnIndex(MediaStore.MediaColumns.WIDTH)
                    val heightIndex = cursor.getColumnIndex(MediaStore.MediaColumns.HEIGHT)

                    if (widthIndex != -1 && heightIndex != -1) {
                        width = cursor.getInt(widthIndex)
                        height = cursor.getInt(heightIndex)
                    }
                }
            }
        }

        // Generate a file name if not found
        if (fileName.isEmpty()) {
            val extension = MimeTypeMap.getSingleton()
                .getExtensionFromMimeType(mimeType) ?: ""
            fileName = "media_${UUID.randomUUID()}.$extension"
        }

        return FileDetails(
            fileName = fileName,
            fileSize = fileSize,
            mimeType = mimeType,
            mediaType = mediaType,
            width = width,
            height = height
        )
    }

    /**
     * Create a temporary file from a URI.
     *
     * @param uri The URI of the file.
     * @return The temporary file.
     */
    private fun createTempFile(uri: Uri): File {
        val contentResolver = context.contentResolver
        val inputStream = contentResolver.openInputStream(uri)
            ?: throw IllegalStateException("Could not open input stream for URI: $uri")

        val tempFile = File.createTempFile("upload_", ".tmp", context.cacheDir)
        FileOutputStream(tempFile).use { outputStream ->
            inputStream.copyTo(outputStream)
        }

        return tempFile
    }

    /**
     * File details.
     */
    data class FileDetails(
        val fileName: String,
        val fileSize: Long,
        val mimeType: String,
        val mediaType: String,
        val width: Int = 0,
        val height: Int = 0
    )
}
