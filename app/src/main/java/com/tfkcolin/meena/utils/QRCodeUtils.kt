package com.tfkcolin.meena.utils

import android.graphics.Bitmap
import android.graphics.Color
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import java.util.EnumMap

/**
 * Utility class for QR code generation and handling.
 */
object QRCodeUtils {

    /**
     * Generate a QR code bitmap from a string.
     *
     * @param content The content to encode in the QR code.
     * @param size The size of the QR code in pixels.
     * @param margin The margin around the QR code.
     * @return The generated QR code as a Bitmap.
     */
    fun generateQRCode(content: String, size: Int = 512, margin: Int = 1): Bitmap {
        val hints = EnumMap<EncodeHintType, Any>(EncodeHintType::class.java)
        hints[EncodeHintType.CHARACTER_SET] = "UTF-8"
        hints[EncodeHintType.ERROR_CORRECTION] = ErrorCorrectionLevel.H
        hints[EncodeHintType.MARGIN] = margin

        val writer = QRCodeWriter()
        val bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, size, size, hints)
        
        val width = bitMatrix.width
        val height = bitMatrix.height
        val pixels = IntArray(width * height)
        
        for (y in 0 until height) {
            val offset = y * width
            for (x in 0 until width) {
                pixels[offset + x] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
            }
        }
        
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        
        return bitmap
    }

    /**
     * Create a Meena ID QR code content string.
     * This formats the content in a way that our app can recognize when scanning.
     *
     * @param meenaId The Meena ID to encode.
     * @return The formatted content string.
     */
    fun createMeenaIdContent(meenaId: String): String {
        return "MEENA:ID:$meenaId"
    }

    /**
     * Parse a QR code content string to extract a Meena ID.
     *
     * @param content The QR code content string.
     * @return The extracted Meena ID, or null if the content is not a valid Meena ID QR code.
     */
    fun parseMeenaIdContent(content: String): String? {
        return if (content.startsWith("MEENA:ID:")) {
            content.substringAfter("MEENA:ID:")
        } else {
            null
        }
    }
}
