package com.tfkcolin.meena.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.tfkcolin.meena.R
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized error handling utility for the app.
 * Provides methods to handle errors consistently across the app.
 */
@Singleton
class ErrorHandler @Inject constructor(
    context: Context
) {
    // Use application context to prevent memory leaks
    private val appContext = context.applicationContext

    /**
     * Get the application context.
     *
     * @return The application context.
     */
    fun getApplicationContext(): Context = appContext

    // Global error state that can be observed by UI components
    private val _globalError = MutableStateFlow<ErrorEvent?>(null)
    val globalError: StateFlow<ErrorEvent?> = _globalError.asStateFlow()

    // Global AppError state that can be observed by UI components
    private val _globalAppError = MutableStateFlow<AppError?>(null)
    val globalAppError: StateFlow<AppError?> = _globalAppError.asStateFlow()

    /**
     * Handle an exception and return a user-friendly error message.
     *
     * @param throwable The exception to handle.
     * @param fallbackMessage Optional fallback message if the exception type is not recognized.
     * @return A user-friendly error message.
     */
    fun getErrorMessage(throwable: Throwable, fallbackMessage: String? = null): String {
        val appError = throwable.toAppError()
        return getErrorMessage(appError, fallbackMessage)
    }

    /**
     * Handle an AppError and return a user-friendly error message.
     *
     * @param error The AppError to handle.
     * @param fallbackMessage Optional fallback message if the error type is not recognized.
     * @return A user-friendly error message.
     */
    fun getErrorMessage(error: AppError, fallbackMessage: String? = null): String {
        return when (error) {
            is AppError.NetworkError -> {
                when (error.exception) {
                    is UnknownHostException, is ConnectException -> appContext.getString(R.string.error_no_internet)
                    is SocketTimeoutException -> appContext.getString(R.string.error_timeout)
                    else -> appContext.getString(R.string.error_network)
                }
            }
            is AppError.ApiError -> error.message
            is AppError.AuthError -> error.message
            is AppError.ValidationError -> "${error.field}: ${error.message}"
            is AppError.FileError -> appContext.getString(R.string.error_file)
            is AppError.DatabaseError -> appContext.getString(R.string.error_database)
            is AppError.WebSocketError -> appContext.getString(R.string.error_websocket)
            is AppError.UnknownError -> fallbackMessage ?: error.exception.message ?: appContext.getString(R.string.error_unknown)
        }
    }

    /**
     * Set a global error that can be observed by UI components.
     *
     * @param throwable The exception that caused the error.
     * @param action Optional action that can be taken to resolve the error.
     */
    fun setGlobalError(throwable: Throwable, action: (() -> Unit)? = null) {
        val appError = throwable.toAppError()
        setGlobalError(appError, action)
    }

    /**
     * Set a global error that can be observed by UI components.
     *
     * @param error The AppError that caused the error.
     * @param action Optional action that can be taken to resolve the error.
     */
    fun setGlobalError(error: AppError, action: (() -> Unit)? = null) {
        val message = getErrorMessage(error)
        _globalError.value = ErrorEvent(message, action)
        _globalAppError.value = error
    }

    /**
     * Set a global error with a custom message.
     *
     * @param message The error message.
     * @param action Optional action that can be taken to resolve the error.
     */
    fun setGlobalError(message: String, action: (() -> Unit)? = null) {
        _globalError.value = ErrorEvent(message, action)
        _globalAppError.value = null
    }

    /**
     * Clear the global error.
     */
    fun clearGlobalError() {
        _globalError.value = null
        _globalAppError.value = null
    }

    /**
     * Check if the device has an active internet connection.
     *
     * @return True if the device has an active internet connection, false otherwise.
     */
    fun isNetworkAvailable(): Boolean {
        val connectivityManager = appContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }

    /**
     * Map a Result to a user-friendly error message.
     *
     * @param result The Result to map.
     * @param fallbackMessage Optional fallback message if the exception type is not recognized.
     * @return A user-friendly error message if the Result is a failure, null otherwise.
     */
    fun <T> getErrorMessageFromResult(result: Result<T>, fallbackMessage: String? = null): String? {
        return if (result.isFailure) {
            result.exceptionOrNull()?.let { getErrorMessage(it, fallbackMessage) }
        } else {
            null
        }
    }

    /**
     * Map a Result to an AppError.
     *
     * @param result The Result to map.
     * @return An AppError if the Result is a failure, null otherwise.
     */
    fun <T> getAppErrorFromResult(result: Result<T>): AppError? {
        return if (result.isFailure) {
            result.exceptionOrNull()?.toAppError()
        } else {
            null
        }
    }
}

/**
 * Represents an error event with a message and an optional action.
 *
 * @param message The error message.
 * @param action Optional action that can be taken to resolve the error.
 */
data class ErrorEvent(
    val message: String,
    val action: (() -> Unit)? = null
)

/**
 * Exception thrown when an API error occurs.
 *
 * @param message The error message.
 * @param code The error code.
 */
class ApiException(message: String, val code: Int = 0) : Exception(message)

/**
 * Exception thrown when an authentication error occurs.
 *
 * @param message The error message.
 */
class AuthException(message: String) : Exception(message)

/**
 * Exception thrown when a validation error occurs.
 *
 * @param message The error message.
 * @param field The field that failed validation.
 */
class ValidationException(message: String, val field: String? = null) : Exception(message)
