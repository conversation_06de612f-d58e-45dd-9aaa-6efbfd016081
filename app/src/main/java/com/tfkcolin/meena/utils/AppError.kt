package com.tfkcolin.meena.utils

/**
 * Sealed class representing different types of errors in the app.
 * This provides a more type-safe way to handle errors compared to using exceptions.
 */
sealed class AppError {
    /**
     * Error related to network connectivity.
     *
     * @param exception The underlying exception.
     */
    data class NetworkError(val exception: Exception) : AppError()
    
    /**
     * Error returned by the API.
     *
     * @param code The error code.
     * @param message The error message.
     */
    data class ApiError(val code: Int, val message: String) : AppError()
    
    /**
     * Error related to authentication.
     *
     * @param message The error message.
     */
    data class AuthError(val message: String) : AppError()
    
    /**
     * Error related to validation.
     *
     * @param field The field that failed validation.
     * @param message The error message.
     */
    data class ValidationError(val field: String, val message: String) : AppError()
    
    /**
     * Error related to file operations.
     *
     * @param exception The underlying exception.
     */
    data class FileError(val exception: Exception) : AppError()
    
    /**
     * Error related to database operations.
     *
     * @param exception The underlying exception.
     */
    data class DatabaseError(val exception: Exception) : AppError()
    
    /**
     * Error related to WebSocket operations.
     *
     * @param exception The underlying exception.
     */
    data class WebSocketError(val exception: Exception) : AppError()
    
    /**
     * Unknown error.
     *
     * @param exception The underlying exception.
     */
    data class UnknownError(val exception: Throwable) : AppError()
}

/**
 * Extension function to convert an exception to an AppError.
 *
 * @return The corresponding AppError.
 */
fun Throwable.toAppError(): AppError {
    return when (this) {
        is java.net.UnknownHostException,
        is java.net.ConnectException,
        is java.net.SocketTimeoutException -> AppError.NetworkError(this)
        
        is ApiException -> AppError.ApiError(code, message ?: "Unknown API error")
        
        is AuthException -> AppError.AuthError(message ?: "Unknown authentication error")
        
        is ValidationException -> AppError.ValidationError(
            field ?: "unknown",
            message ?: "Unknown validation error"
        )
        
        is java.io.IOException -> AppError.FileError(this)
        
        is java.sql.SQLException -> AppError.DatabaseError(this)
        
        else -> AppError.UnknownError(this)
    }
}

/**
 * Extension function to get a user-friendly message from an AppError.
 *
 * @return A user-friendly error message.
 */
fun AppError.getUserFriendlyMessage(): String {
    return when (this) {
        is AppError.NetworkError -> "Network error: ${exception.message}"
        is AppError.ApiError -> "API error ($code): $message"
        is AppError.AuthError -> "Authentication error: $message"
        is AppError.ValidationError -> "Validation error for $field: $message"
        is AppError.FileError -> "File error: ${exception.message}"
        is AppError.DatabaseError -> "Database error: ${exception.message}"
        is AppError.WebSocketError -> "WebSocket error: ${exception.message}"
        is AppError.UnknownError -> "Unknown error: ${exception.message}"
    }
}
