package com.tfkcolin.meena.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import com.tfkcolin.meena.data.models.Message

/**
 * Utility functions for clipboard operations.
 */
object ClipboardUtils {
    
    /**
     * Copy message content to clipboard.
     * 
     * @param context The context.
     * @param message The message to copy.
     */
    fun copyMessageToClipboard(context: Context, message: Message) {
        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = ClipData.newPlainText("Message", message.content)
        clipboardManager.setPrimaryClip(clipData)
    }
}
