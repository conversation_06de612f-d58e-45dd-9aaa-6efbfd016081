package com.tfkcolin.meena.utils

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import android.webkit.MimeTypeMap
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for file operations.
 */
@Singleton
class FileUtils @Inject constructor(
    @ApplicationContext private val context: Context
) {

    /**
     * Get the file name from a URI.
     *
     * @param uri The URI.
     * @return The file name.
     */
    fun getFileName(uri: Uri): String {
        var fileName = "file_${System.currentTimeMillis()}"

        // Try to get the file name from the content resolver
        if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    if (nameIndex != -1) {
                        fileName = cursor.getString(nameIndex)
                    }
                }
            }
        }

        // If the file name couldn't be determined, use the last path segment
        if (fileName.isBlank()) {
            fileName = uri.lastPathSegment ?: "file_${System.currentTimeMillis()}"
        }

        return fileName
    }

    /**
     * Get the file size from a URI.
     *
     * @param uri The URI.
     * @return The file size in bytes, or null if it couldn't be determined.
     */
    fun getFileSize(uri: Uri): Long? {
        // Try to get the file size from the content resolver
        if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                    if (sizeIndex != -1) {
                        return cursor.getLong(sizeIndex)
                    }
                }
            }
        }

        // If the file size couldn't be determined, try to get it from the file
        if (uri.scheme == ContentResolver.SCHEME_FILE) {
            uri.path?.let { path ->
                val file = File(path)
                if (file.exists()) {
                    return file.length()
                }
            }
        }

        return null
    }

    /**
     * Get the MIME type from a URI.
     *
     * @param uri The URI.
     * @return The MIME type, or null if it couldn't be determined.
     */
    fun getMimeType(uri: Uri): String? {
        return if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
            context.contentResolver.getType(uri)
        } else {
            val fileExtension = MimeTypeMap.getFileExtensionFromUrl(uri.toString())
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(fileExtension.lowercase())
        }
    }

    /**
     * Copy a file from a URI to the app's cache directory.
     *
     * @param uri The URI.
     * @return The copied file, or null if the copy failed.
     */
    fun copyFileToCache(uri: Uri): File? {
        val fileName = getFileName(uri)
        val cacheDir = context.cacheDir
        val file = File(cacheDir, "${UUID.randomUUID()}_$fileName")

        try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                FileOutputStream(file).use { outputStream ->
                    copyStream(inputStream, outputStream)
                }
            }

            return file
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * Copy a stream from an input stream to an output stream.
     *
     * @param inputStream The input stream.
     * @param outputStream The output stream.
     */
    private fun copyStream(inputStream: InputStream, outputStream: FileOutputStream) {
        val buffer = ByteArray(4 * 1024) // 4 KB buffer
        var bytesRead: Int

        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
            outputStream.write(buffer, 0, bytesRead)
        }

        outputStream.flush()
    }

    /**
     * Get the app's cache directory.
     *
     * @return The cache directory.
     */
    fun getCacheDir(): File {
        return context.cacheDir
    }
}
