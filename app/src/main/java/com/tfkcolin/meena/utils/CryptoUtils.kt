package com.tfkcolin.meena.utils

import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.PrivateKey
import java.security.PublicKey
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for cryptographic operations.
 * Handles E2E encryption for media files and messages.
 */
@Singleton
class CryptoUtils @Inject constructor() {

    companion object {
        private const val KEYSTORE_PROVIDER = "AndroidKeyStore"
        private const val KEY_ALIAS = "MeenaE2EKey"
        private const val AES_KEY_SIZE = 256
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 128
        private const val BUFFER_SIZE = 8192
    }

    /**
     * Generate a new RSA key pair for E2E encryption.
     *
     * @return The generated key pair.
     */
    fun generateKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance(
            KeyProperties.KEY_ALGORITHM_RSA,
            KEYSTORE_PROVIDER
        )

        val parameterSpec = KeyGenParameterSpec.Builder(
            KEY_ALIAS,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        ).apply {
            setDigests(KeyProperties.DIGEST_SHA256, KeyProperties.DIGEST_SHA512)
            setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_RSA_OAEP)
            setKeySize(2048)
        }.build()

        keyPairGenerator.initialize(parameterSpec)
        return keyPairGenerator.generateKeyPair()
    }

    /**
     * Get the stored key pair from the Android Keystore.
     *
     * @return The stored key pair, or null if not found.
     */
    fun getKeyPair(): KeyPair? {
        val keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER)
        keyStore.load(null)

        if (!keyStore.containsAlias(KEY_ALIAS)) {
            return null
        }

        val privateKey = keyStore.getKey(KEY_ALIAS, null) as PrivateKey
        val publicKey = keyStore.getCertificate(KEY_ALIAS).publicKey

        return KeyPair(publicKey, privateKey)
    }

    /**
     * Generate a random AES key for file encryption.
     *
     * @return The generated AES key.
     */
    fun generateAESKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES)
        keyGenerator.init(AES_KEY_SIZE, SecureRandom())
        return keyGenerator.generateKey()
    }

    /**
     * Encrypt a file using AES-GCM.
     *
     * @param inputFile The file to encrypt.
     * @param outputFile The encrypted output file.
     * @param secretKey The AES key to use for encryption.
     * @return True if encryption was successful, false otherwise.
     */
    fun encryptFile(inputFile: File, outputFile: File, secretKey: SecretKey): Boolean {
        try {
            // Generate a random IV
            val iv = ByteArray(GCM_IV_LENGTH)
            SecureRandom().nextBytes(iv)

            // Initialize the cipher
            val cipher = Cipher.getInstance("AES/GCM/NoPadding")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, GCMParameterSpec(GCM_TAG_LENGTH, iv))

            // Write the IV to the output file
            FileOutputStream(outputFile).use { outputStream ->
                outputStream.write(iv)

                // Encrypt the file
                FileInputStream(inputFile).use { inputStream ->
                    val buffer = ByteArray(BUFFER_SIZE)
                    var bytesRead: Int
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        val encryptedBytes = cipher.update(buffer, 0, bytesRead)
                        if (encryptedBytes != null) {
                            outputStream.write(encryptedBytes)
                        }
                    }
                    val finalBytes = cipher.doFinal()
                    if (finalBytes != null) {
                        outputStream.write(finalBytes)
                    }
                }
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }

    /**
     * Decrypt a file using AES-GCM.
     *
     * @param inputFile The encrypted file.
     * @param outputFile The decrypted output file.
     * @param secretKey The AES key to use for decryption.
     * @return True if decryption was successful, false otherwise.
     */
    fun decryptFile(inputFile: File, outputFile: File, secretKey: SecretKey): Boolean {
        try {
            FileInputStream(inputFile).use { inputStream ->
                // Read the IV from the input file
                val iv = ByteArray(GCM_IV_LENGTH)
                inputStream.read(iv)

                // Initialize the cipher
                val cipher = Cipher.getInstance("AES/GCM/NoPadding")
                cipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(GCM_TAG_LENGTH, iv))

                // Decrypt the file
                FileOutputStream(outputFile).use { outputStream ->
                    val buffer = ByteArray(BUFFER_SIZE)
                    var bytesRead: Int
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        val decryptedBytes = cipher.update(buffer, 0, bytesRead)
                        if (decryptedBytes != null) {
                            outputStream.write(decryptedBytes)
                        }
                    }
                    val finalBytes = cipher.doFinal()
                    if (finalBytes != null) {
                        outputStream.write(finalBytes)
                    }
                }
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }

    /**
     * Encrypt an AES key with a recipient's public key.
     *
     * @param secretKey The AES key to encrypt.
     * @param publicKey The recipient's public key.
     * @return The encrypted AES key as a Base64 string.
     */
    fun encryptAESKey(secretKey: SecretKey, publicKey: PublicKey): String {
        val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, publicKey)
        val encryptedKeyBytes = cipher.doFinal(secretKey.encoded)
        return Base64.encodeToString(encryptedKeyBytes, Base64.NO_WRAP)
    }

    /**
     * Decrypt an AES key with the user's private key.
     *
     * @param encryptedKeyBase64 The encrypted AES key as a Base64 string.
     * @param privateKey The user's private key.
     * @return The decrypted AES key.
     */
    fun decryptAESKey(encryptedKeyBase64: String, privateKey: PrivateKey): SecretKey {
        val encryptedKeyBytes = Base64.decode(encryptedKeyBase64, Base64.NO_WRAP)
        val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding")
        cipher.init(Cipher.DECRYPT_MODE, privateKey)
        val keyBytes = cipher.doFinal(encryptedKeyBytes)
        return SecretKeySpec(keyBytes, "AES")
    }

    /**
     * Encrypt a message with a recipient's public key.
     *
     * @param message The message to encrypt.
     * @param publicKey The recipient's public key.
     * @return The encrypted message as a Base64 string.
     */
    fun encryptMessage(message: String, publicKey: PublicKey): String {
        // Generate a random AES key
        val secretKey = generateAESKey()

        // Encrypt the message with the AES key
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        val iv = ByteArray(GCM_IV_LENGTH)
        SecureRandom().nextBytes(iv)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, GCMParameterSpec(GCM_TAG_LENGTH, iv))
        val encryptedMessageBytes = cipher.doFinal(message.toByteArray())

        // Encrypt the AES key with the recipient's public key
        val encryptedKeyBytes = encryptAESKey(secretKey, publicKey)

        // Combine IV, encrypted key, and encrypted message
        val combined = ByteArray(iv.size + encryptedKeyBytes.length + encryptedMessageBytes.size)
        System.arraycopy(iv, 0, combined, 0, iv.size)
        System.arraycopy(encryptedKeyBytes.toByteArray(), 0, combined, iv.size, encryptedKeyBytes.length)
        System.arraycopy(encryptedMessageBytes, 0, combined, iv.size + encryptedKeyBytes.length, encryptedMessageBytes.size)

        return Base64.encodeToString(combined, Base64.NO_WRAP)
    }

    /**
     * Decrypt a message with the user's private key.
     *
     * @param encryptedMessageBase64 The encrypted message as a Base64 string.
     * @param privateKey The user's private key.
     * @return The decrypted message.
     */
    fun decryptMessage(encryptedMessageBase64: String, privateKey: PrivateKey): String {
        // Decode the combined data
        val combined = Base64.decode(encryptedMessageBase64, Base64.NO_WRAP)

        // Extract IV, encrypted key, and encrypted message
        val iv = ByteArray(GCM_IV_LENGTH)
        System.arraycopy(combined, 0, iv, 0, iv.size)

        // TODO: In a real implementation, we would need to know the length of the encrypted key
        // For this example, we'll assume a fixed length
        val encryptedKeyBytes = ByteArray(256)
        System.arraycopy(combined, iv.size, encryptedKeyBytes, 0, encryptedKeyBytes.size)

        val encryptedMessageBytes = ByteArray(combined.size - iv.size - encryptedKeyBytes.size)
        System.arraycopy(combined, iv.size + encryptedKeyBytes.size, encryptedMessageBytes, 0, encryptedMessageBytes.size)

        // Decrypt the AES key with the private key
        val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding")
        cipher.init(Cipher.DECRYPT_MODE, privateKey)
        val keyBytes = cipher.doFinal(encryptedKeyBytes)
        val secretKey = SecretKeySpec(keyBytes, "AES")

        // Decrypt the message with the AES key
        val aesCipher = Cipher.getInstance("AES/GCM/NoPadding")
        aesCipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(GCM_TAG_LENGTH, iv))
        val decryptedBytes = aesCipher.doFinal(encryptedMessageBytes)

        return String(decryptedBytes)
    }
}
