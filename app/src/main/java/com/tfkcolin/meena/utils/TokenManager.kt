package com.tfkcolin.meena.utils

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import androidx.core.content.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import java.nio.charset.Charset
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for securely storing and retrieving authentication tokens.
 */
@Singleton
class TokenManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    companion object {
        private const val PREFS_NAME = "meena_secure_prefs"
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_HANDLE = "user_handle"
        private const val KEY_REGISTRATION_COMPLETE = "registration_complete"
        private const val KEY_RECOVERY_PHRASE = "recovery_phrase"

        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS = "meena_secure_prefs_key_alias"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val IV_SEPARATOR = "]"
    }

    private val keyStore = KeyStore.getInstance(ANDROID_KEYSTORE).apply { load(null) }

    private val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private fun getOrCreateKey(): SecretKey {
        val existingKey = keyStore.getEntry(KEY_ALIAS, null) as? KeyStore.SecretKeyEntry
        return existingKey?.secretKey ?: generateKey()
    }

    private fun generateKey(): SecretKey {
        return try {
            val keyGenerator = KeyGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_AES,
                ANDROID_KEYSTORE
            )

            val keySpec = KeyGenParameterSpec.Builder(
                KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .setKeySize(256)
                .build()

            keyGenerator.init(keySpec)
            keyGenerator.generateKey()
        } catch (e: Exception) {
            Log.e("TokenManager", "Error generating key via AndroidKeystore", e)
            throw RuntimeException(e)
        }
    }

    private fun encrypt(data: String): String? {
        if (data.isEmpty()) return null
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, getOrCreateKey())
            val iv = cipher.iv
            val encryptedData = cipher.doFinal(data.toByteArray(Charset.defaultCharset()))
            // Prepend the IV to the encrypted data for decryption
            Base64.encodeToString(iv, Base64.DEFAULT) + IV_SEPARATOR + Base64.encodeToString(encryptedData, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e("TokenManager", "Encryption failed", e)
            null
        }
    }

    private fun decrypt(encryptedString: String): String? {
        if (encryptedString.isEmpty()) return null
        return try {
            val parts = encryptedString.split(IV_SEPARATOR)
            if (parts.size != 2) throw IllegalArgumentException("Invalid encrypted string format")

            val iv = Base64.decode(parts[0], Base64.DEFAULT)
            val encryptedData = Base64.decode(parts[1], Base64.DEFAULT)

            val cipher = Cipher.getInstance(TRANSFORMATION)
            val spec = GCMParameterSpec(128, iv)
            cipher.init(Cipher.DECRYPT_MODE, getOrCreateKey(), spec)
            String(cipher.doFinal(encryptedData), Charset.defaultCharset())
        } catch (e: Exception) {
            Log.e("TokenManager", "Decryption failed", e)
            null
        }
    }

    fun saveTokens(accessToken: String, refreshToken: String) {
        sharedPreferences.edit {
            putString(KEY_ACCESS_TOKEN, encrypt(accessToken))
            putString(KEY_REFRESH_TOKEN, encrypt(refreshToken))
        }
    }

    fun saveAccessToken(accessToken: String) {
        sharedPreferences.edit {
            putString(KEY_ACCESS_TOKEN, encrypt(accessToken))
        }
    }

    fun saveUserId(userId: String) {
        sharedPreferences.edit {
            putString(KEY_USER_ID, encrypt(userId))
        }
    }

    fun saveUserHandle(userHandle: String) {
        sharedPreferences.edit {
            putString(KEY_USER_HANDLE, encrypt(userHandle))
        }
    }

    fun getAccessToken(): String? {
        val encryptedToken = sharedPreferences.getString(KEY_ACCESS_TOKEN, null)
        return encryptedToken?.let { decrypt(it) }
    }

    fun getRefreshToken(): String? {
        val encryptedToken = sharedPreferences.getString(KEY_REFRESH_TOKEN, null)
        return encryptedToken?.let { decrypt(it) }
    }

    fun getUserId(): String? {
        val encryptedId = sharedPreferences.getString(KEY_USER_ID, null)
        return encryptedId?.let { decrypt(it) }
    }

    fun getUserHandle(): String? {
        val encryptedHandle = sharedPreferences.getString(KEY_USER_HANDLE, null)
        return encryptedHandle?.let { decrypt(it) }
    }

    fun clearTokens() {
        sharedPreferences.edit {
            clear()
        }
    }

    fun setRegistrationComplete(isComplete: Boolean) {
        sharedPreferences.edit {
            // No need to encrypt a boolean
            putBoolean(KEY_REGISTRATION_COMPLETE, isComplete)
        }
    }

    fun saveRecoveryPhrase(recoveryPhrase: String) {
        sharedPreferences.edit {
            putString(KEY_RECOVERY_PHRASE, encrypt(recoveryPhrase))
        }
    }

    fun isLoggedIn(): Boolean {
        return !getAccessToken().isNullOrEmpty() &&
                !getUserId().isNullOrEmpty() &&
                isRegistrationComplete()
    }

    fun markRegistrationComplete() {
        setRegistrationComplete(true)
    }

    fun isRegistrationComplete(): Boolean {
        return sharedPreferences.getBoolean(KEY_REGISTRATION_COMPLETE, false)
    }

    fun getRecoveryPhrase(): String? {
        val encryptedPhrase = sharedPreferences.getString(KEY_RECOVERY_PHRASE, null)
        return encryptedPhrase?.let { decrypt(it) }
    }
}