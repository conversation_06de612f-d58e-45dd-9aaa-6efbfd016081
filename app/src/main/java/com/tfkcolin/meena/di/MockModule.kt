package com.tfkcolin.meena.di

import android.content.Context
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.api.MockAuthApi
import com.tfkcolin.meena.mock.api.MockChatApi
import com.tfkcolin.meena.mock.api.MockContactApi
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.utils.TokenManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing mock backend dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object MockModule {
    
    /**
     * Provide MockDataStorage for persistent mock data.
     */
    @Provides
    @Singleton
    fun provideMockDataStorage(
        @ApplicationContext context: Context,
        userDao: UserDao,
        contactDao: ContactDao,
        chatDao: ChatDao,
        messageDao: MessageDao,
        tokenManager: TokenManager
    ): MockDataStorage {
        return MockDataStorage(context, userDao, contactDao, chatDao, messageDao, tokenManager)
    }
    
    /**
     * Provide AI response generator for chat simulation.
     */
    @Provides
    @Singleton
    fun provideAIResponseGenerator(): AIResponseGenerator {
        return AIResponseGenerator()
    }
    
    /**
     * Provide MockAuthApi.
     */
    @Provides
    @Singleton
    fun provideMockAuthApi(
        mockDataStorage: MockDataStorage,
        tokenManager: TokenManager
    ): MockAuthApi {
        return MockAuthApi(mockDataStorage, tokenManager)
    }
    
    /**
     * Provide MockContactApi.
     */
    @Provides
    @Singleton
    fun provideMockContactApi(
        mockDataStorage: MockDataStorage,
        mockAuthApi: MockAuthApi
    ): MockContactApi {
        return MockContactApi(mockDataStorage, mockAuthApi)
    }
    
    /**
     * Provide MockChatApi.
     */
    @Provides
    @Singleton
    fun provideMockChatApi(
        mockDataStorage: MockDataStorage,
        mockAuthApi: MockAuthApi,
        aiResponseGenerator: AIResponseGenerator
    ): MockChatApi {
        return MockChatApi(mockDataStorage, mockAuthApi, aiResponseGenerator)
    }
}
