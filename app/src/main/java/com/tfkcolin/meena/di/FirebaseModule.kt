package com.tfkcolin.meena.di

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.local.MediaAttachmentDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.repositories.firebase.FirebaseAuthRepository
import com.tfkcolin.meena.data.repositories.firebase.FirebaseChatRepository
import com.tfkcolin.meena.data.repositories.firebase.FirebaseContactRepository
import com.tfkcolin.meena.data.repositories.firebase.FirebaseUserRepository
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.domain.repositories.IChatRepository
import com.tfkcolin.meena.domain.repositories.IContactRepository
import com.tfkcolin.meena.domain.repositories.IUserRepository
import com.tfkcolin.meena.utils.TokenManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing Firebase dependencies.
 * This module provides Firebase-based repository implementations.
 */
@Module
@InstallIn(SingletonComponent::class)
object FirebaseModule {

    /**
     * Provide Firebase Auth instance.
     */
    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth {
        return FirebaseAuth.getInstance()
    }

    /**
     * Provide Firebase Firestore instance.
     */
    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore {
        return FirebaseFirestore.getInstance()
    }

    /**
     * Provide Firebase Auth Repository implementation.
     */
    @Provides
    @Singleton
    fun provideFirebaseAuthRepository(
        firebaseAuth: FirebaseAuth,
        firestore: FirebaseFirestore,
        userDao: UserDao,
        tokenManager: TokenManager
    ): IAuthRepository {
        return FirebaseAuthRepository(firebaseAuth, firestore, userDao, tokenManager)
    }

    /**
     * Provide Firebase User Repository implementation.
     */
    @Provides
    @Singleton
    fun provideFirebaseUserRepository(
        firebaseAuth: FirebaseAuth,
        firestore: FirebaseFirestore,
        userDao: UserDao,
        tokenManager: TokenManager
    ): IUserRepository {
        return FirebaseUserRepository(firebaseAuth, firestore, userDao, tokenManager)
    }

    /**
     * Provide Firebase Contact Repository implementation.
     */
    @Provides
    @Singleton
    fun provideFirebaseContactRepository(
        firestore: FirebaseFirestore,
        contactDao: ContactDao,
        tokenManager: TokenManager
    ): IContactRepository {
        return FirebaseContactRepository(firestore, contactDao, tokenManager)
    }

    /**
     * Provide Firebase Chat Repository implementation.
     */
    @Provides
    @Singleton
    fun provideFirebaseChatRepository(
        firestore: FirebaseFirestore,
        chatDao: ChatDao,
        messageDao: MessageDao,
        mediaAttachmentDao: MediaAttachmentDao,
        tokenManager: TokenManager
    ): IChatRepository {
        return FirebaseChatRepository(firestore, chatDao, messageDao, mediaAttachmentDao, tokenManager)
    }
}
