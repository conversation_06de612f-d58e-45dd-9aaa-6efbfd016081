package com.tfkcolin.meena.di

import android.content.Context
import com.tfkcolin.meena.utils.FileUtils
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing utility-related dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object UtilsModule {

    /**
     * Provide the FileUtils.
     */
    @Provides
    @Singleton
    fun provideFileUtils(@ApplicationContext context: Context): FileUtils {
        return FileUtils(context)
    }

    /**
     * Provide the MediaAttachmentHelper.
     */
    @Provides
    @Singleton
    fun provideMediaAttachmentHelper(
        @ApplicationContext context: Context,
        fileUtils: FileUtils
    ): MediaAttachmentHelper {
        return MediaAttachmentHelper(context, fileUtils)
    }
}
