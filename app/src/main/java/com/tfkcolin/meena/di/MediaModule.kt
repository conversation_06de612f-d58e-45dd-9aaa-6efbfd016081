package com.tfkcolin.meena.di

import com.tfkcolin.meena.data.services.MediaService
import com.tfkcolin.meena.data.services.MediaServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger module for media-related dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class MediaModule {
    
    /**
     * Provide the MediaService implementation.
     */
    @Binds
    @Singleton
    abstract fun bindMediaService(mediaServiceImpl: MediaServiceImpl): MediaService
}
