package com.tfkcolin.meena.di

import com.tfkcolin.meena.data.services.MediaService

import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.domain.repositories.IUserRepository
import com.tfkcolin.meena.ui.profile.ProfileViewModel
import com.tfkcolin.meena.ui.viewmodels.ContactViewModel
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.domain.usecases.chat.AddGroupChatParticipantsUseCase
import com.tfkcolin.meena.domain.usecases.chat.AddMessageReactionUseCase
import com.tfkcolin.meena.domain.usecases.chat.ArchiveChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.CreateGroupChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.DeleteMessageForEveryoneUseCase
import com.tfkcolin.meena.domain.usecases.chat.DeleteMessageForSelfUseCase
import com.tfkcolin.meena.domain.usecases.chat.EditMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.ForwardMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetArchivedChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetAttachmentsForMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetChatsUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetGroupChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetMessagesFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetMessagesUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetNonArchivedChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetOrCreateChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.LeaveGroupChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.MarkChatMessagesAsReadUseCase
import com.tfkcolin.meena.domain.usecases.chat.MuteChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.PinChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.RemoveGroupChatParticipantsUseCase
import com.tfkcolin.meena.domain.usecases.chat.RemoveMessageReactionUseCase
import com.tfkcolin.meena.domain.usecases.chat.ReplyToMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.SearchMessagesInChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.SearchMessagesUseCase
import com.tfkcolin.meena.domain.usecases.chat.SendMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.SendMessageWithAttachmentsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateChatSettingsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateGroupChatAdminsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateGroupChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateMessageStatusUseCase
import com.tfkcolin.meena.domain.usecases.contacts.AddContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.BlockContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactsUseCase
import com.tfkcolin.meena.domain.usecases.contacts.UnblockContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.UpdateContactUseCase
import com.tfkcolin.meena.ui.chat.ChatViewModel
import com.tfkcolin.meena.ui.chat.ConversationListViewModel
import com.tfkcolin.meena.ui.chat.GroupChatViewModel
import com.tfkcolin.meena.ui.chat.MessageViewModel
import android.content.Context
import android.os.UserManager
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.FileUtils
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ViewModelScoped

/**
 * Module for providing specialized ViewModels to the ChatViewModelFacade.
 */
@Module
@InstallIn(ViewModelComponent::class)
object ViewModelModule {

    /**
     * Provide the ConversationListViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideConversationListViewModel(
        getChatsUseCase: GetChatsUseCase,
        getChatsFlowUseCase: GetChatsFlowUseCase,
        getNonArchivedChatsFlowUseCase: GetNonArchivedChatsFlowUseCase,
        getArchivedChatsFlowUseCase: GetArchivedChatsFlowUseCase,
        getGroupChatsFlowUseCase: GetGroupChatsFlowUseCase,
        getOrCreateChatUseCase: GetOrCreateChatUseCase,
        archiveChatUseCase: ArchiveChatUseCase,
        muteChatUseCase: MuteChatUseCase,
        pinChatUseCase: PinChatUseCase,
        tokenManager: TokenManager,
        errorHandler: ErrorHandler
    ): ConversationListViewModel {
        return ConversationListViewModel(
            getChatsUseCase,
            getChatsFlowUseCase,
            getNonArchivedChatsFlowUseCase,
            getArchivedChatsFlowUseCase,
            getGroupChatsFlowUseCase,
            getOrCreateChatUseCase,
            archiveChatUseCase,
            muteChatUseCase,
            pinChatUseCase,
            tokenManager,
            errorHandler
        )
    }

    /**
     * Provide the MessageViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideMessageViewModel(
        mockDataStorage: MockDataStorage,
        tokenManager: TokenManager,
        aiResponseGenerator: AIResponseGenerator,
        getMessagesFlowUseCase: GetMessagesFlowUseCase,
        markChatMessagesAsReadUseCase: MarkChatMessagesAsReadUseCase,
        mediaAttachmentHelper: MediaAttachmentHelper,
        errorHandler: ErrorHandler
    ): MessageViewModel {
        return MessageViewModel(
            mockDataStorage,
            tokenManager,
            aiResponseGenerator,
            getMessagesFlowUseCase,
            markChatMessagesAsReadUseCase,
            mediaAttachmentHelper,
            errorHandler
        )
    }

    /**
     * Provide the GroupChatViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideGroupChatViewModel(
        createGroupChatUseCase: CreateGroupChatUseCase,
        updateGroupChatUseCase: UpdateGroupChatUseCase,
        addGroupChatParticipantsUseCase: AddGroupChatParticipantsUseCase,
        removeGroupChatParticipantsUseCase: RemoveGroupChatParticipantsUseCase,
        updateGroupChatAdminsUseCase: UpdateGroupChatAdminsUseCase,
        leaveGroupChatUseCase: LeaveGroupChatUseCase,
        errorHandler: ErrorHandler
    ): GroupChatViewModel {
        return GroupChatViewModel(
            createGroupChatUseCase,
            updateGroupChatUseCase,
            addGroupChatParticipantsUseCase,
            removeGroupChatParticipantsUseCase,
            updateGroupChatAdminsUseCase,
            leaveGroupChatUseCase,
            errorHandler
        )
    }

    /**
     * Provide the MediaViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideMediaViewModel(
        mediaService: MediaService,
        fileUtils: FileUtils,
        mediaAttachmentHelper: MediaAttachmentHelper,
        errorHandler: ErrorHandler
    ): MediaViewModel {
        return MediaViewModel(
            mediaService,
            fileUtils,
            mediaAttachmentHelper,
            errorHandler
        )
    }

    /**
     * Provide the ContactViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideContactViewModel(
        getContactsUseCase: GetContactsUseCase,
        addContactUseCase: AddContactUseCase,
        updateContactUseCase: UpdateContactUseCase,
        blockContactUseCase: BlockContactUseCase,
        unblockContactUseCase: UnblockContactUseCase,
        errorHandler: ErrorHandler
    ): ContactViewModel {
        return ContactViewModel(
            getContactsUseCase,
            addContactUseCase,
            updateContactUseCase,
            blockContactUseCase,
            unblockContactUseCase,
            errorHandler
        )
    }

    /**
     * Provide the ProfileViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideProfileViewModel(
        userRepository: IUserRepository,
        authRepository: IAuthRepository,
        tokenManager: TokenManager,
        errorHandler: ErrorHandler
    ): ProfileViewModel {
        return ProfileViewModel(
            userRepository,
            authRepository,
            tokenManager,
            errorHandler
        )
    }

    /**
     * Provide the ChatViewModel.
     */
    @Provides
    @ViewModelScoped
    fun provideChatViewModel(
        conversationListViewModel: ConversationListViewModel,
        messageViewModel: MessageViewModel,
        groupChatViewModel: GroupChatViewModel,
        tokenManager: TokenManager,
        userManager: UserManager, // Use our custom UserManager
        mediaAttachmentHelper: MediaAttachmentHelper
    ): ChatViewModel {
        return ChatViewModel(
            conversationListViewModel,
            messageViewModel,
            groupChatViewModel,
            tokenManager,
            userManager,
            mediaAttachmentHelper
        )
    }

    /**
     * Provides the UserManager.
     */
    @Provides
    @ViewModelScoped
    fun provideUserManager(@dagger.hilt.android.qualifiers.ApplicationContext context: Context): UserManager {
        return context.getSystemService(Context.USER_SERVICE) as UserManager
    }
}
