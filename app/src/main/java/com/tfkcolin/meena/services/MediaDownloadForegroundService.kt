package com.tfkcolin.meena.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import android.app.Service
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import com.tfkcolin.meena.MainActivity
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.preferences.UserPreferences
import com.tfkcolin.meena.utils.CryptoUtils
import com.tfkcolin.meena.utils.FileUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.buffer
import okio.sink
import java.io.File
import java.io.IOException
import javax.inject.Inject

/**
 * Foreground service for downloading media files.
 * This service continues to run even when the app is in the background.
 */
@AndroidEntryPoint
class MediaDownloadForegroundService : Service() {

    // Create a coroutine scope for the service
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    companion object {
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "media_download_channel"
        private const val CHANNEL_NAME = "Media Download"

        const val ACTION_DOWNLOAD = "com.tfkcolin.meena.action.DOWNLOAD_MEDIA"
        const val ACTION_CANCEL = "com.tfkcolin.meena.action.CANCEL_DOWNLOAD"

        const val EXTRA_MEDIA_ID = "media_id"
        const val EXTRA_URL = "url"
        const val EXTRA_FILE_NAME = "file_name"
        const val EXTRA_IS_ENCRYPTED = "is_encrypted"
        const val EXTRA_ENCRYPTED_KEY = "encrypted_key"
    }

    @Inject
    lateinit var mediaApi: MediaApi

    @Inject
    lateinit var userPreferences: UserPreferences

    @Inject
    lateinit var fileUtils: FileUtils

    @Inject
    lateinit var cryptoUtils: CryptoUtils

    @Inject
    lateinit var okHttpClient: OkHttpClient

    private val _downloadProgress = MutableStateFlow<DownloadProgress>(DownloadProgress.Idle)
    val downloadProgress = _downloadProgress.asStateFlow()

    private var isDownloading = false

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_DOWNLOAD -> {
                val mediaId = intent.getStringExtra(EXTRA_MEDIA_ID)
                val url = intent.getStringExtra(EXTRA_URL)
                val fileName = intent.getStringExtra(EXTRA_FILE_NAME)
                val isEncrypted = intent.getBooleanExtra(EXTRA_IS_ENCRYPTED, false)
                val encryptedKey = intent.getStringExtra(EXTRA_ENCRYPTED_KEY)

                if (mediaId != null && url != null && fileName != null) {
                    startForeground(NOTIFICATION_ID, createNotification(0))
                    downloadFile(mediaId, url, fileName, isEncrypted, encryptedKey)
                }
            }
            ACTION_CANCEL -> {
                isDownloading = false
                _downloadProgress.value = DownloadProgress.Cancelled
                stopSelf()
            }
        }

        return START_NOT_STICKY
    }

    private fun downloadFile(
        mediaId: String,
        url: String,
        fileName: String,
        isEncrypted: Boolean,
        encryptedKey: String?
    ) {
        if (isDownloading) return

        isDownloading = true
        _downloadProgress.value = DownloadProgress.Preparing

        serviceScope.launch {
            try {
                // Create directory if it doesn't exist
                val mediaDir = File(getExternalFilesDir(null), "media")
                if (!mediaDir.exists()) {
                    mediaDir.mkdirs()
                }

                // Create temporary file for download
                val tempFile = File(mediaDir, "$fileName.temp")
                val targetFile = File(mediaDir, fileName)

                // Check if file already exists
                if (targetFile.exists()) {
                    _downloadProgress.value = DownloadProgress.Success(targetFile.absolutePath)
                    updateNotification(100)
                    isDownloading = false
                    stopSelf()
                    return@launch
                }

                // Download the file with progress tracking
                val success = downloadWithProgress(url, tempFile)
                if (!success) {
                    _downloadProgress.value = DownloadProgress.Error("Download failed")
                    updateNotification(0, error = true)
                    isDownloading = false
                    stopSelf()
                    return@launch
                }

                // If the file is encrypted, decrypt it
                if (isEncrypted && encryptedKey != null) {
                    _downloadProgress.value = DownloadProgress.Processing
                    updateNotification(100, processing = true)

                    val decryptedFile = decryptFile(tempFile, encryptedKey)
                    if (decryptedFile != null) {
                        // Move decrypted file to target location
                        if (decryptedFile.renameTo(targetFile)) {
                            _downloadProgress.value = DownloadProgress.Success(targetFile.absolutePath)
                            updateNotification(100)
                        } else {
                            _downloadProgress.value = DownloadProgress.Error("Failed to save decrypted file")
                            updateNotification(0, error = true)
                        }
                    } else {
                        _downloadProgress.value = DownloadProgress.Error("Decryption failed")
                        updateNotification(0, error = true)
                    }
                } else {
                    // Move downloaded file to target location
                    if (tempFile.renameTo(targetFile)) {
                        _downloadProgress.value = DownloadProgress.Success(targetFile.absolutePath)
                        updateNotification(100)
                    } else {
                        _downloadProgress.value = DownloadProgress.Error("Failed to save file")
                        updateNotification(0, error = true)
                    }
                }
            } catch (e: Exception) {
                _downloadProgress.value = DownloadProgress.Error(e.message ?: "Unknown error")
                updateNotification(0, error = true)
            } finally {
                // Wait a moment before stopping the service
                withContext(Dispatchers.IO) {
                    Thread.sleep(2000)
                }
                isDownloading = false
                stopSelf()
            }
        }
    }

    private suspend fun downloadWithProgress(url: String, file: File): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val request = Request.Builder().url(url).build()
                okHttpClient.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        return@withContext false
                    }

                    val responseBody = response.body ?: return@withContext false
                    val contentLength = responseBody.contentLength()
                    var bytesRead = 0L

                    responseBody.source().use { source ->
                        file.sink().buffer().use { sink ->
                            val buffer = ByteArray(8192)
                            var read: Long
                            while (source.read(buffer).also { read = it.toLong() } != -1 && isDownloading) {
                                sink.write(buffer, 0, read.toInt())
                                bytesRead += read
                                val progress = if (contentLength > 0) {
                                    (bytesRead * 100 / contentLength).toInt()
                                } else {
                                    -1 // Indeterminate
                                }
                                _downloadProgress.value = DownloadProgress.Downloading(progress)
                                updateNotification(progress)
                            }
                        }
                    }

                    isDownloading
                }
            } catch (e: IOException) {
                false
            }
        }
    }

    private suspend fun decryptFile(encryptedFile: File, encryptedKey: String): File? {
        return withContext(Dispatchers.IO) {
            try {
                // In a real implementation, this would:
                // 1. Decrypt the encrypted key using the user's private key
                // 2. Use the decrypted key to decrypt the file
                // This is a placeholder that simulates decryption

                val decryptedFile = File(encryptedFile.parentFile, "decrypted_${encryptedFile.name}")
                encryptedFile.copyTo(decryptedFile, overwrite = true)

                // Simulate decryption time
                Thread.sleep(1000)

                encryptedFile.delete()
                decryptedFile
            } catch (e: Exception) {
                null
            }
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Used for media download notifications"
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(
        progress: Int,
        error: Boolean = false,
        processing: Boolean = false
    ): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        val cancelIntent = PendingIntent.getService(
            this,
            0,
            Intent(this, MediaDownloadForegroundService::class.java).apply {
                action = ACTION_CANCEL
            },
            PendingIntent.FLAG_IMMUTABLE
        )

        val title = when {
            error -> "Download Failed"
            processing -> "Processing Media"
            else -> "Downloading Media"
        }

        val text = when {
            error -> "Tap to retry"
            processing -> "Decrypting file..."
            else -> if (progress >= 0) "Progress: $progress%" else "Downloading..."
        }

        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(text)
            .setSmallIcon(R.drawable.baseline_download_24)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(!error)

        if (!error && !processing) {
            builder.addAction(R.drawable.baseline_cancel_24, "Cancel", cancelIntent)
            if (progress >= 0) {
                builder.setProgress(100, progress, false)
            } else {
                builder.setProgress(0, 0, true) // Indeterminate
            }
        } else if (processing) {
            builder.setProgress(0, 0, true) // Indeterminate
        }

        return builder.build()
    }

    private fun updateNotification(
        progress: Int,
        error: Boolean = false,
        processing: Boolean = false
    ) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification(progress, error, processing))
    }

    override fun onDestroy() {
        super.onDestroy()
        isDownloading = false
        // Cancel all coroutines when the service is destroyed
        serviceScope.cancel()
    }
}

/**
 * Sealed class representing the different states of a download.
 */
sealed class DownloadProgress {
    object Idle : DownloadProgress()
    object Preparing : DownloadProgress()
    data class Downloading(val progress: Int) : DownloadProgress()
    object Processing : DownloadProgress()
    data class Success(val filePath: String) : DownloadProgress()
    data class Error(val message: String) : DownloadProgress()
    object Cancelled : DownloadProgress()
}
