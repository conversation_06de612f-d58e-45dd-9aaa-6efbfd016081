package com.tfkcolin.meena.ui.stories

import android.net.Uri
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.stories.components.StoriesBar
import com.tfkcolin.meena.ui.stories.models.OverlayItem
import com.tfkcolin.meena.ui.stories.models.OverlayType
import com.tfkcolin.meena.ui.stories.models.SegmentType
import com.tfkcolin.meena.ui.stories.models.Story
import com.tfkcolin.meena.ui.stories.models.StoryItem
import com.tfkcolin.meena.ui.stories.models.StorySegment
import com.tfkcolin.meena.ui.stories.viewmodel.StoryState
import java.util.Date
import java.util.UUID
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R

/**
 * Main screen for the Stories tab.
 *
 * @param onNavigateToStoryCreation The callback to navigate to the story creation screen.
 * @param onNavigateToStoryViewer The callback to navigate to the story viewer screen.
 * @param storyState The state of stories.
 * @param onLoadStories Callback to load stories.
 */
@Composable
fun StoriesScreen(
    onNavigateToStoryCreation: () -> Unit,
    onNavigateToStoryViewer: (List<Story>, Int) -> Unit,
    storyState: StoryState,
    onLoadStories: () -> Unit,
    onShowSnackbar: (String) -> Unit // Added parameter
) {
    // Load stories when the screen is first displayed
    LaunchedEffect(Unit) {
        onLoadStories()
    }

    // UI components are now managed centrally by NavGraph

    // Show error message using snackbar
    LaunchedEffect(storyState.error) {
        storyState.error?.let {
            onShowSnackbar(it)
            // Potentially clear the error in the ViewModel after showing it
        }
    }

    // Removed Scaffold
    Box(
        modifier = Modifier
            .fillMaxSize()
        // .padding(padding) // padding is no longer available from Scaffold
    ) {
        if (storyState.isLoading) {
            // Show loading indicator
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        } else if (storyState.error != null) {
            // Show error message
            Text(
                text = storyState.error ?: stringResource(R.string.an_error_occurred),
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(16.dp)
            )
        } else {
            // Show stories content
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // Your stories section
                Text(
                    text = stringResource(R.string.your_stories),
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(start = 16.dp, top = 16.dp, bottom = 8.dp)
                )

                StoriesBar(
                    stories = storyState.userStories,
                    onCreateStoryClick = onNavigateToStoryCreation,
                    onStoryClick = { storyItem ->
                        // Convert StoryItem to Story for the viewer
                        val stories = listOf(
                            createStoryFromItem(storyItem)
                        )
                        onNavigateToStoryViewer(stories, 0)
                    },
                    userAvatarUrl = "https://i.pravatar.cc/150?img=10" // Replace with actual user avatar
                )

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 8.dp),
                    thickness = 1.dp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f)
                )

                // Friends' stories section
                Text(
                    text = stringResource(R.string.friends_stories),
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(start = 16.dp, top = 8.dp, bottom = 8.dp)
                )

                StoriesBar(
                    stories = storyState.stories,
                    onCreateStoryClick = { /* Not applicable for friends' stories */ },
                    onStoryClick = { storyItem ->
                        // Find the index of the clicked story
                        val index = storyState.stories.indexOf(storyItem)

                        // Convert all StoryItems to Stories for the viewer
                        val stories = storyState.stories.map { createStoryFromItem(it) }

                        onNavigateToStoryViewer(stories, index)
                    },
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * Create a Story object from a StoryItem for the viewer.
 */
private fun createStoryFromItem(storyItem: StoryItem): Story {
    return Story(
        id = storyItem.id,
        authorName = storyItem.userName,
        authorAvatarUrl = storyItem.userAvatarUrl,
        authorId = UUID.randomUUID().toString(), // This would come from the API
        timePosted = Date(), // This would come from the API
        segments = listOf(
            StorySegment(
                id = UUID.randomUUID().toString(),
                type = SegmentType.IMAGE,
                mediaUrl = storyItem.storyImageUrl,
                duration = 5,
                overlayItems = if (storyItem.hasMultipleStories) {
                    listOf(
                        OverlayItem(
                            id = UUID.randomUUID().toString(),
                            type = OverlayType.TEXT,
                            content = "Multiple stories",
                            position = Offset(0.5f, 0.9f)
                        )
                    )
                } else {
                    emptyList()
                }
            )
        )
    )
}
