package com.tfkcolin.meena.ui.navigation

import androidx.compose.material3.FabPosition

/**
 * Screen routes for navigation.
 */
sealed class Screen(val route: String) {
    // Auth screens
    object AnonymousSignIn : Screen("anonymous_sign_in") // This is now the WelcomeScreen
    object Login : Screen("login")
    object Register : Screen("register")
    object RecoveryPhrase : Screen("recovery_phrase")
    object RecoveryPin : Screen("recovery_pin")
    object RemoteWipePin : Screen("remote_wipe_pin")
    object AccountRecovery : Screen("account_recovery")
    object TwoFactorAuth : Screen("two_factor_auth")

    // New auth flow screens
    object WelcomeScreen : Screen("welcome_screen")
    object RegistrationScreen : Screen("registration_screen")
    object UniqueIdScreen : Screen("unique_id_screen")
    object SecretPhraseScreen : Screen("secret_phrase_screen")
    object SetupPinScreen : Screen("setup_pin_screen")
    // Main screens
    object Home : Screen("home")
    object Contacts : Screen("contacts")
    object AddContact : Screen("add_contact")
    object ContactDetail : Screen("contact_detail/{contactId}") {
        fun createRoute(contactId: String) = "contact_detail/$contactId"
    }
    object EditContact : Screen("edit_contact/{contactId}") {
        fun createRoute(contactId: String) = "edit_contact/$contactId"
    }
    object ContactGroups : Screen("contact_groups")
    object ContactGroupDetail : Screen("contact_group_detail/{groupId}") {
        fun createRoute(groupId: String) = "contact_group_detail/$groupId"
    }
    object Settings : Screen("settings")
    object SecuritySettings : Screen("security_settings")
    object EditProfile : Screen("edit_profile")
    object AppearanceSettings : Screen("appearance_settings")
    object NotificationsSettings : Screen("notifications_settings")
    object LanguageSettings : Screen("language_settings")

    // Chat screens
    object Chat : Screen("chat/{chatId}/{recipientId}") {
        fun createRoute(chatId: String, recipientId: String) = "chat/$chatId/$recipientId"
    }
    object NewChat : Screen("new_chat")
    object Search : Screen("search")
    object ChatSearch : Screen("chat_search/{chatId}/{recipientName}") {
        fun createRoute(chatId: String, recipientName: String) = "chat_search/$chatId/$recipientName"
    }
    object ForwardMessage : Screen("forward_message/{messageId}") {
        fun createRoute(messageId: String) = "forward_message/$messageId"
    }

    // Group screens
    object CreateGroup : Screen("create_group")
    object GroupDetails : Screen("group_details/{groupId}") {
        fun createRoute(groupId: String) = "group_details/$groupId"
    }

    // Media screens
    object MediaPreview : Screen("media_preview/{messageId}/{initialIndex?}") {
        fun createRoute(messageId: String, initialIndex: Int = 0) = "media_preview/$messageId/$initialIndex"
    }

    // Bottom navigation screens
    object Chats : Screen("chats")
    object Stories : Screen("stories")
    object Calls : Screen("calls")
    object Profile : Screen("profile")

    // Stories screens
    object StoryCreation : Screen("story_creation")
    object StoryViewer : Screen("story_viewer")
}

/**
 * Navigation UI configuration for different screen types.
 */
data class NavigationUiConfig(
    val showTopBar: Boolean = true,
    val showBottomBar: Boolean = false,
    val showFab: Boolean = false,
    val fabPosition: FabPosition = FabPosition.End,
    val topBarTitle: String = "",
    val showBackButton: Boolean = false,
    val showSearchButton: Boolean = false,
    val topBarActions: List<String> = emptyList()
)

/**
 * Utility object to determine UI configuration based on current destination.
 */
object NavigationUiHelper {

    /**
     * Get UI configuration for a given route.
     */
    fun getUiConfig(route: String?): NavigationUiConfig {
        return when (route) {
            // Main bottom navigation screens
            Screen.Chats.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = true,
                showFab = true,
                topBarTitle = "Chats",
                showSearchButton = true,
                topBarActions = listOf("archive", "refresh")
            )
            Screen.Contacts.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = true,
                showFab = true,
                topBarTitle = "Contacts"
            )
            Screen.Stories.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = true,
                showFab = false,
                topBarTitle = "Stories"
            )
            Screen.Calls.route -> NavigationUiConfig(
                showTopBar = false,
                showBottomBar = true,
                showFab = true
            )
            Screen.Profile.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = true,
                showFab = false,
                topBarTitle = "Profile"
            )

            // Chat screens
            Screen.Chat.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                showBackButton = true,
                showSearchButton = true
            )
            Screen.NewChat.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "New Chat",
                showBackButton = true
            )
            Screen.Search.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Search",
                showBackButton = true
            )
            Screen.ChatSearch.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                showBackButton = true
            )
            Screen.ForwardMessage.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Forward Message",
                showBackButton = true
            )

            // Contact screens
            Screen.AddContact.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Add Contact",
                showBackButton = true
            )
            Screen.ContactDetail.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                showBackButton = true
            )
            Screen.EditContact.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Edit Contact",
                showBackButton = true
            )
            Screen.ContactGroups.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = true,
                topBarTitle = "Contact Groups",
                showBackButton = true
            )
            Screen.ContactGroupDetail.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = true,
                showBackButton = true
            )

            // Group screens
            Screen.CreateGroup.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Create Group",
                showBackButton = true
            )
            Screen.GroupDetails.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                showBackButton = true
            )

            // Story screens
            Screen.StoryCreation.route -> NavigationUiConfig(
                showTopBar = false,
                showBottomBar = false,
                showFab = false
            )
            Screen.StoryViewer.route -> NavigationUiConfig(
                showTopBar = false,
                showBottomBar = false,
                showFab = false
            )

            // Media screens
            Screen.MediaPreview.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                showBackButton = true
            )

            // Settings screens
            Screen.EditProfile.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Edit Profile",
                showBackButton = true
            )
            Screen.SecuritySettings.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Security Settings",
                showBackButton = true
            )
            Screen.AppearanceSettings.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Appearance",
                showBackButton = true
            )
            Screen.NotificationsSettings.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Notifications",
                showBackButton = true
            )
            Screen.LanguageSettings.route -> NavigationUiConfig(
                showTopBar = true,
                showBottomBar = false,
                showFab = false,
                topBarTitle = "Language",
                showBackButton = true
            )

            // Default configuration
            else -> NavigationUiConfig()
        }
    }

    /**
     * Check if a route matches a pattern (for parameterized routes).
     */
    fun routeMatches(currentRoute: String?, targetRoute: String): Boolean {
        if (currentRoute == null) return false

        // Handle parameterized routes
        val targetPattern = targetRoute.replace(Regex("\\{[^}]+\\}"), "[^/]+")
        val regex = Regex("^$targetPattern$")
        return regex.matches(currentRoute)
    }

    /**
     * Get UI configuration with route pattern matching.
     */
    fun getUiConfigForCurrentRoute(currentRoute: String?): NavigationUiConfig {
        if (currentRoute == null) return NavigationUiConfig()

        return when {
            routeMatches(currentRoute, Screen.Chat.route) -> getUiConfig(Screen.Chat.route)
            routeMatches(currentRoute, Screen.ContactDetail.route) -> getUiConfig(Screen.ContactDetail.route)
            routeMatches(currentRoute, Screen.EditContact.route) -> getUiConfig(Screen.EditContact.route)
            routeMatches(currentRoute, Screen.ContactGroupDetail.route) -> getUiConfig(Screen.ContactGroupDetail.route)
            routeMatches(currentRoute, Screen.ChatSearch.route) -> getUiConfig(Screen.ChatSearch.route)
            routeMatches(currentRoute, Screen.ForwardMessage.route) -> getUiConfig(Screen.ForwardMessage.route)
            routeMatches(currentRoute, Screen.GroupDetails.route) -> getUiConfig(Screen.GroupDetails.route)
            routeMatches(currentRoute, Screen.MediaPreview.route) -> getUiConfig(Screen.MediaPreview.route)
            else -> getUiConfig(currentRoute)
        }
    }
}
