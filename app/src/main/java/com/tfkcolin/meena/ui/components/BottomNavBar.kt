package com.tfkcolin.meena.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Chat
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.tfkcolin.meena.ui.navigation.Screen
import kotlin.collections.contains

/**
 * Bottom navigation bar.
 *
 * @param navController The navigation controller.
 */
@Composable
fun BottomNavBar(navController: NavController) {
    val items = listOf(
        BottomNavItem(
            title = "Chats",
            icon = Icons.AutoMirrored.Filled.Chat,
            route = Screen.Chats.route
        ),
        BottomNavItem(
            title = "Contacts",
            icon = Icons.Default.Person,
            route = Screen.Contacts.route
        ),
        BottomNavItem(
            title = "Stories",
            icon = Icons.Default.PhotoLibrary,
            route = Screen.Stories.route
        ),
        BottomNavItem(
            title = "Calls",
            icon = Icons.Default.Call,
            route = Screen.Calls.route
        ),
        BottomNavItem(
            title = "Profile",
            icon = Icons.Default.Person,
            route = Screen.Profile.route
        )
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // Only show bottom navigation on main screens
    val mainScreens = listOf(
        Screen.Chats.route,
        Screen.Contacts.route,
        Screen.Stories.route,
        Screen.Calls.route,
        Screen.Profile.route
    )

    if (currentDestination?.route in mainScreens) {
        NavigationBar {
            items.forEach { item ->
                val selected = currentDestination?.hierarchy?.any { it.route == item.route } == true

                NavigationBarItem(
                    icon = { Icon(item.icon, contentDescription = item.title) },
                    label = { Text(item.title) },
                    selected = selected,
                    onClick = {
                        navController.navigate(item.route) {
                            // Pop up to the start destination of the graph to
                            // avoid building up a large stack of destinations
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            // Avoid multiple copies of the same destination when
                            // reselecting the same item
                            launchSingleTop = true
                            // Restore state when reselecting a previously selected item
                            restoreState = true
                        }
                    }
                )
            }
        }
    }
}

/**
 * Bottom navigation item.
 *
 * @param title The title of the item.
 * @param icon The icon of the item.
 * @param route The route of the item.
 */
data class BottomNavItem(
    val title: String,
    val icon: ImageVector,
    val route: String
)
