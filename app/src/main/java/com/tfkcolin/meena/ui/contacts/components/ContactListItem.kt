package com.tfkcolin.meena.ui.contacts.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Block
import androidx.compose.material.icons.filled.Chat
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.StarBorder
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.getInitial
import com.tfkcolin.meena.data.models.isFavorite
import com.tfkcolin.meena.ui.components.ContextMenuItem
import com.tfkcolin.meena.ui.components.LongPressContextMenu

/**
 * Contact list item component.
 *
 * @param contact The contact to display.
 * @param onClick The click handler.
 * @param onRemove The remove handler (optional).
 * @param onEdit The edit handler (optional).
 * @param onMessage The message handler (optional).
 * @param onToggleFavorite The toggle favorite handler (optional).
 * @param onToggleBlocked The toggle blocked handler (optional).
 * @param modifier The modifier for the component.
 */
@Composable
fun ContactListItem(
    contact: Contact,
    onClick: () -> Unit,
    onRemove: (() -> Unit)? = null,
    onEdit: (() -> Unit)? = null,
    onMessage: (() -> Unit)? = null,
    onToggleFavorite: (() -> Unit)? = null,
    onToggleBlocked: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    // Create context menu items
    val menuItems = mutableListOf<ContextMenuItem>().apply {
        onMessage?.let {
            add(ContextMenuItem(
                text = "Message",
                icon = Icons.Default.Chat,
                onClick = onMessage
            ))
        }
        onEdit?.let {
            add(ContextMenuItem(
                text = "Edit",
                icon = Icons.Default.Edit,
                onClick = onEdit
            ))
        }
        onToggleFavorite?.let {
            add(ContextMenuItem(
                text = if (contact.isFavorite()) "Remove from Favorites" else "Add to Favorites",
                icon = if (contact.isFavorite()) Icons.Default.Star else Icons.Default.StarBorder,
                onClick = onToggleFavorite
            ))
        }
        onToggleBlocked?.let {
            add(ContextMenuItem(
                text = if (contact.isBlocked()) "Unblock" else "Block",
                icon = Icons.Default.Block,
                onClick = onToggleBlocked
            ))
        }
        onRemove?.let {
            add(ContextMenuItem(
                text = "Delete",
                icon = Icons.Default.Delete,
                onClick = onRemove
            ))
        }
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        LongPressContextMenu(
            menuItems = menuItems
        ) { clickableModifier ->
        Row(
            modifier = clickableModifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Profile picture or placeholder
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                // If there's a profile picture URL, load it with Coil
                // Otherwise, show the first letter of the display name
                val initial = contact.getInitial()

                Text(
                    text = initial,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Contact info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = contact.displayName ?: "User",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                if (contact.notes != null) {
                    Text(
                        text = contact.notes,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                } else {
                    Text(
                        text = "ID: ${contact.contactId}",
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }

            // Remove button (if provided)
            if (onRemove != null) {
                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Remove",
                        tint = MaterialTheme.colorScheme.error
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))
            }

            // Favorite indicator
            if (contact.isFavorite()) {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = "Favorite",
                    tint = Color(0xFFFFC107) // Amber color for star
                )

                Spacer(modifier = Modifier.width(8.dp))
            }

            // Blocked indicator
            if (contact.isBlocked()) {
                Icon(
                    imageVector = Icons.Default.Block,
                    contentDescription = "Blocked",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
        }
    }
}
