package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.utils.MediaAttachmentHelper

/**
 * Grid layout for displaying multiple media attachments.
 *
 * @param attachments The list of media attachments to display.
 * @param onAttachmentClick The callback for when an attachment is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun MediaAttachmentGrid(
    attachments: List<MediaAttachment>,
    onAttachmentClick: (MediaAttachment) -> Unit,
    mediaAttachmentHelper: MediaAttachmentHelper,
    modifier: Modifier = Modifier
) {
    when {
        attachments.isEmpty() -> {
            // No attachments, don't display anything
            return
        }
        attachments.size == 1 -> {
            // Single attachment, display it normally
            MediaAttachmentView(
                attachment = attachments.first(),
                onClick = { onAttachmentClick(attachments.first()) },
                mediaAttachmentHelper = mediaAttachmentHelper,
                modifier = modifier
            )
        }
        attachments.size == 2 -> {
            // Two attachments, display them side by side
            Row(
                modifier = modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                attachments.forEach { attachment ->
                    MediaAttachmentView(
                        attachment = attachment,
                        onClick = { onAttachmentClick(attachment) },
                        mediaAttachmentHelper = mediaAttachmentHelper,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                    )
                }
            }
        }
        attachments.size == 3 -> {
            // Three attachments, display one large and two small
            Column(
                modifier = modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // First attachment takes full width
                MediaAttachmentView(
                    attachment = attachments[0],
                    onClick = { onAttachmentClick(attachments[0]) },
                    mediaAttachmentHelper = mediaAttachmentHelper,
                    modifier = Modifier.fillMaxWidth()
                )

                // Second and third attachments side by side
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    attachments.subList(1, 3).forEach { attachment ->
                        MediaAttachmentView(
                            attachment = attachment,
                            onClick = { onAttachmentClick(attachment) },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f)
                        )
                    }
                }
            }
        }
        attachments.size == 4 -> {
            // Four attachments, display in a 2x2 grid
            Column(
                modifier = modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // First row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    attachments.subList(0, 2).forEach { attachment ->
                        MediaAttachmentView(
                            attachment = attachment,
                            onClick = { onAttachmentClick(attachment) },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f)
                        )
                    }
                }

                // Second row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    attachments.subList(2, 4).forEach { attachment ->
                        MediaAttachmentView(
                            attachment = attachment,
                            onClick = { onAttachmentClick(attachment) },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f)
                        )
                    }
                }
            }
        }
        else -> {
            // More than 4 attachments, display first 4 with a "+X more" overlay on the last one
            Column(
                modifier = modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // First row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    attachments.subList(0, 2).forEach { attachment ->
                        MediaAttachmentView(
                            attachment = attachment,
                            onClick = { onAttachmentClick(attachment) },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f)
                        )
                    }
                }

                // Second row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // Third attachment
                    MediaAttachmentView(
                        attachment = attachments[2],
                        onClick = { onAttachmentClick(attachments[2]) },
                        mediaAttachmentHelper = mediaAttachmentHelper,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1.5f)
                    )

                    // Fourth attachment with overlay
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1.5f)
                    ) {
                        MediaAttachmentView(
                            attachment = attachments[3],
                            onClick = { onAttachmentClick(attachments[3]) },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxWidth()
                        )

                        if (attachments.size > 4) {
                            // Overlay with "+X more" text
                            Box(
                                modifier = Modifier
                                    .matchParentSize()
                                    .clip(RoundedCornerShape(8.dp))
                                    .padding(4.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "+${attachments.size - 4} more",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onPrimary,
                                    modifier = Modifier
                                        .padding(8.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
