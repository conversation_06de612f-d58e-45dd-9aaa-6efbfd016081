package com.tfkcolin.meena.ui.viewmodels

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.domain.usecases.contacts.AddContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.BlockContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactsUseCase
import com.tfkcolin.meena.domain.usecases.contacts.UnblockContactUseCase
import com.tfkcolin.meena.domain.usecases.contacts.UpdateContactUseCase
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

/**
 * ViewModel for contact operations.
 */
@HiltViewModel
class ContactViewModel @Inject constructor(
    private val getContactsUseCase: GetContactsUseCase,
    private val addContactUseCase: AddContactUseCase,
    private val updateContactUseCase: UpdateContactUseCase,
    private val blockContactUseCase: BlockContactUseCase,
    private val unblockContactUseCase: UnblockContactUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for contact operations
    private val _contactState = MutableStateFlow(ContactState())
    val contactState: StateFlow<ContactState> = _contactState.asStateFlow()

    init {
        // Load contacts from API
        loadContacts()
    }

    /**
     * Load contacts from the API.
     */
    fun loadContacts() {
        launchWithErrorHandling {
            _contactState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { getContactsUseCase() },
                onSuccess = { response ->
                    _contactState.update {
                        it.copy(
                            totalCount = response.totalCount,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }
                },
                onError = { error ->
                    _contactState.update {
                        it.copy(
                            properties = it.properties.copy(
                                isLoading = false,
                                error = getErrorMessage(error)
                            )
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Add a contact.
     */
    fun addContact(userHandle: String, displayName: String? = null, notes: String? = null) {
        launchWithErrorHandling {
            _contactState.update { it.copy(
                addContactOperation = it.addContactOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    addContactUseCase(
                        userHandle = userHandle,
                        displayName = displayName,
                        notes = notes
                    )
                },
                onSuccess = { response ->
                    _contactState.update {
                        it.copy(
                            addContactOperation = it.addContactOperation.success()
                        )
                    }
                },
                onError = { error ->
                    _contactState.update {
                        it.copy(
                            addContactOperation = it.addContactOperation.failure(
                                getErrorMessage(error, "Failed to add contact")
                            )
                        )
                    }
                },
                showLoading = false
            )
        }
    }

    /**
     * Update a contact.
     */
    fun updateContact(contactId: String, displayName: String? = null, notes: String? = null, relationship: String? = null) {
        launchWithErrorHandling {
            _contactState.update { it.copy(
                updateContactOperation = it.updateContactOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    updateContactUseCase(
                        contactId = contactId,
                        displayName = displayName,
                        notes = notes,
                        relationship = relationship
                    )
                },
                onSuccess = { response ->
                    _contactState.update {
                        it.copy(
                            updateContactOperation = it.updateContactOperation.success()
                        )
                    }
                },
                onError = { error ->
                    _contactState.update {
                        it.copy(
                            updateContactOperation = it.updateContactOperation.failure(
                                getErrorMessage(error, "Failed to update contact")
                            )
                        )
                    }
                },
                showLoading = false
            )
        }
    }

    /**
     * Block a contact.
     */
    fun blockContact(contactId: String) {
        launchWithErrorHandling {
            _contactState.update { it.copy(
                blockContactOperation = it.blockContactOperation.start()
            ) }

            executeUseCase(
                useCase = { blockContactUseCase(contactId) },
                onSuccess = {
                    _contactState.update {
                        it.copy(
                            blockContactOperation = it.blockContactOperation.success()
                        )
                    }
                },
                onError = { error ->
                    _contactState.update {
                        it.copy(
                            blockContactOperation = it.blockContactOperation.failure(
                                getErrorMessage(error, "Failed to block contact")
                            )
                        )
                    }
                },
                showLoading = false
            )
        }
    }

    /**
     * Unblock a contact.
     */
    fun unblockContact(contactId: String) {
        launchWithErrorHandling {
            _contactState.update { it.copy(
                unblockContactOperation = it.unblockContactOperation.start()
            ) }

            executeUseCase(
                useCase = { unblockContactUseCase(contactId) },
                onSuccess = {
                    _contactState.update {
                        it.copy(
                            unblockContactOperation = it.unblockContactOperation.success()
                        )
                    }
                },
                onError = { error ->
                    _contactState.update {
                        it.copy(
                            unblockContactOperation = it.unblockContactOperation.failure(
                                getErrorMessage(error, "Failed to unblock contact")
                            )
                        )
                    }
                },
                showLoading = false
            )
        }
    }

    /**
     * Get error message from a throwable.
     *
     * @param throwable The throwable to get the error message from.
     * @param fallback The fallback message if the throwable doesn't have a message.
     * @return The error message.
     */
    private fun getErrorMessage(throwable: Throwable, fallback: String? = null): String {
        return errorHandler.getErrorMessage(throwable, fallback)
    }
}

/**
 * State for contact operations.
 */
data class ContactState(
    val totalCount: Int = 0,
    val addContactOperation: OperationState = OperationState(),
    val updateContactOperation: OperationState = OperationState(),
    val blockContactOperation: OperationState = OperationState(),
    val unblockContactOperation: OperationState = OperationState(),
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
