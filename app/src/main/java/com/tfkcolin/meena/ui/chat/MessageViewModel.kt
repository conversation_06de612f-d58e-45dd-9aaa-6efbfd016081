package com.tfkcolin.meena.ui.chat

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.domain.usecases.chat.GetMessagesFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.MarkChatMessagesAsReadUseCase
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.TokenManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.UUID
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import javax.inject.Inject
import kotlin.random.Random
import com.tfkcolin.meena.config.AppConfig // Added for ENABLE_MOCK_LOGGING

/**
 * ViewModel for message operations in a chat.
 * This version is adapted for mock backend with local Room database.
 */
@HiltViewModel
class MessageViewModel @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val tokenManager: TokenManager,
    private val aiResponseGenerator: AIResponseGenerator,
    private val getMessagesFlowUseCase: GetMessagesFlowUseCase,
    private val markChatMessagesAsReadUseCase: MarkChatMessagesAsReadUseCase,
    private val mediaAttachmentHelper: MediaAttachmentHelper, // Inject MediaAttachmentHelper
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for message operations
    private val _uiState = MutableStateFlow(MessageUiState())
    val uiState: StateFlow<MessageUiState> = _uiState.asStateFlow()

    // Current chat ID
    private val _currentChatId = MutableStateFlow<String?>(null)
    val currentChatId: StateFlow<String?> = _currentChatId.asStateFlow()

    // Messages for the current chat
    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    // Typing indicators for other participants in the chat
    private val _typingUsers = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val typingUsers: StateFlow<Map<String, Boolean>> = _typingUsers.asStateFlow()

    init {
        // Subscribe to messages flow for the current chat
        viewModelScope.launch {
            _currentChatId.collectLatest { chatId ->
                if (chatId != null) {
                    getMessagesFlowUseCase(chatId).collectLatest { messages ->
                        _messages.value = messages
                    }
                } else {
                    _messages.value = emptyList()
                }
            }
        }
    }

    /**
     * Set the current chat and load its messages.
     *
     * @param chatId The chat ID.
     */
    fun setCurrentChat(chatId: String) {
        if (_currentChatId.value == chatId) return

        // Clear typing indicators from previous chat if any
        _typingUsers.value = emptyMap()
        
        _currentChatId.value = chatId
        // Messages will be loaded automatically by the flow collector in init block
        // Mark all messages in this chat as read
        viewModelScope.launch {
            markChatMessagesAsReadUseCase(chatId)
        }
    }

    /**
     * Load messages for a chat.
     * In mock mode, messages are observed via flow from Room DB.
     * This method primarily triggers the flow collection.
     *
     * @param chatId The chat ID.
     */
    fun loadMessages(chatId: String) {
        // The messages are already being collected via getMessagesFlowUseCase in the init block
        // We just need to ensure the loading state is handled.
        viewModelScope.launch {
            _uiState.update { it.copy(properties = it.properties.copy(isLoading = true, error = null)) }
            // Simulate a brief loading time
            delay(200)
            _uiState.update { it.copy(properties = it.properties.copy(isLoading = false)) }
        }
    }

    /**
     * Send a message.
     * This method now handles mock AI responses for one-to-one and group chats.
     *
     * @param recipientId The recipient ID (for one-to-one) or chat ID (for group/channel).
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     */
    fun sendMessage(
        recipientId: String, // This is actually the chat ID for group/channel, or recipient for 1-1
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ) {
        val chatId = _currentChatId.value ?: return
        val currentUserId = tokenManager.getUserId() ?: return

        launchWithErrorHandling {
            _uiState.update { it.copy(sendOperation = it.sendOperation.start()) }

            // Create the user's message
            val userMessage = Message(
                id = UUID.randomUUID().toString(),
                chatId = chatId,
                senderId = currentUserId,
                recipientId = recipientId, // For 1-1, this is the actual recipient. For group, it's the chat ID.
                content = content,
                contentType = contentType,
                mediaUrl = mediaUrl,
                hasAttachments = mediaUrl != null,
                timestamp = System.currentTimeMillis(),
                status = "sent"
            )
            mockDataStorage.addMessage(userMessage)
            _uiState.update { it.copy(sendOperation = it.sendOperation.success()) }

            // Update chat's last message
            val currentChat = mockDataStorage.getChat(chatId)
            if (currentChat != null) {
                mockDataStorage.updateChat(currentChat.copy(
                    lastMessage = content,
                    lastMessageTimestamp = System.currentTimeMillis(),
                    lastMessageSenderId = currentUserId
                ))
            }

            // --- Simulate AI response ---
            val chat = mockDataStorage.getChat(chatId)
            if (chat != null) {
                when (chat.conversationType) {
                    ConversationType.ONE_TO_ONE.value -> {
                        val otherParticipantId = chat.participantIds.split(",").first { it != currentUserId }
                        if (MockDataGenerator.isMockAIContact(otherParticipantId)) {
                            simulateAIResponse(chatId, otherParticipantId, content)
                        }
                    }
                    ConversationType.GROUP.value, ConversationType.CHANNEL.value -> {
                        val participants = chat.participantIds.split(",")
                        val otherParticipants = participants.filter { it != currentUserId }
                        if (otherParticipants.isNotEmpty()) {
                            val respondingParticipantId = otherParticipants.random()
                            // Only simulate response if the chosen participant is a mock AI contact
                            if (MockDataGenerator.isMockAIContact(respondingParticipantId)) {
                                simulateAIResponse(chatId, respondingParticipantId, content)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Simulate an AI response from a mock contact.
     */
    private fun simulateAIResponse(chatId: String, senderId: String, userMessageContent: String) {
        viewModelScope.launch {
            try {
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Starting AI response simulation for sender $senderId")
                }

                // Set typing indicator for the AI sender before generating response
                _typingUsers.update { it + (senderId to true) }
                
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Set typing indicator true for $senderId, current state: ${_typingUsers.value}")
                }
                
                // Add a small initial delay to make typing indicator visible
                delay(500)

                // Generate AI response (this already includes the response delay)
                val aiResponse = aiResponseGenerator.generateResponse(
                    userMessage = userMessageContent,
                    chatId = chatId,
                    mockAIContactId = senderId
                )

                // Add an extra small delay after response generation
                delay(500)

                // Create and send the AI's message
                val aiMessage = Message(
                    id = UUID.randomUUID().toString(),
                    chatId = chatId,
                    senderId = senderId,
                    recipientId = tokenManager.getUserId() ?: "", // Recipient is the current user
                    content = aiResponse.text,
                    contentType = "text",
                    timestamp = System.currentTimeMillis(),
                    status = "delivered" // AI messages are "delivered" immediately
                )
                mockDataStorage.addMessage(aiMessage)

                // Update chat's last message
                val currentChat = mockDataStorage.getChat(chatId)
                if (currentChat != null) {
                    mockDataStorage.updateChat(currentChat.copy(
                        lastMessage = aiResponse.text,
                        lastMessageTimestamp = System.currentTimeMillis(),
                        lastMessageSenderId = senderId
                    ))
                }
            } catch (e: Exception) {
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Error simulating AI response: ${e.message}")
                    e.printStackTrace()
                }
            } finally {
                // Clear typing indicator
                _typingUsers.update { it - senderId }
                
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Cleared typing indicator for $senderId, current state: ${_typingUsers.value}")
                }
            }
        }
    }

    /**
     * Mark a message as read.
     *
     * @param messageId The message ID.
     */
    fun markMessageAsRead(messageId: String) {
        viewModelScope.launch {
            markChatMessagesAsReadUseCase(messageId)
        }
    }

    // All other message operations (edit, delete, forward, reactions, replies, attachments)
    // will be simplified or removed as per the plan. For now, I'll remove their implementations
    // and keep only the necessary ones for basic chat functionality.

    // Simplified/Removed methods:
    fun sendMessageWithAttachments(recipientId: String, content: String, attachments: List<MediaAttachment>) {
        // TODO: Implement actual attachment sending logic using mediaAttachmentHelper
        // For now, just log or handle as a no-op if mock mode doesn't support it fully
    }
    fun uploadMediaForMessage(uri: Uri, contentType: String) {
        // TODO: Implement actual media upload logic using mediaAttachmentHelper
    }
    fun updateMessageStatus(messageId: String, status: String) {
        // This is handled by markChatMessagesAsReadUseCase for "read" status
        // Other statuses are not actively managed in mock mode.
    }
    fun sendTypingIndicator(isTyping: Boolean) {
        val chatId = _currentChatId.value ?: return
        val currentUserId = tokenManager.getUserId() ?: return
        
        viewModelScope.launch {
            val chat = mockDataStorage.getChat(chatId)
            if (chat != null) {
                // Update typing state for current user
                _typingUsers.update { currentTyping ->
                    if (isTyping) {
                        currentTyping + (currentUserId to true)
                    } else {
                        currentTyping - currentUserId
                    }
                }
            }
        }
    }
    fun editMessage(messageId: String, newContent: String) {
        // TODO: Implement actual message editing logic
    }
    fun deleteMessageForSelf(messageId: String) {
        // TODO: Implement actual delete for self logic
    }
    fun deleteMessageForEveryone(messageId: String) {
        // TODO: Implement actual delete for everyone logic
    }
    fun forwardMessage(messageId: String, targetChatId: String, additionalContent: String? = null) {
        // TODO: Implement actual message forwarding logic
    }
    fun searchMessages(query: String) {
        // TODO: Implement actual message search logic
    }
    fun searchMessagesInCurrentChat(query: String) {
        // TODO: Implement actual message search logic
    }
    fun clearSearch() {
        // No-op as search is not supported
    }
    fun setMessageToEdit(message: Message) {
        // TODO: Implement actual message editing logic
    }
    fun clearMessageToEdit() {
        // No-op as edit is not supported
    }
    fun addAttachment(attachment: MediaAttachment) {
        // TODO: Implement actual attachment adding logic
    }
    fun removeAttachment(attachment: MediaAttachment) {
        // TODO: Implement actual attachment removing logic
    }
    fun clearAttachments() {
        // TODO: Implement actual attachment clearing logic
    }
    fun addReaction(messageId: String, emoji: String) {
        // TODO: Implement actual reaction adding logic
    }
    fun removeReaction(messageId: String, emoji: String) {
        // TODO: Implement actual reaction removing logic
    }
    fun toggleReaction(messageId: String, emoji: String, currentUserId: String) {
        // TODO: Implement actual reaction toggling logic
    }
    fun replyToMessage(recipientId: String, content: String, replyToMessageId: String, contentType: String = "text", mediaUrl: String? = null) {
        // TODO: Implement actual reply logic
    }
    fun replyToMessageWithAttachments(recipientId: String, content: String, replyToMessageId: String, attachments: List<MediaAttachment>) {
        // TODO: Implement actual reply with attachments logic
    }
    fun setMessageToReplyTo(message: Message) {
        // TODO: Implement actual reply logic
    }
    fun clearMessageToReplyTo() {
        // No-op as reply is not supported
    }
    fun downloadMediaAttachment(attachment: MediaAttachment) {
        // TODO: Implement actual media download logic
    }

    /**
     * Reset message operation states.
     */
    fun resetMessageOperationStates() {
        _uiState.update { it.copy(
            sendOperation = it.sendOperation.reset(),
            editOperation = it.editOperation.reset(),
            deleteOperation = it.deleteOperation.reset(),
            forwardOperation = it.forwardOperation.reset(),
            searchOperation = it.searchOperation.reset(),
            reactionOperation = it.reactionOperation.reset(),
            replyOperation = it.replyOperation.reset()
        ) }
    }
}
