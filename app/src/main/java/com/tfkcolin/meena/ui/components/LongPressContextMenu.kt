package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * A composable that provides a long-press context menu.
 *
 * @param menuItems The list of menu items to display.
 * @param content The content to be displayed.
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LongPressContextMenu(
    menuItems: List<ContextMenuItem>,
    modifier: Modifier = Modifier,
    content: @Composable (Modifier) -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }

    val clickableModifier = modifier.combinedClickable(
        interactionSource = interactionSource,
        indication = null,
        onClick = { /* Handle normal click in the content */ },
        onLongClick = { showMenu = true }
    )

    content(clickableModifier)

    DropdownMenu(
        expanded = showMenu,
        onDismissRequest = { showMenu = false }
    ) {
        menuItems.forEach { item ->
            DropdownMenuItem(
                text = { Text(item.text) },
                leadingIcon = item.icon?.let { icon ->
                    { Icon(imageVector = icon, contentDescription = item.text) }
                },
                onClick = {
                    item.onClick()
                    showMenu = false
                }
            )
        }
    }
}

/**
 * Data class representing a context menu item.
 */
data class ContextMenuItem(
    val text: String,
    val icon: ImageVector? = null,
    val onClick: () -> Unit
)
