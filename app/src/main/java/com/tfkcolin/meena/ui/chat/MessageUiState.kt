package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * UI state for message operations.
 */
data class MessageUiState(
    val properties: UiStateProperties = UiStateProperties(),
    val sendOperation: OperationState = OperationState(),
    val searchOperation: OperationState = OperationState(),
    val editOperation: OperationState = OperationState(),
    val deleteOperation: OperationState = OperationState(),
    val forwardOperation: OperationState = OperationState(),
    val reactionOperation: OperationState = OperationState(),
    val replyOperation: OperationState = OperationState(),
    val totalMessageCount: Int = 0,
    val selectedAttachments: List<MediaAttachment> = emptyList(),
    val searchQuery: String = "",
    val searchResults: List<Message> = emptyList(),
    val messageToEdit: Message? = null,
    val messageToReplyTo: Message? = null,
    val typingUsers: Map<String, Boolean> = emptyMap()
) : UiState {
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error

    // Convenience properties for backward compatibility
    val isSending: Boolean get() = sendOperation.isInProgress
    val isSearching: Boolean get() = searchOperation.isInProgress
    val isEditing: Boolean get() = editOperation.isInProgress
    val isDeleting: Boolean get() = deleteOperation.isInProgress
    val isForwarding: Boolean get() = forwardOperation.isInProgress
    val isReacting: Boolean get() = reactionOperation.isInProgress
    val isReplying: Boolean get() = replyOperation.isInProgress
    val isMessageSent: Boolean get() = sendOperation.isSuccessful
    val isMessageEdited: Boolean get() = editOperation.isSuccessful
    val isMessageDeleted: Boolean get() = deleteOperation.isSuccessful
    val isMessageForwarded: Boolean get() = forwardOperation.isSuccessful
    val isReactionAdded: Boolean get() = reactionOperation.isSuccessful
    val isReplySuccessful: Boolean get() = replyOperation.isSuccessful
}
