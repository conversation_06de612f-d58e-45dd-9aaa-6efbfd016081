package com.tfkcolin.meena.ui.chat

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.R
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.domain.usecases.chat.ArchiveChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetArchivedChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetChatsUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetGroupChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetNonArchivedChatsFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetOrCreateChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.MuteChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.PinChatUseCase
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.ui.models.ConversationListItem
import com.tfkcolin.meena.ui.models.ConversationWrapper
import com.tfkcolin.meena.utils.ErrorHandler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the conversation list screen.
 */
@HiltViewModel
class ConversationListViewModel @Inject constructor(
    private val getChatsUseCase: GetChatsUseCase,
    private val getChatsFlowUseCase: GetChatsFlowUseCase,
    private val getNonArchivedChatsFlowUseCase: GetNonArchivedChatsFlowUseCase,
    private val getArchivedChatsFlowUseCase: GetArchivedChatsFlowUseCase,
    private val getGroupChatsFlowUseCase: GetGroupChatsFlowUseCase,
    private val getOrCreateChatUseCase: GetOrCreateChatUseCase,
    private val archiveChatUseCase: ArchiveChatUseCase,
    private val muteChatUseCase: MuteChatUseCase,
    private val pinChatUseCase: PinChatUseCase,
    private val tokenManager: TokenManager,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for conversation list operations
    private val _uiState = MutableStateFlow(ConversationListUiState())
    val uiState: StateFlow<ConversationListUiState> = _uiState.asStateFlow()

    // Chats from local database
    private val _showArchivedChats = MutableStateFlow(false)
    @OptIn(ExperimentalCoroutinesApi::class)
    val chats = _showArchivedChats
        .flatMapLatest { showArchived ->
            if (showArchived) {
                getArchivedChatsFlowUseCase()
            } else {
                getNonArchivedChatsFlowUseCase()
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Group chats from local database
    val groupChats = getGroupChatsFlowUseCase()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Contact name map for displaying one-to-one chat names
    private val _contactNameMap = MutableStateFlow<Map<String, String>>(emptyMap())

    // Contact avatar map for displaying one-to-one chat avatars
    private val _contactAvatarMap = MutableStateFlow<Map<String, String?>>(emptyMap())

    // Conversation wrappers for type-specific functionality
    private val _conversationWrappers = MutableStateFlow<List<ConversationWrapper>>(emptyList())
    val conversationWrappers: StateFlow<List<ConversationWrapper>> = _conversationWrappers.asStateFlow()

    // Conversation list items for UI display
    private val _conversationListItems = MutableStateFlow<List<ConversationListItem>>(emptyList())
    val conversationListItems: StateFlow<List<ConversationListItem>> = _conversationListItems.asStateFlow()

    init {
        // Load chats from API
        loadChats()

        // Update conversation list items when chats change
        viewModelScope.launch {
            chats.collectLatest { chatList ->
                updateConversationListItems(chatList)
            }
        }
    }

    /**
     * Load chats from the API.
     */
    fun loadChats() {
        launchWithErrorHandling {
            setLoading(true)
            _uiState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { getChatsUseCase() },
                onSuccess = { response ->
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false),
                            totalChatCount = response.total_count
                        )
                    }
                    setLoading(false)
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                    setError(errorMessage)
                },
                showLoading = false // We're managing loading state manually
            )
        }
    }

    /**
     * Get or create a chat with a user.
     *
     * @param userId The user ID.
     */
    fun getOrCreateChat(userId: String) {
        launchWithErrorHandling {
            _uiState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null),
                createChatOperation = it.createChatOperation.start()
            ) }

            executeUseCase(
                useCase = { getOrCreateChatUseCase(userId) },
                onSuccess = { chat ->
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false),
                            createChatOperation = it.createChatOperation.success(),
                            newChatId = chat.id
                        )
                    }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false, error = errorMessage),
                            createChatOperation = it.createChatOperation.failure(errorMessage)
                        )
                    }
                    setError(errorMessage)
                },
                showLoading = false // We're managing loading state manually
            )
        }
    }

    /**
     * Archive or unarchive a conversation.
     *
     * @param conversationId The conversation ID.
     * @param isArchived Whether the conversation should be archived.
     */
    fun archiveChat(conversationId: String, isArchived: Boolean) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                archiveOperation = it.archiveOperation.start()
            ) }

            executeUseCase(
                useCase = { archiveChatUseCase(conversationId, isArchived) },
                onSuccess = {
                    _uiState.update { it.copy(
                        archiveOperation = it.archiveOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(if (isArchived) R.string.error_unknown else R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        archiveOperation = it.archiveOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Mute or unmute a conversation.
     *
     * @param conversationId The conversation ID.
     * @param isMuted Whether the conversation should be muted.
     */
    fun muteChat(conversationId: String, isMuted: Boolean) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                muteOperation = it.muteOperation.start()
            ) }

            executeUseCase(
                useCase = { muteChatUseCase(conversationId, isMuted) },
                onSuccess = {
                    _uiState.update { it.copy(
                        muteOperation = it.muteOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(if (isMuted) R.string.error_unknown else R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        muteOperation = it.muteOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Pin or unpin a conversation.
     *
     * @param conversationId The conversation ID.
     * @param isPinned Whether the conversation should be pinned.
     */
    fun pinChat(conversationId: String, isPinned: Boolean) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                pinOperation = it.pinOperation.start()
            ) }

            executeUseCase(
                useCase = { pinChatUseCase(conversationId, isPinned) },
                onSuccess = {
                    _uiState.update { it.copy(
                        pinOperation = it.pinOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(if (isPinned) R.string.error_unknown else R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        pinOperation = it.pinOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Toggle showing archived chats.
     *
     * @param show Whether to show archived chats.
     */
    fun toggleShowArchivedChats(show: Boolean) {
        _showArchivedChats.value = show
        _uiState.update { it.copy(
            showArchivedChats = show
        ) }
    }

    /**
     * Update the conversation wrappers and list items based on the chat list.
     *
     * @param chatList The list of chats.
     */
    private fun updateConversationListItems(chatList: List<Chat>) {
        val currentUserId = tokenManager.getUserId() ?: return

        // Create conversation wrappers
        val wrappers = chatList.map { chat ->
            ConversationWrapper.fromChat(
                chat = chat,
                currentUserId = currentUserId,
                contactNameMap = _contactNameMap.value,
                contactAvatarMap = _contactAvatarMap.value
            )
        }
        _conversationWrappers.value = wrappers

        // Create conversation list items from wrappers
        val items = wrappers.map { wrapper ->
            ConversationListItem.fromConversationWrapper(wrapper)
        }
        _conversationListItems.value = items
    }

    /**
     * Update the contact name map.
     *
     * @param contactNameMap The map of user IDs to display names.
     */
    fun updateContactNameMap(contactNameMap: Map<String, String>) {
        _contactNameMap.value = contactNameMap

        // Update conversation list items with the new contact names
        updateConversationListItems(chats.value)
    }

    /**
     * Update the contact avatar map.
     *
     * @param contactAvatarMap The map of user IDs to avatar URLs.
     */
    fun updateContactAvatarMap(contactAvatarMap: Map<String, String?>) {
        _contactAvatarMap.value = contactAvatarMap

        // Update conversation list items with the new avatar URLs
        updateConversationListItems(chats.value)
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _uiState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
    }

    /**
     * Reset conversation operation states.
     */
    fun resetConversationOperationStates() {
        _uiState.update { it.copy(
            archiveOperation = it.archiveOperation.reset(),
            muteOperation = it.muteOperation.reset(),
            pinOperation = it.pinOperation.reset(),
            createChatOperation = it.createChatOperation.reset(),
            newChatId = null
        ) }
    }
}


