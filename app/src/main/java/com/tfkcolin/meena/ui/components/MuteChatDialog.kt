package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * Mute chat dialog component.
 *
 * @param isLoading Whether the mute operation is in progress.
 * @param onDismiss The callback for when the dialog is dismissed.
 * @param onMute The callback for when the mute button is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun MuteChatDialog(
    isLoading: Boolean,
    onDismiss: () -> Unit,
    onMute: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedOption by remember { mutableStateOf(MuteOption.EIGHT_HOURS) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Mute Notifications",
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                )
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth().selectableGroup()
            ) {
                MuteOption.values().forEach { option ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .selectable(
                                selected = (option == selectedOption),
                                onClick = { selectedOption = option },
                                role = Role.RadioButton
                            )
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = (option == selectedOption),
                            onClick = null // null because we're handling the click on the row
                        )
                        Text(
                            text = option.label,
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(start = 16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = { onMute() },
                enabled = !isLoading
            ) {
                Text("Mute")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text("Cancel")
            }
        }
    )
}

/**
 * Mute options.
 */
enum class MuteOption(val label: String, val duration: Long?) {
    EIGHT_HOURS("8 hours", System.currentTimeMillis() + 8 * 60 * 60 * 1000),
    ONE_WEEK("1 week", System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000),
    ONE_MONTH("1 month", System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000),
    ALWAYS("Always", null)
}
