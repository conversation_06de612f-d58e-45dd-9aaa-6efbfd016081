package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.Message
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Search result item component.
 * 
 * @param message The message to display.
 * @param chatName The name of the chat.
 * @param onClick The click handler.
 * @param highlightQuery The query to highlight in the message content.
 * @param modifier The modifier for the component.
 */
@Composable
fun SearchResultItem(
    message: Message,
    chatName: String,
    onClick: () -> Unit,
    highlightQuery: String = "",
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Chat name and timestamp
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = chatName,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // Format timestamp
                val timestamp = message.timestamp.let {
                    val date = Date(it)
                    val format = SimpleDateFormat("MMM d, yyyy HH:mm", Locale.getDefault())
                    format.format(date)
                }
                
                Text(
                    text = timestamp,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Message content
            if (highlightQuery.isNotBlank() && message.content.contains(highlightQuery, ignoreCase = true)) {
                // Highlight the query in the message content
                val startIndex = message.content.indexOf(highlightQuery, ignoreCase = true)
                val endIndex = startIndex + highlightQuery.length
                
                val beforeHighlight = message.content.substring(0, startIndex)
                val highlighted = message.content.substring(startIndex, endIndex)
                val afterHighlight = message.content.substring(endIndex)
                
                Row {
                    Text(
                        text = beforeHighlight,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Text(
                        text = highlighted,
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    )
                    
                    Text(
                        text = afterHighlight,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            } else {
                Text(
                    text = message.content,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // Display attachment indicator if the message has attachments
            if (message.hasAttachments) {
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "Contains attachment",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}
