package com.tfkcolin.meena.ui.calls

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.CallMissed
import androidx.compose.material.icons.automirrored.filled.CallReceived
import androidx.compose.material.icons.filled.Call
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import java.util.*

data class Call(
    val id: String,
    val contactName: String,
    val callType: CallType,
    val timestamp: Date,
    val duration: String
)

enum class CallType(val icon: ImageVector, val color: Long) {
    INCOMING(Icons.AutoMirrored.Filled.CallReceived, 0xFF4CAF50),
    OUTGOING(Icons.Default.Call, 0xFF2196F3),
    MISSED(Icons.AutoMirrored.Filled.CallMissed, 0xFFF44336)
}

@Composable
fun CallListScreen(
    modifier: Modifier = Modifier,
    calls: List<Call> = emptyList() // Will be replaced with real data later
) {
    // UI components are now managed centrally by NavGraph
    LazyColumn(
        modifier = modifier
            .fillMaxSize(),
        contentPadding = PaddingValues(8.dp)
    ) {
        items(calls) { call ->
            CallListItem(call = call)
            HorizontalDivider(thickness = 1.dp, color = MaterialTheme.colorScheme.onSurface.copy(alpha = .2f))
        }
    }
}

@Composable
fun CallListItem(call: Call) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = call.callType.icon,
            contentDescription = null,
            tint = androidx.compose.ui.graphics.Color(call.callType.color),
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(16.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = call.contactName,
                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold)
            )
            Text(
                text = "${call.callType.name.lowercase().capitalize(Locale.ROOT)} • ${call.duration}",
                style = MaterialTheme.typography.bodySmall
            )
        }
        Text(
            text = call.timestamp.toString().take(10), // Simple date formatting
            style = MaterialTheme.typography.bodySmall
        )
    }
}
