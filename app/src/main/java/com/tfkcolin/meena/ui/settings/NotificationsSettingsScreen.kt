package com.tfkcolin.meena.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Preview
import androidx.compose.material.icons.filled.ReceiptLong
import androidx.compose.material.icons.filled.Textsms
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.SettingsItem

/**
 * Notifications settings screen.
 * Allows users to customize notification preferences.
 *
 * @param onNavigateBack Navigate back to the profile screen.
 * @param viewModel The settings view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationsSettingsScreen(
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    notificationsEnabled: Boolean,
    messagePreviewsEnabled: Boolean,
    readReceiptsEnabled: Boolean,
    typingIndicatorsEnabled: Boolean,
    onUpdateNotifications: (Boolean) -> Unit,
    onUpdateMessagePreviews: (Boolean) -> Unit,
    onUpdateReadReceipts: (Boolean) -> Unit,
    onUpdateTypingIndicators: (Boolean) -> Unit,
    error: String?,
    onClearError: () -> Unit
) {
    val scrollState = rememberScrollState()

    // Show error message
    LaunchedEffect(error) {
        error?.let {
            onShowSnackbar(it)
            onClearError()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // General notifications section
        Text(
            text = stringResource(R.string.notifications),
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = stringResource(R.string.control_how_and_when_you_receive_notifications),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        SettingsItem(
            title = stringResource(R.string.enable_notifications),
            description = stringResource(R.string.receive_notifications_for_new_messages_and_events),
            icon = Icons.Default.Notifications,
            onClick = { onUpdateNotifications(!notificationsEnabled) },
            endContent = {
                Switch(
                    checked = notificationsEnabled,
                    onCheckedChange = { onUpdateNotifications(it) }
                )
            }
        )

        SettingsItem(
            title = stringResource(R.string.message_previews),
            description = stringResource(R.string.show_message_content_in_notifications),
            icon = Icons.Default.Preview,
            onClick = { onUpdateMessagePreviews(!messagePreviewsEnabled) },
            endContent = {
                Switch(
                    checked = messagePreviewsEnabled,
                    onCheckedChange = { onUpdateMessagePreviews(it) }
                )
            }
        )

        HorizontalDivider(
            modifier = Modifier.padding(vertical = 8.dp),
            thickness = 1.dp,
            color = MaterialTheme.colorScheme.outlineVariant
        )

        // Chat features section
        Text(
            text = stringResource(R.string.chat_features),
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = stringResource(R.string.control_chat_related_features_and_notifications),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        SettingsItem(
            title = stringResource(R.string.read_receipts),
            description = stringResource(R.string.let_others_know_when_youve_read_their_messages),
            icon = Icons.Default.ReceiptLong,
            onClick = { onUpdateReadReceipts(!readReceiptsEnabled) },
            endContent = {
                Switch(
                    checked = readReceiptsEnabled,
                    onCheckedChange = { onUpdateReadReceipts(it) }
                )
            }
        )

        SettingsItem(
            title = stringResource(R.string.typing_indicators),
            description = stringResource(R.string.let_others_know_when_youre_typing),
            icon = Icons.Default.Textsms,
            onClick = { onUpdateTypingIndicators(!typingIndicatorsEnabled) },
            endContent = {
                Switch(
                    checked = typingIndicatorsEnabled,
                    onCheckedChange = { onUpdateTypingIndicators(it) }
                )
            }
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}