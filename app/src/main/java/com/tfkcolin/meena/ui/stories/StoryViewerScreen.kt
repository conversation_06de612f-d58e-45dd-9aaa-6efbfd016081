package com.tfkcolin.meena.ui.stories

import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.waitForUpOrCancellation
import kotlinx.coroutines.launch
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.foundation.StoryProgressIndicator
import com.tfkcolin.meena.ui.components.foundation.UserAvatar
import com.tfkcolin.meena.ui.stories.models.OverlayType
import com.tfkcolin.meena.ui.stories.models.SegmentType
import com.tfkcolin.meena.ui.stories.models.Story
import java.text.DateFormat

/**
 * Screen for viewing stories.
 *
 * @param stories The list of stories to view.
 * @param initialStoryIndex The index of the initial story to view.
 * @param onClose The callback when the viewer is closed.
 * @param onReplyToStory The callback when a reply is sent to a story.
 * @param onReactionSelected The callback when a reaction is selected for a story.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StoryViewerScreen(
    stories: List<Story>,
    initialStoryIndex: Int = 0,
    onClose: () -> Unit,
    onReplyToStory: (Story, String) -> Unit,
    onReactionSelected: (Story, String) -> Unit
) {
    var currentStoryIndex by remember { mutableIntStateOf(initialStoryIndex) }
    var currentSegmentIndex by remember { mutableIntStateOf(0) }

    // Story progress tracking
    val currentStory = stories.getOrNull(currentStoryIndex) ?: stories.firstOrNull()
    val storySegments = currentStory?.segments ?: emptyList()
    val currentSegment = storySegments.getOrNull(currentSegmentIndex)

    // State for controlling story progress
    var isPaused by remember { mutableStateOf(false) }
    var showReplyInput by remember { mutableStateOf(false) }
    var replyText by remember { mutableStateOf("") }

    // Create a coroutine scope for gesture handling
    val coroutineScope = rememberCoroutineScope()

    // Progress animation
    val animationDuration = 5000 // 5 seconds per segment
    val progress = remember(currentStoryIndex, currentSegmentIndex) { Animatable(0f) }

    // Control segment progress
    LaunchedEffect(currentStoryIndex, currentSegmentIndex, isPaused) {
        if (isPaused) {
            // Pause progress
            progress.stop()
        } else {
            // Animate progress and move to next segment when done
            progress.animateTo(
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = (animationDuration * (1f - progress.value)).toInt(),
                    easing = LinearEasing
                )
            )
            // When animation completes, move to next segment or story
            if (currentSegmentIndex < storySegments.size - 1) {
                // Next segment of current story
                currentSegmentIndex++
            } else if (currentStoryIndex < stories.size - 1) {
                // Next story
                currentStoryIndex++
                currentSegmentIndex = 0
            } else {
                // End of all stories
                onClose()
            }
        }
    }

    // Reset progress when changing segments
    LaunchedEffect(currentStoryIndex, currentSegmentIndex) {
        progress.snapTo(0f)
    }

    // Handle back press
    BackHandler {
        onClose()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Story content (image or video)
        currentSegment?.let { segment ->
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                when (segment.type) {
                    SegmentType.IMAGE -> {
                        AsyncImage(
                            model = segment.mediaUrl,
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    SegmentType.VIDEO -> {
                        // Video player would go here
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color.Black),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.video_content),
                                color = Color.White,
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }

                // Optional overlay content (stickers, text, etc.)
                segment.overlayItems.forEach { overlay ->
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = when {
                            overlay.position.x < 0.33f && overlay.position.y < 0.33f -> Alignment.TopStart
                            overlay.position.x > 0.66f && overlay.position.y < 0.33f -> Alignment.TopEnd
                            overlay.position.x < 0.33f && overlay.position.y > 0.66f -> Alignment.BottomStart
                            overlay.position.x > 0.66f && overlay.position.y > 0.66f -> Alignment.BottomEnd
                            overlay.position.x < 0.33f -> Alignment.CenterStart
                            overlay.position.x > 0.66f -> Alignment.CenterEnd
                            overlay.position.y < 0.33f -> Alignment.TopCenter
                            overlay.position.y > 0.66f -> Alignment.BottomCenter
                            else -> Alignment.Center
                        }
                    ) {
                        when (overlay.type) {
                            OverlayType.TEXT -> {
                                Text(
                                    text = overlay.content,
                                    color = Color.White,
                                    style = MaterialTheme.typography.titleLarge,
                                    fontWeight = FontWeight.Bold,
                                    modifier = Modifier.padding(8.dp)
                                )
                            }
                            OverlayType.STICKER -> {
                                Text(
                                    text = overlay.content,
                                    fontSize = 40.sp,
                                    modifier = Modifier.padding(8.dp)
                                )
                            }
                        }
                    }
                }
            }
        }

        // Touch areas for navigation
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 48.dp, bottom = if (showReplyInput) 64.dp else 0.dp)
        ) {
            // Previous story/segment area
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onPress = {
                                isPaused = true
                                coroutineScope.launch {
                                    try {
                                        awaitPointerEventScope {
                                            waitForUpOrCancellation()
                                            isPaused = false
                                        }
                                    } catch (e: Exception) {
                                        isPaused = false
                                    }
                                }
                            },
                            onTap = {
                                if (currentSegmentIndex > 0) {
                                    currentSegmentIndex--
                                } else if (currentStoryIndex > 0) {
                                    currentStoryIndex--
                                    currentSegmentIndex = stories[currentStoryIndex].segments.size - 1
                                }
                            }
                        )
                    }
            )

            // Next story/segment area
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onPress = {
                                isPaused = true
                                coroutineScope.launch {
                                    try {
                                        awaitPointerEventScope {
                                            waitForUpOrCancellation()
                                            isPaused = false
                                        }
                                    } catch (e: Exception) {
                                        isPaused = false
                                    }
                                }
                            },
                            onTap = {
                                if (currentSegmentIndex < storySegments.size - 1) {
                                    currentSegmentIndex++
                                } else if (currentStoryIndex < stories.size - 1) {
                                    currentStoryIndex++
                                    currentSegmentIndex = 0
                                } else {
                                    onClose()
                                }
                            }
                        )
                    }
            )
        }

        // Progress indicators
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 12.dp)
                .statusBarsPadding(),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            storySegments.forEachIndexed { index, _ ->
                StoryProgressIndicator(
                    progress = if (index < currentSegmentIndex) 1f else if (index == currentSegmentIndex) progress.value else 0f,
                    modifier = Modifier
                        .weight(1f)
                        .height(2.dp)
                )
            }
        }

        // Top info bar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .statusBarsPadding()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // User info
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // User avatar
                UserAvatar(
                    imageUrl = currentStory?.authorAvatarUrl ?: "",
                    userName = currentStory?.authorName ?: "",
                    size = 36.dp,
                    isOnline = true
                )
                Spacer(modifier = Modifier.width(12.dp))
                // Author name and time
                Column {
                    Text(
                        text = currentStory?.authorName ?: "",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = currentStory?.timePosted?.let {
                            DateFormat.getTimeInstance(DateFormat.SHORT).format(it)
                        } ?: "",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
            // Close button
            IconButton(
                onClick = onClose,
                modifier = Modifier
                    .size(36.dp)
                    .background(Color.Black.copy(alpha = 0.3f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = stringResource(R.string.close_story),
                    tint = Color.White
                )
            }
        }

        // Bottom action area
        if (!showReplyInput) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = 16.dp, vertical = 16.dp)
                    .imePadding()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .clip(RoundedCornerShape(28.dp))
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable {
                            showReplyInput = true
                            isPaused = true
                        }
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.reply_to_story_placeholder),
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    // Quick reactions
                    Row {
                        listOf("❤️", "👍", "😂", "😮").forEach { emoji ->
                            Box(
                                modifier = Modifier
                                    .size(36.dp)
                                    .clickable {
                                        currentStory?.let { story ->
                                            onReactionSelected(story, emoji)
                                        }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = emoji,
                                    fontSize = 20.sp
                                )
                            }
                        }
                    }
                }
            }
        } else {
            // Reply input field
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .background(Color.Black.copy(alpha = 0.8f))
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .imePadding(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = {
                        showReplyInput = false
                        isPaused = false
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = stringResource(R.string.cancel_reply),
                        tint = Color.White
                    )
                }
                TextField(
                    value = replyText,
                    onValueChange = { replyText = it },
                    placeholder = {
                        Text(
                            text = stringResource(R.string.reply_to_author_placeholder, currentStory?.authorName ?: ""),
                            color = Color.White.copy(alpha = 0.6f)
                        )
                    },
                    colors = TextFieldDefaults.colors(
                        focusedContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                        unfocusedContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                        disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                        cursorColor = MaterialTheme.colorScheme.primary,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent,
                    ),
                    singleLine = true,
                    modifier = Modifier.weight(1f)
                )
                IconButton(
                    onClick = {
                        if (replyText.isNotEmpty()) {
                            currentStory?.let { story ->
                                onReplyToStory(story, replyText)
                            }
                            replyText = ""
                            showReplyInput = false
                            isPaused = false
                        }
                    },
                    enabled = replyText.isNotEmpty()
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = stringResource(R.string.send_reply),
                        tint = if (replyText.isNotEmpty())
                            MaterialTheme.colorScheme.primary
                        else
                            Color.White.copy(alpha = 0.4f)
                    )
                }
            }
        }
    }
}