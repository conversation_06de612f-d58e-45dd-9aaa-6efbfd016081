package com.tfkcolin.meena.ui.models

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.data.models.PrivacyType
import com.tfkcolin.meena.data.models.getDisplayName

/**
 * UI model for displaying a conversation in a list.
 * Follows the backend API contract for ConversationListItem.
 */
data class ConversationListItem(
    val id: String,
    val name: String,
    val avatarUrl: String?,
    val lastMessage: String?,
    val lastMessageTimestamp: Long?,
    val unreadCount: Int,
    val isArchived: Boolean,
    val isMuted: Boolean,
    val isPinned: Boolean,
    val conversationType: ConversationType,
    val privacyType: PrivacyType?,
    val participantCount: Int,
    val isEncrypted: Boolean = false,
    val lastMessageSenderId: String? = null,
    val lastMessageType: String? = null
) {
    companion object {
        /**
         * Create a ConversationListItem from a Chat.
         *
         * @param chat The Chat to convert.
         * @param currentUserId The current user ID.
         * @param contactNameMap A map of user IDs to display names.
         * @return The ConversationListItem.
         */
        fun fromChat(
            chat: Chat,
            currentUserId: String,
            contactNameMap: Map<String, String>
        ): ConversationListItem {
            return ConversationListItem(
                id = chat.id,
                name = chat.getDisplayName(currentUserId, contactNameMap),
                avatarUrl = chat.avatarUrl,
                lastMessage = chat.lastMessage,
                lastMessageTimestamp = chat.lastMessageTimestamp,
                unreadCount = chat.unreadCount,
                isArchived = chat.isArchived,
                isMuted = chat.isMuted,
                isPinned = chat.isPinned,
                conversationType = chat.getConversationType(),
                privacyType = chat.getPrivacyType(),
                participantCount = chat.getParticipantIdsList().size,
                isEncrypted = chat.isEncrypted,
                lastMessageSenderId = chat.lastMessageSenderId,
                lastMessageType = chat.lastMessageType
            )
        }

        /**
         * Create a ConversationListItem from a ConversationWrapper.
         *
         * @param wrapper The ConversationWrapper to convert.
         * @return The ConversationListItem.
         */
        fun fromConversationWrapper(wrapper: ConversationWrapper): ConversationListItem {
            val displayName = when (wrapper) {
                is ConversationWrapper.OneToOneConversation -> wrapper.displayName
                is ConversationWrapper.GroupConversation -> wrapper.displayName
                is ConversationWrapper.ChannelConversation -> wrapper.displayName
            }

            val avatarUrl = when (wrapper) {
                is ConversationWrapper.OneToOneConversation -> wrapper.avatarUrl
                is ConversationWrapper.GroupConversation -> wrapper.avatarUrl
                is ConversationWrapper.ChannelConversation -> wrapper.avatarUrl
            }

            return ConversationListItem(
                id = wrapper.id,
                name = displayName,
                avatarUrl = avatarUrl,
                lastMessage = wrapper.lastMessage,
                lastMessageTimestamp = wrapper.lastMessageTimestamp,
                unreadCount = wrapper.unreadCount,
                isArchived = wrapper.isArchived,
                isMuted = wrapper.isMuted,
                isPinned = wrapper.isPinned,
                conversationType = wrapper.conversationType,
                privacyType = wrapper.privacyType,
                participantCount = wrapper.participantCount,
                isEncrypted = wrapper.isEncrypted,
                lastMessageSenderId = wrapper.lastMessageSenderId,
                lastMessageType = wrapper.lastMessageType
            )
        }
    }
}
