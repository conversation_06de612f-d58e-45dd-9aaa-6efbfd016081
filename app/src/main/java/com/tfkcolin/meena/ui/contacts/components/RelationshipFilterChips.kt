package com.tfkcolin.meena.ui.contacts.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Relationship filter chips component.
 *
 * @param selectedRelationship The currently selected relationship.
 * @param onRelationshipSelected The callback for when a relationship is selected.
 * @param modifier The modifier for the component.
 */
@Composable
fun RelationshipFilterChips(
    selectedRelationship: String?,
    onRelationshipSelected: (String?) -> Unit,
    modifier: Modifier = Modifier
) {
    val relationships = listOf(
        null to "All",
        "friend" to "Friends",
        "family" to "Family",
        "colleague" to "Colleagues",
        "acquaintance" to "Acquaintances",
        "blocked" to "Blocked"
    )
    
    LazyRow(
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        items(relationships) { (relationship, label) ->
            FilterChip(
                selected = selectedRelationship == relationship,
                onClick = { onRelationshipSelected(relationship) },
                label = { Text(label) },
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = MaterialTheme.colorScheme.primary,
                    selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                ),
                modifier = Modifier.padding(vertical = 4.dp)
            )
        }
    }
}
