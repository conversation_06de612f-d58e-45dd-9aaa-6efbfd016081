package com.tfkcolin.meena.ui.group

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Public
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.data.models.PrivacyType
import com.tfkcolin.meena.ui.components.ContactSelectionDialog
import com.tfkcolin.meena.ui.components.ErrorSnackbar
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.models.ConversationListItem

/**
 * Screen for viewing and managing group details.
 *
 * @param groupId The group ID.
 * @param onNavigateBack Navigate back to the previous screen.
 * @param onNavigateToChat Navigate to the chat screen.
 * @param viewModel The chat view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GroupDetailsScreen(
    onShowSnackbar: (String) -> Unit,
    groupId: String,
    onNavigateBack: () -> Unit,
    onNavigateToChat: (String) -> Unit,
    conversationListItems: List<ConversationListItem>,
    isLoading: Boolean,
    isGroupOperationInProgress: Boolean,
    isLeaveGroupSuccessful: Boolean,
    error: String?,
    currentUserId: String,
    onClearError: () -> Unit,
    onUpdateGroup: (groupId: String, name: String?, description: String?, pictureUrl: String?, privacyType: String?) -> Unit,
    onAddMembers: (groupId: String, userHandles: List<String>) -> Unit,
    onRemoveMembers: (groupId: String, userHandles: List<String>) -> Unit,
    onUpdateAdmins: (groupId: String, userHandles: List<String>) -> Unit,
    onLeaveGroup: (groupId: String) -> Unit,
    onResetOperationStates: () -> Unit
) {
    val snackbarHostState = remember { SnackbarHostState() }
    // Find the group conversation
    val groupConversation = conversationListItems.find {
        it.id == groupId && it.conversationType == ConversationType.GROUP
    }
    // UI state
    var showEditNameDialog by remember { mutableStateOf(false) }
    var showEditDescriptionDialog by remember { mutableStateOf(false) }
    var showAddMembersDialog by remember { mutableStateOf(false) }
    var showLeaveGroupDialog by remember { mutableStateOf(false) }
    var showRemoveMemberDialog by remember { mutableStateOf<String?>(null) }
    var showMakeAdminDialog by remember { mutableStateOf<String?>(null) }
    var showRemoveAdminDialog by remember { mutableStateOf<String?>(null) }
    var showOptionsMenu by remember { mutableStateOf(false) }
    // Check if current user is admin (placeholder - would need to be implemented in the backend)
    // For now, we'll assume the current user is an admin for demonstration purposes
    val isCurrentUserAdmin = true // This would need to be determined from the backend
    // Navigate back when group is left
    LaunchedEffect(isLeaveGroupSuccessful) {
        if (isLeaveGroupSuccessful) {
            onNavigateBack()
            onResetOperationStates()
        }
    }
    // Show error message
//    val scope = rememberCoroutineScope()
//    ErrorSnackbar(
//        errorMessage = error,
//        snackbarHostState = snackbarHostState,
//        scope = scope,
//        onDismiss = onClearError
//    )
    LaunchedEffect(error) {
        error?.let {
            onShowSnackbar(it)
            onClearError()
        }
    }

    // Edit name dialog
    if (showEditNameDialog) {
        EditTextDialog(
            title = stringResource(R.string.edit_group_name),
            initialValue = groupConversation?.name ?: "",
            onDismiss = { showEditNameDialog = false },
            onSave = { newName ->
                if (newName.isNotBlank()) {
                    onUpdateGroup(
                        groupId,
                        newName,
                        null,
                        null,
                        null
                    )
                    showEditNameDialog = false
                }
            }
        )
    }
    // Edit description dialog
    if (showEditDescriptionDialog) {
        EditTextDialog(
            title = stringResource(R.string.edit_group_description),
            initialValue = groupConversation?.let { getGroupDescription(it) } ?: "",
            onDismiss = { showEditDescriptionDialog = false },
            onSave = { newDescription ->
                onUpdateGroup(
                    groupId,
                    null,
                    newDescription,
                    null,
                    null
                )
                showEditDescriptionDialog = false
            },
            singleLine = false
        )
    }
    // Add members dialog
    if (showAddMembersDialog) {
        ContactSelectionDialog(
            onDismiss = { showAddMembersDialog = false },
            onContactsSelected = { selectedContacts ->
                if (selectedContacts.isNotEmpty()) {
                    onAddMembers(
                        groupId,
                        selectedContacts
                    )
                }
                showAddMembersDialog = false
            }
        )
    }
    // Leave group dialog
    if (showLeaveGroupDialog) {
        AlertDialog(
            onDismissRequest = { showLeaveGroupDialog = false },
            title = { Text(stringResource(R.string.leave_group)) },
            text = { Text(stringResource(R.string.are_you_sure_leave_group)) },
            confirmButton = {
                Button(
                    onClick = {
                        onLeaveGroup(groupId)
                        showLeaveGroupDialog = false
                    }
                ) {
                    Text(stringResource(R.string.leave))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLeaveGroupDialog = false }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    // Remove member dialog
    showRemoveMemberDialog?.let { userId ->
        AlertDialog(
            onDismissRequest = { showRemoveMemberDialog = null },
            title = { Text(stringResource(R.string.remove_member)) },
            text = { Text(stringResource(R.string.are_you_sure_remove_member)) },
            confirmButton = {
                Button(
                    onClick = {
                        onRemoveMembers(
                            groupId,
                            listOf(userId)
                        )
                        showRemoveMemberDialog = null
                    }
                ) {
                    Text(stringResource(R.string.remove))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showRemoveMemberDialog = null }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    // Make admin dialog
    showMakeAdminDialog?.let { userId ->
        AlertDialog(
            onDismissRequest = { showMakeAdminDialog = null },
            title = { Text(stringResource(R.string.make_admin)) },
            text = { Text(stringResource(R.string.make_this_member_admin)) },
            confirmButton = {
                Button(
                    onClick = {
                        // Get current admin IDs and add the new admin
                        val currentAdminIds = getGroupAdminIds(groupConversation)
                        val updatedAdminIds = currentAdminIds + userId
                        onUpdateAdmins(
                            groupId,
                            updatedAdminIds
                        )
                        showMakeAdminDialog = null
                    }
                ) {
                    Text(stringResource(R.string.make_admin_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showMakeAdminDialog = null }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    // Remove admin dialog
    showRemoveAdminDialog?.let { userId ->
        AlertDialog(
            onDismissRequest = { showRemoveAdminDialog = null },
            title = { Text(stringResource(R.string.remove_admin)) },
            text = { Text(stringResource(R.string.remove_admin_privileges)) },
            confirmButton = {
                Button(
                    onClick = {
                        // Get current admin IDs and remove the admin
                        val currentAdminIds = getGroupAdminIds(groupConversation)
                        val updatedAdminIds = currentAdminIds - userId
                        onUpdateAdmins(
                            groupId,
                            updatedAdminIds
                        )
                        showRemoveAdminDialog = null
                    }
                ) {
                    Text(stringResource(R.string.remove_admin_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showRemoveAdminDialog = null }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (groupConversation != null) {
            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                item {
                    // Group header
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Group avatar
                        Box(
                            modifier = Modifier
                                .size(100.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colorScheme.surfaceVariant)
                                .clickable(enabled = isCurrentUserAdmin) {
                                    // TODO: Implement avatar change
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Group,
                                contentDescription = stringResource(R.string.cancel),
                                modifier = Modifier.size(50.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            if (isCurrentUserAdmin) {
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.BottomEnd)
                                        .size(32.dp)
                                        .clip(CircleShape)
                                        .background(MaterialTheme.colorScheme.primary),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Camera,
                                        contentDescription = stringResource(R.string.change_avatar),
                                        modifier = Modifier.size(18.dp),
                                        tint = MaterialTheme.colorScheme.onPrimary
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                        // Group name
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = groupConversation.name,
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold
                            )
                            if (isCurrentUserAdmin) {
                                IconButton(onClick = { showEditNameDialog = true }) {
                                    Icon(
                                        imageVector = Icons.Default.Edit,
                                        contentDescription = stringResource(R.string.edit_name),
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                        // Privacy type
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(vertical = 4.dp)
                        ) {
                            val (icon, labelRes) = when (groupConversation.privacyType) {
                                PrivacyType.PUBLIC -> Icons.Default.Public to R.string.public_group
                                PrivacyType.PRIVATE -> Icons.Default.Group to R.string.private_group
                                PrivacyType.SECRET -> Icons.Default.Lock to R.string.secret_group
                                null -> Icons.Default.Group to R.string.private_group
                            }
                            Icon(
                                imageVector = icon,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = stringResource(labelRes),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // Member count
                        Text(
                            text = stringResource(R.string.members_label, groupConversation.participantCount),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        // Description
                        val description = getGroupDescription(groupConversation)
                        if (description.isNotBlank()) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.Top
                            ) {
                                Text(
                                    text = description,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )
                                if (isCurrentUserAdmin) {
                                    IconButton(onClick = { showEditDescriptionDialog = true }) {
                                        Icon(
                                            imageVector = Icons.Default.Edit,
                                            contentDescription = stringResource(R.string.edit_description),
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }
                            }
                        } else if (isCurrentUserAdmin) {
                            TextButton(onClick = { showEditDescriptionDialog = true }) {
                                Text(stringResource(R.string.add_description))
                            }
                        }
                    }
                    HorizontalDivider(thickness = 2.dp, color = MaterialTheme.colorScheme.primary)
                    // Members section header
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.members_section),
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.weight(1f)
                        )
                        if (isCurrentUserAdmin) {
                            IconButton(onClick = { showAddMembersDialog = true }) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = stringResource(R.string.add_members)
                                )
                            }
                        }
                    }
                }
                // Members list - for demonstration, we'll use a placeholder list
                // In a real app, you would fetch the actual members from the backend
                val memberIds = getMemberIds(groupConversation)
                val adminIds = getGroupAdminIds(groupConversation)
                items(memberIds) { memberId ->
                    val isAdmin = adminIds.contains(memberId)
                    val isCurrentUser = memberId == currentUserId
                    GroupMemberItem(
                        userId = memberId,
                        isAdmin = isAdmin,
                        isCurrentUser = isCurrentUser,
                        onRemoveMember = if (isCurrentUserAdmin && !isCurrentUser) {
                            { showRemoveMemberDialog = memberId }
                        } else null,
                        onMakeAdmin = if (isCurrentUserAdmin && !isAdmin && !isCurrentUser) {
                            { showMakeAdminDialog = memberId }
                        } else null,
                        onRemoveAdmin = if (isCurrentUserAdmin && isAdmin && !isCurrentUser) {
                            { showRemoveAdminDialog = memberId }
                        } else null
                    )
                }
            }
        } else {
            // Loading or error state
            if (isLoading || isGroupOperationInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                Text(
                    text = stringResource(R.string.group_not_found),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

// Other components and utility functions remain unchanged
@Composable
fun GroupMemberItem(
    userId: String,
    isAdmin: Boolean,
    isCurrentUser: Boolean,
    onRemoveMember: (() -> Unit)? = null,
    onMakeAdmin: (() -> Unit)? = null,
    onRemoveAdmin: (() -> Unit)? = null
) {
    var showOptionsMenu by remember { mutableStateOf(false) }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { showOptionsMenu = onRemoveMember != null || onMakeAdmin != null || onRemoveAdmin != null }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Avatar or placeholder
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.surfaceVariant),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = stringResource(R.string.member),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Spacer(modifier = Modifier.width(16.dp))
        // Member info
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = userId + if (isCurrentUser) " ${stringResource(R.string.you_label)}" else "",
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                if (isAdmin) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = stringResource(R.string.admin_label),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
        // Options menu
        if (onRemoveMember != null || onMakeAdmin != null || onRemoveAdmin != null) {
            Box {
                IconButton(onClick = { showOptionsMenu = true }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = stringResource(R.string.member_options)
                    )
                }
                DropdownMenu(
                    expanded = showOptionsMenu,
                    onDismissRequest = { showOptionsMenu = false }
                ) {
                    if (onMakeAdmin != null) {
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.make_admin_menu)) },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null
                                )
                            },
                            onClick = {
                                onMakeAdmin()
                                showOptionsMenu = false
                            }
                        )
                    }
                    if (onRemoveAdmin != null) {
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.remove_admin_menu)) },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null
                                )
                            },
                            onClick = {
                                onRemoveAdmin()
                                showOptionsMenu = false
                            }
                        )
                    }
                    if (onRemoveMember != null) {
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.remove_from_group)) },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = null
                                )
                            },
                            onClick = {
                                onRemoveMember()
                                showOptionsMenu = false
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun EditTextDialog(
    title: String,
    initialValue: String,
    onDismiss: () -> Unit,
    onSave: (String) -> Unit,
    singleLine: Boolean = true
) {
    var text by remember { mutableStateOf(initialValue) }

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(16.dp))

                OutlinedTextField(
                    value = text,
                    onValueChange = { text = it },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = singleLine,
                    maxLines = if (singleLine) 1 else 5
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text(stringResource(R.string.cancel))
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(onClick = { onSave(text) }) {
                        Text(stringResource(R.string.save))
                    }
                }
            }
        }
    }
}

fun getGroupDescription(conversation: ConversationListItem): String {
    // In a real app, this would come from the conversation object
    // For now, we'll return a placeholder
    return "This is a group for discussing various topics."
}

fun getMemberIds(conversation: ConversationListItem): List<String> {
    // In a real app, this would come from the conversation object or a separate API call
    // For now, we'll return a placeholder list
    return listOf("user1", "user2", "user3", "user4")
}

fun getGroupAdminIds(conversation: ConversationListItem?): List<String> {
    // In a real app, this would come from the conversation object or a separate API call
    // For now, we'll return a placeholder list
    return listOf("user1")
}
