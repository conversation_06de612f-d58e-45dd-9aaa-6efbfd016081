package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Display an error message in a snackbar.
 *
 * @param errorMessage The error message to display.
 * @param snackbarHostState The snackbar host state.
 * @param onDismiss Optional callback when the snackbar is dismissed.
 * @param actionLabel Optional label for the action button.
 * @param onAction Optional callback when the action button is clicked.
 * @param duration The duration to show the snackbar.
 */
@Composable
fun ErrorSnackbar(
    errorMessage: String?,
    snackbarHostState: SnackbarHostState,
    scope: CoroutineScope,
    onDismiss: (() -> Unit)? = null,
    actionLabel: String? = null,
    onAction: (() -> Unit)? = null,
    duration: SnackbarDuration = SnackbarDuration.Short
) {
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            val result = snackbarHostState.showSnackbar(
                message = message,
                actionLabel = actionLabel,
                duration = duration
            )
            
            when (result) {
                SnackbarResult.Dismissed -> onDismiss?.invoke()
                SnackbarResult.ActionPerformed -> onAction?.invoke()
            }
        }
    }
}

/**
 * Display an error message in a card.
 *
 * @param errorMessage The error message to display.
 * @param onRetry Optional callback when the retry button is clicked.
 * @param icon Optional icon to display next to the error message.
 * @param modifier Modifier for the card.
 */
@Composable
fun ErrorCard(
    errorMessage: String,
    onRetry: (() -> Unit)? = null,
    icon: ImageVector = Icons.Default.Error,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = "Error",
                    tint = MaterialTheme.colorScheme.error
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = errorMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f)
                )
            }
            
            if (onRetry != null) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = onRetry,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Retry"
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text("Retry")
                }
            }
        }
    }
}

/**
 * Display an inline error message.
 *
 * @param errorMessage The error message to display.
 * @param modifier Modifier for the text.
 */
@Composable
fun ErrorText(
    errorMessage: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = errorMessage,
        style = MaterialTheme.typography.bodySmall,
        color = MaterialTheme.colorScheme.error,
        modifier = modifier
    )
}

/**
 * Display a full-screen error message.
 *
 * @param errorMessage The error message to display.
 * @param onRetry Optional callback when the retry button is clicked.
 * @param modifier Modifier for the container.
 */
@Composable
fun FullScreenError(
    errorMessage: String,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "Error",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            if (onRetry != null) {
                Button(onClick = onRetry) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Retry"
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text("Retry")
                }
            }
        }
    }
}
