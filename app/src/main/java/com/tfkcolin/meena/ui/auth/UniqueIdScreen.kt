package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

@Composable
fun UniqueIdScreen(
    userHandle: String?,
    confirmationChecked: Boolean,
    onConfirmationChange: (Boolean) -> Unit,
    onNavigateBack: () -> Unit,
    onContinue: () -> Unit,
    modifier: Modifier = Modifier
) {
    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp) // Adjusted vertical padding
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.your_unique_meena_id_headline),
                style = MaterialTheme.typography.headlineMedium.copy( // Adjusted headline style
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = stringResource(R.string.unique_meena_id_instruction),
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // MEENA ID display
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                color = MaterialTheme.colorScheme.surfaceVariant, // Using surfaceVariant for distinct background
                shape = MaterialTheme.shapes.medium
            ) {
                Text(
                    text = userHandle ?: stringResource(R.string.loading_text),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant // Text color on surfaceVariant
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(24.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Warning text
            Text(
                text = stringResource(R.string.meena_id_important_warning),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.error // Keep error color for warning
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            // Confirmation checkbox
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 32.dp)
            ) {
                Checkbox(
                    checked = confirmationChecked,
                    onCheckedChange = onConfirmationChange,
                    colors = CheckboxDefaults.colors(
                        checkedColor = MaterialTheme.colorScheme.primary, // Consistent checkbox color
                        uncheckedColor = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.6f) // Improved unchecked color
                    )
                )
                Text(
                    text = stringResource(R.string.meena_id_confirmation_checkbox),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary // Consistent text color
                    )
                )
            }

            // Continue button
            MeenaPrimaryButton(
                text = stringResource(R.string.continue_button),
                onClick = { onContinue() },
                enabled = confirmationChecked,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

// Ensure you have MeenaTheme for preview if needed
@Preview(showBackground = true)
@Composable
fun PreviewUniqueIdScreen() {
    MeenaTheme {
        UniqueIdScreen(
            userHandle = "TEST1234",
            confirmationChecked = true,
            onConfirmationChange = {},
            onNavigateBack = {},
            onContinue = {},
        )
    }
}