package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPasswordTextField
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Screen for setting up and confirming a PIN.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param pin The current value of the PIN input.
 * @param confirmPin The current value of the confirm PIN input.
 * @param errorMessage An error message to display, if any.
 * @param isLoading Indicates if an operation is in progress.
 * @param onPinChange Callback for when the PIN changes.
 * @param onConfirmPinChange Callback for when the confirm PIN changes.
 * @param onCompleteRegistration Callback to complete the registration with the PIN.
 * @param onClearError Callback to clear the current error message.
 */
@Composable
fun SetupPinScreen(
    onNavigateBack: () -> Unit,
    pin: String,
    confirmPin: String,
    errorMessage: String?,
    isLoading: Boolean,
    onPinChange: (String) -> Unit,
    onConfirmPinChange: (String) -> Unit,
    onCompleteRegistration: (String) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp), // Adjusted vertical padding
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.set_your_pin_headline),
                style = MaterialTheme.typography.headlineMedium.copy( // Consistent headline style
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = stringResource(R.string.set_pin_description),
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // PIN input field
            MeenaPasswordTextField(
                value = pin,
                tint = Color.White, // Using primary color for text field
                onValueChange = {
                    if (it.length <= 6 && it.all { char -> char.isDigit() }) {
                        onPinChange(it)
                    }
                },
                label = stringResource(R.string.pin_label),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.NumberPassword,
                    imeAction = ImeAction.Next
                ),
                keyboardActions = KeyboardActions(
                    onNext = { focusManager.moveFocus(FocusDirection.Down) }
                ),
                isError = pin.isNotEmpty() && pin.length != 6,
                errorText = if (pin.isNotEmpty() && pin.length != 6) stringResource(R.string.pin_length_error) else null,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Confirm PIN input field
            MeenaPasswordTextField(
                value = confirmPin,
                onValueChange = {
                    if (it.length <= 6 && it.all { char -> char.isDigit() }) {
                        onConfirmPinChange(it)
                    }
                },
                label = stringResource(R.string.confirm_pin_label),
                tint = Color.White,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.NumberPassword,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (pin.isNotEmpty() && confirmPin.isNotEmpty() && pin == confirmPin) {
                            onCompleteRegistration(pin)
                        }
                    }
                ),
                isError = confirmPin.isNotEmpty() && pin != confirmPin,
                errorText = if (confirmPin.isNotEmpty() && pin != confirmPin) stringResource(R.string.pins_do_not_match_error) else null,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Error message
            if (errorMessage != null) {
                Text(
                    text = errorMessage,
                    color = MaterialTheme.colorScheme.error, // Keep error color for warning
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center, // Center align error message for consistency
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }

            // Complete registration button
            MeenaPrimaryButton(
                text = stringResource(R.string.complete_registration_button),
                onClick = {
                    focusManager.clearFocus()
                    if (pin.isNotEmpty() && confirmPin.isNotEmpty() && pin == confirmPin) {
                        onCompleteRegistration(pin)
                    }
                },
                enabled = pin.isNotEmpty() && confirmPin.isNotEmpty() && pin == confirmPin && !isLoading,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview()
@Composable
fun SetupPinScreenPreview() {
    MeenaTheme {
        SetupPinScreen(
            onNavigateBack = {},
            pin = "",
            confirmPin = "",
            errorMessage = null,
            isLoading = false,
            onPinChange = {},
            onConfirmPinChange = {},
            onCompleteRegistration = {},
        )
    }
}