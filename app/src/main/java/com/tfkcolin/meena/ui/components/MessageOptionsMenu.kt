package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Forward
import androidx.compose.material.icons.automirrored.filled.Reply
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.EmojiEmotions
import androidx.compose.material.icons.filled.Forward
import androidx.compose.material.icons.filled.Reply
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.Message

/**
 * Message options menu component.
 *
 * @param message The message.
 * @param isFromCurrentUser Whether the message is from the current user.
 * @param isExpanded Whether the menu is expanded.
 * @param onDismiss The callback for when the menu is dismissed.
 * @param onCopyClick The callback for when the copy option is clicked.
 * @param onEditClick The callback for when the edit option is clicked.
 * @param onDeleteForSelfClick The callback for when the delete for self option is clicked.
 * @param onDeleteForEveryoneClick The callback for when the delete for everyone option is clicked.
 * @param onReplyClick The callback for when the reply option is clicked.
 * @param onForwardClick The callback for when the forward option is clicked.
 * @param onReactClick The callback for when the react option is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun MessageOptionsMenu(
    modifier: Modifier = Modifier,
    message: Message,
    isFromCurrentUser: Boolean,
    isExpanded: Boolean,
    onDismiss: () -> Unit,
    onCopyClick: () -> Unit,
    onEditClick: () -> Unit,
    onDeleteForSelfClick: () -> Unit,
    onDeleteForEveryoneClick: () -> Unit,
    onReplyClick: () -> Unit,
    onForwardClick: () -> Unit,
    onReactClick: () -> Unit = {},
) {
    val clipboardManager = LocalClipboardManager.current
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        DropdownMenu(
            expanded = isExpanded,
            onDismissRequest = onDismiss
        ) {
            // Copy option
            DropdownMenuItem(
                text = { Text("Copy") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.ContentCopy,
                        contentDescription = "Copy"
                    )
                },
                onClick = {
                    // Copy the message content to clipboard
                    clipboardManager.setText(AnnotatedString(message.content))
                    onCopyClick()
                    onDismiss()
                }
            )

            // React option
            DropdownMenuItem(
                text = { Text("React") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.EmojiEmotions,
                        contentDescription = "React"
                    )
                },
                onClick = {
                    onReactClick()
                    onDismiss()
                }
            )

            // Reply option
            DropdownMenuItem(
                text = { Text("Reply") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Reply,
                        contentDescription = "Reply"
                    )
                },
                onClick = {
                    onReplyClick()
                    onDismiss()
                }
            )

            // Forward option
            DropdownMenuItem(
                text = { Text("Forward") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Forward,
                        contentDescription = "Forward"
                    )
                },
                onClick = {
                    onForwardClick()
                    onDismiss()
                }
            )

            // Edit option (only for current user's messages)
            if (isFromCurrentUser) {
                DropdownMenuItem(
                    text = { Text("Edit") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit"
                        )
                    },
                    onClick = {
                        onEditClick()
                        onDismiss()
                    }
                )
            }

            HorizontalDivider(thickness = 1.dp, color = MaterialTheme.colorScheme.primary)

            // Delete for self option
            DropdownMenuItem(
                text = { Text("Delete for me") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete for me"
                    )
                },
                onClick = {
                    onDeleteForSelfClick()
                    onDismiss()
                }
            )

            // Delete for everyone option (only for current user's messages)
            if (isFromCurrentUser) {
                DropdownMenuItem(
                    text = { Text("Delete for everyone") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete for everyone",
                            tint = MaterialTheme.colorScheme.error
                        )
                    },
                    onClick = {
                        onDeleteForEveryoneClick()
                        onDismiss()
                    }
                )
            }
        }
    }
}


