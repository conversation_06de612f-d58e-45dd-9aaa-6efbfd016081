package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.UploadProgress
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel

/**
 * Enhanced message input component with support for multiple attachments.
 *
 * @param value The current value of the input.
 * @param onValueChange The callback for when the value changes.
 * @param onSendClick The callback for when the send button is clicked.
 * @param isLoading Whether the message is being sent.
 * @param onAttachmentClick The callback for when the attachment button is clicked.
 * @param selectedAttachments The currently selected attachments.
 * @param onRemoveAttachment The callback for when an attachment is removed.
 * @param onAttachmentPreviewClick The callback for when an attachment preview is clicked.
 * @param modifier The modifier for the component.
 * @param viewModel The media view model.
 */
@Composable
fun EnhancedMessageInput(
    value: String,
    onValueChange: (String) -> Unit,
    onSendClick: () -> Unit,
    isLoading: Boolean,
    onAttachmentClick: () -> Unit,
    selectedAttachments: List<MediaAttachment> = emptyList(),
    onRemoveAttachment: (MediaAttachment) -> Unit,
    onAttachmentPreviewClick: (MediaAttachment) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: MediaViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Display selected attachments if any
        if (selectedAttachments.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .horizontalScroll(rememberScrollState()),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                selectedAttachments.forEach { attachment ->
                    AttachmentPreview(
                        attachment = attachment,
                        onPreviewClick = { onAttachmentPreviewClick(attachment) },
                        onRemoveClick = { onRemoveAttachment(attachment) },
                        viewModel = viewModel
                    )
                }
            }
        }
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Attachment button
            IconButton(
                onClick = onAttachmentClick
            ) {
                Icon(
                    imageVector = Icons.Default.AttachFile,
                    contentDescription = "Attach file",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            
            // Text input
            OutlinedTextField(
                value = value,
                onValueChange = onValueChange,
                modifier = Modifier.weight(1f),
                placeholder = { Text("Type a message") },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Send
                ),
                keyboardActions = KeyboardActions(
                    onSend = {
                        if ((value.isNotBlank() || selectedAttachments.isNotEmpty()) && !isLoading) {
                            onSendClick()
                        }
                    }
                ),
                maxLines = 5
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // Send button
            IconButton(
                onClick = onSendClick,
                enabled = (value.isNotBlank() || selectedAttachments.isNotEmpty()) && !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Send",
                        tint = if (value.isNotBlank() || selectedAttachments.isNotEmpty())
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                    )
                }
            }
        }
    }
}

/**
 * Attachment preview component.
 *
 * @param attachment The attachment to preview.
 * @param onPreviewClick The callback for when the preview is clicked.
 * @param onRemoveClick The callback for when the remove button is clicked.
 * @param viewModel The media view model.
 */
@Composable
private fun AttachmentPreview(
    attachment: MediaAttachment,
    onPreviewClick: () -> Unit,
    onRemoveClick: () -> Unit,
    viewModel: MediaViewModel
) {
    val context = LocalContext.current
    
    // Get upload progress
    val uploadProgress by viewModel.getUploadProgress(attachment.id)
        .collectAsState(initial = null)
    
    Box(
        modifier = Modifier
            .size(80.dp)
            .clip(RoundedCornerShape(8.dp))
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.outline,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(onClick = onPreviewClick)
    ) {
        when (attachment.type) {
            "image" -> {
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(attachment.url)
                        .crossfade(true)
                        .build(),
                    contentDescription = attachment.name ?: "Image",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            "video" -> {
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(attachment.thumbnailUrl ?: attachment.url)
                        .crossfade(true)
                        .build(),
                    contentDescription = attachment.name ?: "Video",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // Play icon overlay
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.Black.copy(alpha = 0.3f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Play",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
            "audio" -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.primaryContainer),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Audio",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
            "document" -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.secondaryContainer),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Document",
                        tint = MaterialTheme.colorScheme.onSecondaryContainer,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
            "location" -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.tertiaryContainer),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Location",
                        tint = MaterialTheme.colorScheme.onTertiaryContainer,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
        
        // Remove button
        IconButton(
            onClick = onRemoveClick,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .size(24.dp)
                .clip(CircleShape)
                .background(Color.Black.copy(alpha = 0.5f))
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Remove",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
        
        // Show upload progress if uploading
        if (uploadProgress is UploadProgress.Uploading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .height(4.dp)
                    .background(Color.Black.copy(alpha = 0.5f))
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth((uploadProgress as UploadProgress.Uploading).progress / 100f)
                        .height(4.dp)
                        .background(MaterialTheme.colorScheme.primary)
                )
            }
        }
    }
}
