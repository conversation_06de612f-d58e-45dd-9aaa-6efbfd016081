package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.ConversationType

/**
 * Conversation type tabs component.
 *
 * @param selectedTabIndex The index of the selected tab.
 * @param onTabSelected The callback for when a tab is selected.
 * @param showOneToOne Whether to show the one-to-one tab.
 * @param showGroups Whether to show the groups tab.
 * @param showChannels Whether to show the channels tab.
 * @param oneToOneCount The count of one-to-one conversations.
 * @param groupCount The count of group conversations.
 * @param channelCount The count of channel conversations.
 * @param modifier The modifier for the component.
 */
@Composable
fun ConversationTypeTabs(
    modifier: Modifier = Modifier,
    selectedTabIndex: Int,
    onTabSelected: (Int, ConversationType) -> Unit,
    showOneToOne: Boolean = true,
    showGroups: Boolean = true,
    showChannels: Boolean = true,
    oneToOneCount: Int = 0,
    groupCount: Int = 0,
    channelCount: Int = 0,
) {
    val tabs = mutableListOf<Pair<String, ConversationType>>()
    
    if (showOneToOne) {
        tabs.add("Chats ($oneToOneCount)" to ConversationType.ONE_TO_ONE)
    }
    
    if (showGroups) {
        tabs.add("Groups ($groupCount)" to ConversationType.GROUP)
    }
    
    if (showChannels) {
        tabs.add("Channels ($channelCount)" to ConversationType.CHANNEL)
    }
    
    Column(modifier = modifier.fillMaxWidth()) {
        TabRow(
            modifier = Modifier.fillMaxWidth(),
            selectedTabIndex = selectedTabIndex,
            indicator = { tabPositions ->
                if (selectedTabIndex < tabPositions.size) {
                    TabRowDefaults.SecondaryIndicator(
                        modifier = Modifier
                            .tabIndicatorOffset(tabPositions[selectedTabIndex])
                            .height(4.dp) // Adjust height for a thinner line
                            .padding(horizontal = 24.dp) // Adjust horizontal padding to make it shorter
                            .clip(RoundedCornerShape(topStart = 1.dp, topEnd = 1.dp)), // Slightly rounded corners
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        ) {
            tabs.forEachIndexed { index, (title, type) ->
                Tab(
                    selected = selectedTabIndex == index,
                    onClick = { onTabSelected(index, type) },
                    text = {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = if (selectedTabIndex == index) FontWeight.Bold else FontWeight.Normal
                            )
                        )
                    }
                )
            }
        }
    }
}
