package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Archive
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.NotificationsOff
import androidx.compose.material.icons.filled.NotificationsActive
import androidx.compose.material.icons.filled.PushPin
import androidx.compose.material.icons.outlined.PushPin
import androidx.compose.material.icons.filled.Unarchive
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.tfkcolin.meena.ui.models.ConversationListItem

/**
 * Chat options menu component.
 *
 * @param conversation The conversation.
 * @param isExpanded Whether the menu is expanded.
 * @param onDismiss The callback for when the menu is dismissed.
 * @param onArchiveClick The callback for when the archive option is clicked.
 * @param onMuteClick The callback for when the mute option is clicked.
 * @param onPinClick The callback for when the pin option is clicked.
 * @param onDeleteClick The callback for when the delete option is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun ChatOptionsMenu(
    conversation: ConversationListItem,
    isExpanded: Boolean,
    onDismiss: () -> Unit,
    onArchiveClick: () -> Unit,
    onMuteClick: () -> Unit,
    onPinClick: () -> Unit,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        DropdownMenu(
            expanded = isExpanded,
            onDismissRequest = onDismiss
        ) {
            // Archive/Unarchive option
            DropdownMenuItem(
                text = {
                    Text(if (conversation.isArchived) "Unarchive" else "Archive")
                },
                leadingIcon = {
                    Icon(
                        imageVector = if (conversation.isArchived) Icons.Default.Unarchive else Icons.Default.Archive,
                        contentDescription = if (conversation.isArchived) "Unarchive" else "Archive"
                    )
                },
                onClick = {
                    onArchiveClick()
                    onDismiss()
                }
            )

            // Mute/Unmute option
            DropdownMenuItem(
                text = {
                    Text(if (conversation.isMuted) "Unmute" else "Mute")
                },
                leadingIcon = {
                    Icon(
                        imageVector = if (conversation.isMuted) Icons.Default.NotificationsActive else Icons.Default.NotificationsOff,
                        contentDescription = if (conversation.isMuted) "Unmute" else "Mute"
                    )
                },
                onClick = {
                    onMuteClick()
                    onDismiss()
                }
            )

            // Pin/Unpin option
            DropdownMenuItem(
                text = {
                    Text(if (conversation.isPinned) "Unpin" else "Pin")
                },
                leadingIcon = {
                    Icon(
                        imageVector = if (conversation.isPinned) Icons.Outlined.PushPin else Icons.Default.PushPin,
                        contentDescription = if (conversation.isPinned) "Unpin" else "Pin"
                    )
                },
                onClick = {
                    onPinClick()
                    onDismiss()
                }
            )

            // Delete option
            DropdownMenuItem(
                text = { Text("Delete") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete",
                        tint = MaterialTheme.colorScheme.error
                    )
                },
                onClick = {
                    onDeleteClick()
                    onDismiss()
                }
            )
        }
    }
}
