package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Group
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.contacts.components.ContactGroupItem

/**
 * Contact groups screen.
 *
 * @param onNavigateToGroupDetail Navigate to the group detail screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun ContactGroupsScreen(
    onShowSnackbar: (String) -> Unit,
    onNavigateToGroupDetail: (String) -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val contactGroups by viewModel.contactGroups.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    var showCreateGroupDialog by remember { mutableStateOf(false) }
    var newGroupName by remember { mutableStateOf("") }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            onShowSnackbar(it)
            viewModel.clearError()
        }
    }

    val groupCreatedSuccessfully = stringResource(R.string.group_created_successfully)

    // Show operation success/error messages
    LaunchedEffect(contactsState.createGroupOperation.isSuccessful) {
        if (contactsState.createGroupOperation.isSuccessful) {
            onShowSnackbar(groupCreatedSuccessfully)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.createGroupOperation.error) {
        contactsState.createGroupOperation.error?.let {
            onShowSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    // Create group dialog
    if (showCreateGroupDialog) {
        AlertDialog(
            onDismissRequest = {
                showCreateGroupDialog = false
                newGroupName = ""
            },
            title = { Text(stringResource(R.string.create_group)) },
            text = {
                Column {
                    Text(stringResource(R.string.enter_name_for_new_group))
                    Spacer(modifier = Modifier.height(8.dp))
                    TextField(
                        value = newGroupName,
                        onValueChange = { newGroupName = it },
                        label = { Text(stringResource(R.string.group_name_label)) },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (newGroupName.isNotBlank()) {
                            viewModel.createContactGroup(newGroupName)
                            showCreateGroupDialog = false
                            newGroupName = ""
                        }
                    },
                    enabled = newGroupName.isNotBlank()
                ) {
                    Text(stringResource(R.string.create_button))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showCreateGroupDialog = false
                        newGroupName = ""
                    }
                ) {
                    Text(stringResource(R.string.cancel_button))
                }
            }
        )
    }

    // UI components are now managed centrally by NavGraph

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (contactGroups.isEmpty() && !contactsState.properties.isLoading) {
            // Empty state
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Group,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.no_contact_groups),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = stringResource(R.string.create_your_first_group),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
            }
        } else {
            // Group list
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(contactGroups) { group ->
                    ContactGroupItem(
                        group = group,
                        onClick = { onNavigateToGroupDetail(group.id) }
                    )
                }
            }
        }

        if (contactsState.properties.isLoading || contactsState.createGroupOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}