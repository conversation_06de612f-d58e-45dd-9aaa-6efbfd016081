package com.tfkcolin.meena.ui.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.contacts.ContactsState

/**
 * New chat screen.
 *
 * @param onNavigateBack Navigate back to the chat list screen.
 * @param onNavigateToChat Navigate to the chat screen.
 * @param contactsViewModel The contacts view model.
 * @param chatViewModel The chat view model.
 */
@Composable
fun NewChatScreen(
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    onNavigateToChat: (String, String) -> Unit,
    contactsState: ContactsState,
    contacts: List<Contact>,
    onClearContactsError: () -> Unit,
    chatUiState: ChatUiState,
    onGetOrCreateChat: (String) -> Unit,
    onResetChatOperationStates: () -> Unit,
    onClearChatError: () -> Unit
) {
    val focusManager = LocalFocusManager.current

    var searchQuery by remember { mutableStateOf("") }
    var selectedRecipientId by remember { mutableStateOf<String?>(null) }

    // Filter contacts based on search query
    val filteredContacts = if (searchQuery.isBlank()) {
        contacts
    } else {
        contacts.filter {
            it.displayName?.contains(searchQuery, ignoreCase = true) == true ||
                    it.contactId.contains(searchQuery, ignoreCase = true)
        }
    }

    // Show error message
    LaunchedEffect(contactsState.error, chatUiState.error) {
        val error = contactsState.error ?: chatUiState.error
        error?.let {
            onShowSnackbar(it)
            onClearContactsError()
            onClearChatError()
        }
    }

    // Navigate to chat when a new chat is created
    LaunchedEffect(chatUiState.conversationListState.createChatOperation.isSuccessful) {
        if (chatUiState.conversationListState.createChatOperation.isSuccessful) {
            chatUiState.newChatId?.let { chatId ->
                onResetChatOperationStates()
                selectedRecipientId?.let { recipientId ->
                    onNavigateToChat(chatId, recipientId)
                }
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Search field
            OutlinedTextField(
                value = searchQuery,
                onValueChange = { searchQuery = it },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text(stringResource(id = R.string.search_contacts_placeholder)) },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = stringResource(id = R.string.search_content_description)
                    )
                },
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Search
                ),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        focusManager.clearFocus()
                    }
                ),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Contact list
            if (filteredContacts.isEmpty() && !contactsState.isLoading) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = null, // Content description for decorative icons can be null
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = stringResource(id = R.string.no_contacts_found_headline),
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    contentPadding = PaddingValues(vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredContacts) { contact ->
                        ContactItem(
                            contact = contact,
                            onClick = {
                                selectedRecipientId = contact.contactId
                                onGetOrCreateChat(contact.contactId)
                            }
                        )
                    }
                }
            }
        }

        if (contactsState.properties.isLoading ||
            chatUiState.properties.isLoading ||
            chatUiState.conversationListState.createChatOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

/**
 * Contact item component.
 *
 * @param contact The contact to display.
 * @param onClick The click handler.
 * @param modifier The modifier for the component.
 */
@Composable
fun ContactItem(
    contact: Contact,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Profile picture or placeholder
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        shape = CircleShape
                    )
            )

            Spacer(modifier = Modifier.width(16.dp))

            // Contact info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = contact.displayName ?: stringResource(id = R.string.contact_item_default_user_name),
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = stringResource(id = R.string.contact_item_id_prefix, contact.contactId),
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun NewChatScreenPreview() {
    MaterialTheme { // Apply your app's theme for a more accurate preview
        NewChatScreen(
            onNavigateBack = {},
            onNavigateToChat = { _, _ -> },
            contactsState = ContactsState(),
            contacts = emptyList(),
            onClearContactsError = {},
            chatUiState = ChatUiState(),
            onGetOrCreateChat = {},
            onResetChatOperationStates = {},
            onClearChatError = {},
            onShowSnackbar = {}
        )
    }
}
