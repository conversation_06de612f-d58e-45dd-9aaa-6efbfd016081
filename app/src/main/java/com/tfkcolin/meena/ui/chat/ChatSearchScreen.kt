package com.tfkcolin.meena.ui.chat

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.SearchBar
import com.tfkcolin.meena.ui.components.SearchResultItem

/**
 * Chat search screen for searching messages within a specific chat.
 *
 * @param chatId The chat ID.
 * @param recipientName The recipient name.
 * @param onNavigateBack Navigate back to the chat screen.
 * @param onMessageClick Navigate to the message in the chat.
 * @param chatUiState The chat UI state.
 * @param onSetCurrentChat Callback to set the current chat.
 * @param onClearChatError Callback to clear chat-related errors.
 * @param onClearSearch Callback to clear search results and query.
 * @param onSearchMessagesInCurrentChat Callback to trigger message search within the current chat.
 */
@Composable
fun ChatSearchScreen(
    onShowSnackbar: (String) -> Unit,
    chatId: String,
    recipientName: String,
    onNavigateBack: () -> Unit,
    onMessageClick: (String) -> Unit,
    chatUiState: ChatUiState,
    onSetCurrentChat: (String) -> Unit,
    onClearChatError: () -> Unit,
    onClearSearch: () -> Unit,
    onSearchMessagesInCurrentChat: (String) -> Unit
) {
    var searchQuery by remember { mutableStateOf("") }

    // Set current chat
    LaunchedEffect(chatId) {
        onSetCurrentChat(chatId)
    }

    // Show error message
    LaunchedEffect(chatUiState.error) {
        chatUiState.error?.let {
            onShowSnackbar(it)
            onClearChatError()
        }
    }

    // Clear search when leaving the screen
    LaunchedEffect(Unit) {
        onClearSearch()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // Search bar
        SearchBar(
            query = searchQuery,
            onQueryChange = {
                searchQuery = it
                if (it.length >= 3) {
                    onSearchMessagesInCurrentChat(it)
                } else if (it.isEmpty()) {
                    onClearSearch()
                }
            },
            onSearch = {
                if (searchQuery.isNotBlank()) {
                    onSearchMessagesInCurrentChat(searchQuery)
                }
            },
            onClear = {
                searchQuery = ""
                onClearSearch()
            },
            placeholder = stringResource(R.string.search_in_this_chat_placeholder) // Using string resource
        )

        // Search results
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            if (chatUiState.messageState.searchOperation.isInProgress) {
                // Loading indicator
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (chatUiState.searchResults.isEmpty() && chatUiState.searchQuery.isNotBlank()) {
                // No results
                Column(
                    modifier = Modifier.align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = stringResource(R.string.no_results_found_headline), // Using string resource
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.try_different_search_term_prompt), // Using string resource
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                }
            } else if (chatUiState.searchQuery.isBlank()) {
                // Initial state
                Column(
                    modifier = Modifier.align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = stringResource(R.string.search_in_chat_title), // Reusing string resource
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.enter_search_term_to_find_messages), // Using string resource
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // Search results
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(vertical = 8.dp),
                    verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(8.dp)
                ) {
                    items(chatUiState.searchResults) { message ->
                        SearchResultItem(
                            message = message,
                            chatName = recipientName,
                            onClick = {
                                onMessageClick(message.id)
                            },
                            highlightQuery = chatUiState.searchQuery
                        )
                    }
                }
            }
        }
    }
}