package com.tfkcolin.meena.ui.stories.models

import androidx.compose.ui.geometry.Offset
import java.util.Date

/**
 * Represents a story with multiple segments.
 */
data class Story(
    val id: String = "",
    val authorName: String = "",
    val authorAvatarUrl: String = "",
    val authorId: String = "",
    val timePosted: Date = Date(),
    val segments: List<StorySegment> = listOf(),
    val isViewed: Boolean = false
)

/**
 * Represents a segment of a story (image or video).
 */
data class StorySegment(
    val id: String = "",
    val type: SegmentType = SegmentType.IMAGE,
    val mediaUrl: String = "",
    val duration: Int = 5, // seconds
    val overlayItems: List<OverlayItem> = emptyList()
)

/**
 * Types of story segments.
 */
enum class SegmentType {
    IMAGE, VIDEO
}

/**
 * Represents an overlay item on a story segment.
 */
data class OverlayItem(
    val id: String = "",
    val type: OverlayType = OverlayType.TEXT,
    val content: String = "",
    val position: Offset = Offset.Zero
)

/**
 * Types of overlay items.
 */
enum class OverlayType {
    TEXT, STICKER
}

/**
 * Represents a story item in the stories list.
 */
data class StoryItem(
    val id: String = "",
    val userName: String = "",
    val userAvatarUrl: String = "",
    val storyImageUrl: String = "",
    val isViewed: Boolean = false,
    val hasMultipleStories: Boolean = false,
    val isLive: Boolean = false
)
