package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * Combined UI state for chat operations.
 * This class is a facade that combines states from multiple ViewModels.
 */
data class ChatUiState(
    // Base state properties
    val properties: UiStateProperties = UiStateProperties(),

    // Conversation list state
    val conversationListState: ConversationListUiState = ConversationListUiState(),

    // Message state
    val messageState: MessageUiState = MessageUiState(),

    // Group chat state
    val groupChatState: GroupChatState = GroupChatState(),

    // Typing indicators
    val typingUsers: Map<String, Boolean> = emptyMap()
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading ||
                                           conversationListState.isLoading ||
                                           messageState.isLoading ||
                                           groupChatState.isLoading
    override val error: String? get() = properties.error ?:
                                       conversationListState.error ?:
                                       messageState.error ?:
                                       groupChatState.error

    // Convenience properties for backward compatibility
    val totalChatCount: Int get() = conversationListState.totalChatCount
    val totalMessageCount: Int get() = messageState.totalMessageCount
    val showArchivedChats: Boolean get() = conversationListState.showArchivedChats
    val newChatId: String? get() = conversationListState.newChatId
    val newGroupChatId: String? get() = groupChatState.newGroupChatId
    val selectedAttachments: List<MediaAttachment> get() = messageState.selectedAttachments
    val searchQuery: String get() = messageState.searchQuery
    val searchResults: List<Message> get() = messageState.searchResults
    val messageToEdit: Message? get() = messageState.messageToEdit

    // Operation states
    val isSending: Boolean get() = messageState.isSending
    val isSearching: Boolean get() = messageState.isSearching
    val isEditing: Boolean get() = messageState.isEditing
    val isDeleting: Boolean get() = messageState.isDeleting
    val isForwarding: Boolean get() = messageState.isForwarding
    val isArchiving: Boolean get() = conversationListState.isArchiving
    val isMuting: Boolean get() = conversationListState.isMuting
    val isPinning: Boolean get() = conversationListState.isPinning
    val isCreatingGroup: Boolean get() = groupChatState.createGroupOperation.isInProgress
    val isUpdatingGroup: Boolean get() = groupChatState.updateGroupOperation.isInProgress
    val isAddingParticipants: Boolean get() = groupChatState.addParticipantsOperation.isInProgress
    val isRemovingParticipants: Boolean get() = groupChatState.removeParticipantsOperation.isInProgress
    val isUpdatingAdmins: Boolean get() = groupChatState.updateAdminsOperation.isInProgress
    val isLeavingGroup: Boolean get() = groupChatState.leaveGroupOperation.isInProgress

    // Operation results
    val isMessageSent: Boolean get() = messageState.isMessageSent
    val isMessageEdited: Boolean get() = messageState.isMessageEdited
    val isMessageDeleted: Boolean get() = messageState.isMessageDeleted
    val isMessageForwarded: Boolean get() = messageState.isMessageForwarded
    val isChatArchived: Boolean get() = conversationListState.isChatArchived
    val isChatMuted: Boolean get() = conversationListState.isChatMuted
    val isChatPinned: Boolean get() = conversationListState.isChatPinned
    val isGroupCreated: Boolean get() = groupChatState.createGroupOperation.isSuccessful
    val isGroupUpdated: Boolean get() = groupChatState.updateGroupOperation.isSuccessful
    val areParticipantsAdded: Boolean get() = groupChatState.addParticipantsOperation.isSuccessful
    val areParticipantsRemoved: Boolean get() = groupChatState.removeParticipantsOperation.isSuccessful
    val areAdminsUpdated: Boolean get() = groupChatState.updateAdminsOperation.isSuccessful
    val isGroupLeft: Boolean get() = groupChatState.leaveGroupOperation.isSuccessful
}
