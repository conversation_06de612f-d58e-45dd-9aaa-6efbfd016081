package com.tfkcolin.meena.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.googlefonts.GoogleFont
import androidx.compose.ui.text.googlefonts.Font
import com.tfkcolin.meena.R

// Define the Google Font provider for Poppins and Open Sans
val provider = GoogleFont.Provider(
    providerAuthority = "com.google.android.gms.fonts",
    providerPackage = "com.google.android.gms",
    certificates = R.array.com_google_android_gms_fonts_certs
)

val fontOpenSans = "Open Sans"
val fontRoboto = "Roboto"

// Poppins Font Family (will be replaced by Roboto for display/headline/title)
val Roboto = FontFamily(
    Font(GoogleFont(fontRoboto), provider, FontWeight.Light),
    <PERSON>ont(GoogleFont(fontRoboto), provider, FontWeight.Normal),
    Font(GoogleFont(fontRoboto), provider, FontWeight.Medium),
    Font(GoogleFont(fontRoboto), provider, FontWeight.SemiBold),
    Font(GoogleFont(fontRoboto), provider, FontWeight.Bold),
    Font(GoogleFont(fontRoboto), provider, FontWeight.ExtraBold),
)

// Open Sans Font Family
val OpenSans = FontFamily(
    Font(GoogleFont(fontOpenSans), provider, FontWeight.Light),
    Font(GoogleFont(fontOpenSans), provider, FontWeight.Normal),
    Font(GoogleFont(fontOpenSans), provider, FontWeight.Medium),
    Font(GoogleFont(fontOpenSans), provider, FontWeight.SemiBold),
    Font(GoogleFont(fontOpenSans), provider, FontWeight.Bold),
    Font(GoogleFont(fontOpenSans), provider, FontWeight.ExtraBold),
)

val Typography = Typography(
// Styles for large headings (e.g., "MEENA" app title)
    displayLarge = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.ExtraBold, // Use ExtraBold for the main app name
        fontSize = 48.sp,
        lineHeight = 56.sp,
        letterSpacing = 0.sp
    ),
    displayMedium = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Bold,
        fontSize = 36.sp,
        lineHeight = 44.sp,
        letterSpacing = 0.sp
    ),
// Styles for smaller headlines, maybe section titles
    headlineLarge = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.SemiBold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 0.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.SemiBold,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp
    ),
// Styles for button text and prominent labels
    labelLarge = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Medium, // Medium or SemiBold for buttons
        fontSize = 18.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.5.sp
    ),
    labelMedium = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    ),
// Styles for body text (chat messages, descriptions)
    bodyLarge = TextStyle(
        fontFamily = OpenSans,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = OpenSans,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    ),
    bodySmall = TextStyle(
        fontFamily = OpenSans,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp
    ),
// Styles for small captions or hint text
    labelSmall = TextStyle(
        fontFamily = OpenSans,
        fontWeight = FontWeight.Normal, // Use Regular for small labels
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    ),
// Tagline "Messagerie ultra sécurisée"
    titleSmall = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp, // Adjust size as needed for tagline
        lineHeight = 24.sp,
        letterSpacing = 0.25.sp
    ),
// Other styles you might want to customize
    titleLarge = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Normal,
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp
    ),
    titleMedium = TextStyle(
        fontFamily = Roboto,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp
    ),
)
