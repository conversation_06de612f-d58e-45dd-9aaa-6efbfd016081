package com.tfkcolin.meena.ui.contacts

import android.util.Log
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.domain.usecases.contacts.GetContactsFlowUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactsByRelationshipFlowUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactByIdUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetRecentContactsUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetFavoriteContactsUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactGroupsUseCase
import com.tfkcolin.meena.domain.usecases.contacts.GetContactGroupByIdUseCase
import com.tfkcolin.meena.domain.usecases.contacts.CreateContactGroupUseCase
import com.tfkcolin.meena.domain.usecases.contacts.UpdateContactGroupUseCase
import com.tfkcolin.meena.domain.usecases.contacts.DeleteContactGroupUseCase
import com.tfkcolin.meena.domain.usecases.contacts.AddContactToGroupUseCase
import com.tfkcolin.meena.domain.usecases.contacts.RemoveContactFromGroupUseCase
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.ui.models.UIContact
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.TokenManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.tfkcolin.meena.config.AppConfig // Added for ENABLE_MOCK_LOGGING

/**
 * ViewModel for contact management screens.
 */
@HiltViewModel
class ContactsViewModel @Inject constructor(
    private val mockDataStorage: MockDataStorage, // Inject MockDataStorage
    private val tokenManager: TokenManager, // Inject TokenManager to get current user ID
    private val getContactsFlowUseCase: GetContactsFlowUseCase,
    private val getContactsByRelationshipFlowUseCase: GetContactsByRelationshipFlowUseCase,
    private val getContactByIdUseCase: GetContactByIdUseCase,
    private val getRecentContactsUseCase: GetRecentContactsUseCase,
    private val getFavoriteContactsUseCase: GetFavoriteContactsUseCase,
    private val getContactGroupsUseCase: GetContactGroupsUseCase,
    private val getContactGroupByIdUseCase: GetContactGroupByIdUseCase,
    private val createContactGroupUseCase: CreateContactGroupUseCase,
    private val updateContactGroupUseCase: UpdateContactGroupUseCase,
    private val deleteContactGroupUseCase: DeleteContactGroupUseCase,
    private val addContactToGroupUseCase: AddContactToGroupUseCase,
    private val removeContactFromGroupUseCase: RemoveContactFromGroupUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for contact operations
    private val _contactsState = MutableStateFlow(ContactsState())
    val contactsState: StateFlow<ContactsState> = _contactsState.asStateFlow()

    // State for the MEENA ID input field in Add Contact section
    private val _addContactMeenaId = MutableStateFlow("")
    val addContactMeenaId: StateFlow<String> = _addContactMeenaId.asStateFlow()

    // Map of contact user IDs to User objects
    private val _userMap = MutableStateFlow<Map<String, User>>(emptyMap())

    // Contacts from local database
    private val _contacts = getContactsFlowUseCase()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Regular contacts list for contact management screens
    val contacts = _contacts

    // Contacts filtered by relationship
    val contactsByRelationship = getContactsByRelationshipFlowUseCase("friend")
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // UI contacts for selection dialogs
    val uiContacts = _contacts
        .map { contactList ->
            contactList.map { contact ->
                val user = _userMap.value[contact.contactId]
                UIContact.fromContactAndUser(contact, user)
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Contact groups
    private val _contactGroups = MutableStateFlow<List<ContactGroup>>(emptyList())
    val contactGroups = _contactGroups.asStateFlow()

    init {
        // Load contacts from local database (now handled by MockDataStorage)
        loadContacts()

        // Load recent and favorite contacts (these use cases might need to be updated to use MockDataStorage)
        loadRecentContacts()
        loadFavoriteContacts()

        // Load contact groups
        loadContactGroups()
    }

    /**
     * Update the value of the MEENA ID input field.
     */
    fun updateAddContactMeenaId(newValue: String) {
        _addContactMeenaId.value = newValue
    }

    /**
     * Load contacts from the local database via MockDataStorage.
     */
    fun loadContacts() {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        properties = it.properties.copy(
                            isLoading = false,
                            error = "Current user not found."
                        )
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                // Collect from the flow to update the user map
                _contacts.collect { contactsFromDb ->
                    contactsFromDb.forEach { contact ->
                        loadUserDetails(contact.contactId)
                    }
                    _contactsState.update {
                        it.copy(
                            totalCount = contactsFromDb.size,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        properties = it.properties.copy(
                            isLoading = false,
                            error = getErrorMessage(e, "Failed to load contacts from mock storage")
                        )
                    )
                }
            }
        }
    }

    /**
     * Load user details for a contact.
     *
     * @param userId The user ID.
     */
    private fun loadUserDetails(userId: String) {
        viewModelScope.launch {
            try {
                val user = mockDataStorage.getUser(userId)
                if (user != null) {
                    _userMap.update { currentMap ->
                        currentMap + (userId to user)
                    }
                }
            } catch (e: Exception) {
                // Ignore errors for now, or log them
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Error loading user details for $userId: ${e.message}")
                }
            }
        }
    }

    /**
     * Add a mock AI contact.
     * This method will generate a new mock AI user and contact, and save them to the database.
     */
    fun addContact(displayName: String? = null, notes: String? = null) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                addContactOperation = it.addContactOperation.start()
            ) }

            val contactName = _addContactMeenaId.value.trim()
            if (contactName.isBlank()) {
                _contactsState.update {
                    it.copy(
                        addContactOperation = it.addContactOperation.failure("Contact name cannot be empty.")
                    )
                }
                return@launchWithErrorHandling
            }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        addContactOperation = it.addContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                // Generate a new mock AI user
                val newMockAIUser = MockDataGenerator.generateMockAIContactUser(contactName)
                mockDataStorage.addUser(newMockAIUser)

                // Generate a contact for the current user pointing to the new mock AI user
                val newContact = MockDataGenerator.generateMockAIContact(
                    currentUserId,
                    newMockAIUser,
                    displayName = displayName,
                    notes = notes
                )
                Log.i("ContactsViewModel", "Adding mock AI contact - userId: ${newMockAIUser}")
                mockDataStorage.addContact(currentUserId, newContact)

                _contactsState.update {
                    it.copy(
                        addContactOperation = it.addContactOperation.success(),
                    )
                }
                _addContactMeenaId.value = "" // Clear input field
                // loadContacts() // No need to explicitly call loadContacts, flow will update
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        addContactOperation = it.addContactOperation.failure(
                            getErrorMessage(e, "Failed to add mock AI contact.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Update a contact.
     * This method will now update the contact in MockDataStorage.
     */
    fun updateContact(contactId: String, displayName: String? = null, notes: String? = null, relationship: String? = null) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                updateContactOperation = it.updateContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        updateContactOperation = it.updateContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val existingContact = mockDataStorage.getContact(currentUserId, contactId)
                if (existingContact == null) {
                    _contactsState.update {
                        it.copy(
                            updateContactOperation = it.updateContactOperation.failure("Contact not found.")
                        )
                    }
                    return@launchWithErrorHandling
                }

                val updatedContact = existingContact.copy(
                    displayName = displayName ?: existingContact.displayName,
                    notes = notes ?: existingContact.notes,
                    relationship = relationship ?: existingContact.relationship
                )
                mockDataStorage.addContact(currentUserId, updatedContact) // addContact with REPLACE strategy acts as update

                // Also update the associated User's display name if it's a mock AI contact
                if (MockDataGenerator.isMockAIContact(existingContact.contactId)) {
                    val mockAIUser = mockDataStorage.getUser(existingContact.contactId)
                    if (mockAIUser != null) {
                        val updatedMockAIUser = mockAIUser.copy(
                            displayName = displayName ?: mockAIUser.displayName
                        )
                        mockDataStorage.addUser(updatedMockAIUser) // addUser with REPLACE strategy acts as update
                    }
                }

                _contactsState.update {
                    it.copy(
                        updateContactOperation = it.updateContactOperation.success(),
                    )
                }
                // loadContacts() // No need to explicitly call loadContacts, flow will update
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        updateContactOperation = it.updateContactOperation.failure(
                            getErrorMessage(e, "Failed to update contact.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Delete a contact.
     * This method will now delete the contact from MockDataStorage.
     */
    fun deleteContact(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                deleteContactOperation = it.deleteContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        deleteContactOperation = it.deleteContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                // First, get the contact to find the associated mock AI user ID
                val contactToDelete = mockDataStorage.getContact(currentUserId, contactId)
                if (contactToDelete == null) {
                    _contactsState.update {
                        it.copy(
                            deleteContactOperation = it.deleteContactOperation.failure("Contact not found.")
                        )
                    }
                    return@launchWithErrorHandling
                }

                // Remove the contact from the current user's contacts
                mockDataStorage.removeContact(currentUserId, contactId)

                // If it's a mock AI contact, also delete the associated mock AI user
                if (MockDataGenerator.isMockAIContact(contactToDelete.contactId)) {
                    mockDataStorage.getUser(contactToDelete.contactId)?.let { mockAIUser ->
                        mockDataStorage.deleteUser(mockAIUser.userId)
                    }
                }

                _contactsState.update {
                    it.copy(
                        deleteContactOperation = it.deleteContactOperation.success(),
                    )
                }
                // loadContacts() // No need to explicitly call loadContacts, flow will update
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        deleteContactOperation = it.deleteContactOperation.failure(
                            getErrorMessage(e, "Failed to delete contact.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Search contacts.
     * This method will now search contacts in MockDataStorage.
     */
    fun searchContacts(query: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                searchContactsOperation = it.searchContactsOperation.start(),
                searchQuery = query
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        searchContactsOperation = it.searchContactsOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val allContacts = mockDataStorage.getContacts(currentUserId)
                val searchResults = allContacts.filter { contact ->
                    contact.displayName?.contains(query, ignoreCase = true) == true ||
                            contact.notes?.contains(query, ignoreCase = true) == true ||
                            contact.user?.handle?.contains(query, ignoreCase = true) == true
                }

                _contactsState.update {
                    it.copy(
                        searchContactsOperation = it.searchContactsOperation.success(),
                        searchResults = searchResults
                    )
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        searchContactsOperation = it.searchContactsOperation.failure(
                            getErrorMessage(e, "Failed to search contacts.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Load recent contacts.
     * This will now load from MockDataStorage.
     */
    fun loadRecentContacts(limit: Int = 10) {
        viewModelScope.launch {
            val currentUserId = tokenManager.getUserId() ?: return@launch
            try {
                val allContacts = mockDataStorage.getContacts(currentUserId)
                // For mock, "recent" can be simplified to just the most recently added contacts
                val recent = allContacts.sortedByDescending { it.createdAt }.take(limit)
                _contactsState.update {
                    it.copy(
                        recentContacts = recent
                    )
                }
            } catch (e: Exception) {
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Error loading recent contacts: ${e.message}")
                }
            }
        }
    }

    /**
     * Load favorite contacts.
     * This will now load from MockDataStorage.
     */
    fun loadFavoriteContacts(limit: Int = 50, offset: Int = 0) {
        viewModelScope.launch {
            val currentUserId = tokenManager.getUserId() ?: return@launch
            try {
                val allContacts = mockDataStorage.getContacts(currentUserId)
                val favorites = allContacts.filter { it.isFavorite }.drop(offset).take(limit)
                _contactsState.update {
                    it.copy(
                        favoriteContacts = favorites
                    )
                }
            } catch (e: Exception) {
                if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                    println("Error loading favorite contacts: ${e.message}")
                }
            }
        }
    }

    /**
     * Add a contact to favorites.
     * This will now update in MockDataStorage.
     */
    fun addToFavorites(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                favoriteContactOperation = it.favoriteContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        favoriteContactOperation = it.favoriteContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val contact = mockDataStorage.getContact(currentUserId, contactId)
                if (contact != null) {
                    mockDataStorage.addContact(currentUserId, contact.copy(isFavorite = true))
                    _contactsState.update {
                        it.copy(
                            favoriteContactOperation = it.favoriteContactOperation.success(),
                        )
                    }
                    loadFavoriteContacts()
                } else {
                    _contactsState.update {
                        it.copy(
                            favoriteContactOperation = it.favoriteContactOperation.failure("Contact not found.")
                        )
                    }
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        favoriteContactOperation = it.favoriteContactOperation.failure(
                            getErrorMessage(e, "Failed to add contact to favorites.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Remove a contact from favorites.
     * This will now update in MockDataStorage.
     */
    fun removeFromFavorites(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                unfavoriteContactOperation = it.unfavoriteContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        unfavoriteContactOperation = it.unfavoriteContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val contact = mockDataStorage.getContact(currentUserId, contactId)
                if (contact != null) {
                    mockDataStorage.addContact(currentUserId, contact.copy(isFavorite = false))
                    _contactsState.update {
                        it.copy(
                            unfavoriteContactOperation = it.unfavoriteContactOperation.success(),
                        )
                    }
                    loadFavoriteContacts()
                } else {
                    _contactsState.update {
                        it.copy(
                            unfavoriteContactOperation = it.unfavoriteContactOperation.failure("Contact not found.")
                        )
                    }
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        unfavoriteContactOperation = it.unfavoriteContactOperation.failure(
                            getErrorMessage(e, "Failed to remove contact from favorites.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Block a contact.
     * This will now update in MockDataStorage.
     */
    fun blockContact(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                blockContactOperation = it.blockContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        blockContactOperation = it.blockContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val contact = mockDataStorage.getContact(currentUserId, contactId)
                if (contact != null) {
                    mockDataStorage.addContact(currentUserId, contact.copy(relationship = "blocked"))
                    _contactsState.update {
                        it.copy(
                            blockContactOperation = it.blockContactOperation.success(),
                        )
                    }
                } else {
                    _contactsState.update {
                        it.copy(
                            blockContactOperation = it.blockContactOperation.failure("Contact not found.")
                        )
                    }
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        blockContactOperation = it.blockContactOperation.failure(
                            getErrorMessage(e, "Failed to block contact.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Unblock a contact.
     * This will now update in MockDataStorage.
     */
    fun unblockContact(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                unblockContactOperation = it.unblockContactOperation.start()
            ) }

            val currentUserId = tokenManager.getUserId()
            if (currentUserId == null) {
                _contactsState.update {
                    it.copy(
                        unblockContactOperation = it.unblockContactOperation.failure("Current user not authenticated for mock operations.")
                    )
                }
                return@launchWithErrorHandling
            }

            try {
                val contact = mockDataStorage.getContact(currentUserId, contactId)
                if (contact != null) {
                    mockDataStorage.addContact(currentUserId, contact.copy(relationship = "friend")) // Assuming "friend" is default
                    _contactsState.update {
                        it.copy(
                            unblockContactOperation = it.unblockContactOperation.success(),
                        )
                    }
                } else {
                    _contactsState.update {
                        it.copy(
                            unblockContactOperation = it.unblockContactOperation.failure("Contact not found.")
                        )
                    }
                }
            } catch (e: Exception) {
                _contactsState.update {
                    it.copy(
                        unblockContactOperation = it.unblockContactOperation.failure(
                            getErrorMessage(e, "Failed to unblock contact.")
                        )
                    )
                }
            }
        }
    }

    /**
     * Filter contacts by relationship.
     */
    fun filterContactsByRelationship(relationship: String?) {
        _contactsState.update {
            it.copy(
                selectedRelationship = relationship
            )
        }
    }

    /**
     * Load contact groups.
     */
    fun loadContactGroups() {
        viewModelScope.launch {
            try {
                // Use getAll() method which returns a List instead of a Flow
                val groups = getContactGroupsUseCase.getAll()
                _contactGroups.value = groups
            } catch (e: Exception) {
                // Ignore errors
            }
        }
    }

    /**
     * Create a contact group.
     */
    fun createContactGroup(name: String, description: String? = null) {
        viewModelScope.launch {
            _contactsState.update { it.copy(
                createGroupOperation = it.createGroupOperation.start()
            ) }

            try {
                // Direct call to use case which returns ContactGroup
                val response = createContactGroupUseCase(
                    name = name,
                    description = description
                )

                _contactsState.update {
                    it.copy(
                        createGroupOperation = it.createGroupOperation.success()
                    )
                }

                // Reload contact groups
                loadContactGroups()
            } catch (error: Exception) {
                _contactsState.update {
                    it.copy(
                        createGroupOperation = it.createGroupOperation.failure(
                            getErrorMessage(error, "Failed to create group")
                        )
                    )
                }
            }
        }
    }

    /**
     * Update a contact group.
     */
    fun updateContactGroup(groupId: String, name: String, description: String? = null) {
        viewModelScope.launch {
            _contactsState.update { it.copy(
                updateGroupOperation = it.updateGroupOperation.start()
            ) }

            try {
                // Direct call to use case which returns ContactGroup
                val response = updateContactGroupUseCase(
                    groupId = groupId,
                    name = name,
                    description = description
                )

                _contactsState.update {
                    it.copy(
                        updateGroupOperation = it.updateGroupOperation.success()
                    )
                }

                // Reload contact groups
                loadContactGroups()
            } catch (error: Exception) {
                _contactsState.update {
                    it.copy(
                        updateGroupOperation = it.updateGroupOperation.failure(
                            getErrorMessage(error, "Failed to update group")
                        )
                    )
                }
            }
        }
    }

    /**
     * Delete a contact group.
     */
    fun deleteContactGroup(groupId: String) {
        viewModelScope.launch {
            _contactsState.update { it.copy(
                deleteGroupOperation = it.deleteGroupOperation.start()
            ) }

            try {
                // Direct call to use case which returns Unit (void)
                deleteContactGroupUseCase(groupId)

                _contactsState.update {
                    it.copy(
                        deleteGroupOperation = it.deleteGroupOperation.success()
                    )
                }

                // Reload contact groups
                loadContactGroups()
            } catch (error: Exception) {
                _contactsState.update {
                    it.copy(
                        deleteGroupOperation = it.deleteGroupOperation.failure(
                            getErrorMessage(error, "Failed to delete group")
                        )
                    )
                }
            }
        }
    }

    /**
     * Add a contact to a group.
     */
    fun addContactToGroup(groupId: String, contactId: String) {
        viewModelScope.launch {
            _contactsState.update { it.copy(
                addToGroupOperation = it.addToGroupOperation.start()
            ) }

            try {
                // Direct call to use case which returns ContactGroup
                val response = addContactToGroupUseCase(groupId, contactId)

                _contactsState.update {
                    it.copy(
                        addToGroupOperation = it.addToGroupOperation.success()
                    )
                }

                // Reload contact groups
                loadContactGroups()
            } catch (error: Exception) {
                _contactsState.update {
                    it.copy(
                        addToGroupOperation = it.addToGroupOperation.failure(
                            getErrorMessage(error, "Failed to add contact to group")
                        )
                    )
                }
            }
        }
    }

    /**
     * Remove a contact from a group.
     */
    fun removeContactFromGroup(groupId: String, contactId: String) {
        viewModelScope.launch {
            _contactsState.update { it.copy(
                removeFromGroupOperation = it.removeFromGroupOperation.start()
            ) }

            try {
                // Direct call to use case which returns ContactGroup
                val response = removeContactFromGroupUseCase(groupId, contactId)

                _contactsState.update {
                    it.copy(
                        removeFromGroupOperation = it.removeFromGroupOperation.success()
                    )
                }

                // Reload contact groups
                loadContactGroups()
            } catch (error: Exception) {
                _contactsState.update {
                    it.copy(
                        removeFromGroupOperation = it.removeFromGroupOperation.failure(
                            getErrorMessage(error, "Failed to remove contact from group")
                        )
                    )
                }
            }
        }
    }

    /**
     * Reset contact operation states.
     */
    fun resetContactOperationStates() {
        _contactsState.update {
            it.copy(
                addContactOperation = it.addContactOperation.reset(),
                updateContactOperation = it.updateContactOperation.reset(),
                deleteContactOperation = it.deleteContactOperation.reset(),
                searchContactsOperation = it.searchContactsOperation.reset(),
                favoriteContactOperation = it.favoriteContactOperation.reset(),
                unfavoriteContactOperation = it.unfavoriteContactOperation.reset(),
                blockContactOperation = it.blockContactOperation.reset(),
                unblockContactOperation = it.unblockContactOperation.reset(),
                createGroupOperation = it.createGroupOperation.reset(),
                updateGroupOperation = it.updateGroupOperation.reset(),
                deleteGroupOperation = it.deleteGroupOperation.reset(),
                addToGroupOperation = it.addToGroupOperation.reset(),
                removeFromGroupOperation = it.removeFromGroupOperation.reset()
            )
        }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _contactsState.update {
            it.copy(
                properties = it.properties.copy(error = null)
            )
        }
    }

    /**
     * Get error message from a throwable.
     */
    private fun getErrorMessage(throwable: Throwable, fallback: String? = null): String {
        return errorHandler.getErrorMessage(throwable, fallback)
    }
}

/**
 * State for contact management screens.
 */
data class ContactsState(
    val totalCount: Int = 0,
    val addContactOperation: OperationState = OperationState(),
    val updateContactOperation: OperationState = OperationState(),
    val deleteContactOperation: OperationState = OperationState(),
    val searchContactsOperation: OperationState = OperationState(),
    val favoriteContactOperation: OperationState = OperationState(),
    val unfavoriteContactOperation: OperationState = OperationState(),
    val blockContactOperation: OperationState = OperationState(),
    val unblockContactOperation: OperationState = OperationState(),
    val createGroupOperation: OperationState = OperationState(),
    val updateGroupOperation: OperationState = OperationState(),
    val deleteGroupOperation: OperationState = OperationState(),
    val addToGroupOperation: OperationState = OperationState(),
    val removeFromGroupOperation: OperationState = OperationState(),
    val searchQuery: String = "",
    val searchResults: List<Contact> = emptyList(),
    val recentContacts: List<Contact> = emptyList(),
    val favoriteContacts: List<Contact> = emptyList(),
    val selectedRelationship: String? = null,
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
