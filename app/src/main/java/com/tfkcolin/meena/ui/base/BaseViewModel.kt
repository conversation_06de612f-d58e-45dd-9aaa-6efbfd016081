package com.tfkcolin.meena.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Base ViewModel class that provides common functionality for all ViewModels.
 * Includes error handling, loading state management, and coroutine utilities.
 */
abstract class BaseViewModel(
    protected val errorHandler: ErrorHandler
) : ViewModel() {

    // Application context from the ErrorHandler
    protected val appContext = errorHandler.getApplicationContext()

    // Error state
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    /**
     * Clear the error message.
     */
    open fun clearError() {
        _error.value = null
    }

    /**
     * Set an error message.
     *
     * @param message The error message.
     */
    protected fun setError(message: String) {
        _error.value = message
    }

    /**
     * Set an error from a throwable.
     *
     * @param throwable The throwable.
     * @param fallbackMessage Optional fallback message if the throwable doesn't have a message.
     */
    protected fun setError(throwable: Throwable, fallbackMessage: String? = null) {
        _error.value = errorHandler.getErrorMessage(throwable, fallbackMessage)
    }

    /**
     * Set the loading state.
     *
     * @param isLoading Whether the ViewModel is loading data.
     */
    protected fun setLoading(isLoading: Boolean) {
        _isLoading.value = isLoading
    }

    /**
     * Launch a coroutine with error handling.
     *
     * @param block The coroutine block to execute.
     */
    protected fun launchWithErrorHandling(block: suspend CoroutineScope.() -> Unit) {
        viewModelScope.launch(
            CoroutineExceptionHandler { _, throwable ->
                setError(throwable)
                setLoading(false)
            }
        ) {
            block()
        }
    }

    /**
     * Execute a use case and handle the result.
     *
     * @param useCase The use case to execute.
     * @param onSuccess The callback to execute on success.
     * @param onError The callback to execute on error. If not provided, the error will be set automatically.
     * @param showLoading Whether to show the loading state.
     */
    protected suspend fun <T> executeUseCase(
        useCase: suspend () -> Result<T>,
        onSuccess: (T) -> Unit,
        onError: ((Throwable) -> Unit)? = null,
        showLoading: Boolean = true
    ) {
        if (showLoading) {
            setLoading(true)
        }

        try {
            val result = useCase()
            result.fold(
                onSuccess = {
                    onSuccess(it)
                    if (showLoading) {
                        setLoading(false)
                    }
                },
                onFailure = { throwable ->
                    if (onError != null) {
                        onError(throwable)
                    } else {
                        setError(throwable)
                    }
                    if (showLoading) {
                        setLoading(false)
                    }
                }
            )
        } catch (e: Exception) {
            if (onError != null) {
                onError(e)
            } else {
                setError(e)
            }
            if (showLoading) {
                setLoading(false)
            }
        }
    }
}
