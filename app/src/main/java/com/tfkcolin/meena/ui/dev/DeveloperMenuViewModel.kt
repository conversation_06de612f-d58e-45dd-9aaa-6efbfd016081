package com.tfkcolin.meena.ui.dev

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.mock.MockInitializer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the developer menu screen.
 */
@HiltViewModel
class DeveloperMenuViewModel @Inject constructor(
    private val mockInitializer: MockInitializer
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DeveloperMenuUiState())
    val uiState: StateFlow<DeveloperMenuUiState> = _uiState.asStateFlow()
    
    init {
        loadCurrentState()
    }
    
    private fun loadCurrentState() {
        viewModelScope.launch {
            val mockStats = if (AppConfig.useMockBackend) {
                val currentUser = mockInitializer.getCurrentMockUser()
                val allUsers = mockInitializer.getAllMockUsers()
                val allChats = mockInitializer.getMockChats()
                
                MockDataStats(
                    userCount = allUsers.size,
                    chatCount = allChats.size,
                    messageCount = 0, // Would need to calculate from all chats
                    contactCount = 0, // Would need to calculate from current user
                    currentUserHandle = currentUser?.userHandle ?: "None"
                )
            } else {
                MockDataStats()
            }
            
            _uiState.value = _uiState.value.copy(
                useMockBackend = true, // Always true in mock-only branch
                aiResponsesEnabled = AppConfig.FeatureFlags.ENABLE_AI_CHAT_RESPONSES,
                typingIndicatorsEnabled = AppConfig.FeatureFlags.ENABLE_TYPING_INDICATORS,
                mockStats = mockStats,
                featureFlags = FeatureFlagsState(
                    storiesEnabled = AppConfig.FeatureFlags.ENABLE_STORIES,
                    callsEnabled = AppConfig.FeatureFlags.ENABLE_CALLS,
                    groupChatsEnabled = AppConfig.FeatureFlags.ENABLE_GROUP_CHATS,
                    channelsEnabled = AppConfig.FeatureFlags.ENABLE_CHANNELS,
                    mediaMessagesEnabled = AppConfig.FeatureFlags.ENABLE_MEDIA_MESSAGES
                )
            )
        }
    }
    
    fun toggleMockBackend() {
        // Note: Mock backend is always enabled in this branch
        // This method is kept for UI compatibility but doesn't change anything
        _uiState.value = _uiState.value.copy(useMockBackend = true)
        loadCurrentState()
    }
    
    fun resetMockData() {
        viewModelScope.launch {
            mockInitializer.resetMockData()
            loadCurrentState()
        }
    }
    
    fun toggleAIResponses() {
        // Note: In a real implementation, you'd want to persist these settings
        val newValue = !_uiState.value.aiResponsesEnabled
        _uiState.value = _uiState.value.copy(aiResponsesEnabled = newValue)
    }
    
    fun toggleTypingIndicators() {
        val newValue = !_uiState.value.typingIndicatorsEnabled
        _uiState.value = _uiState.value.copy(typingIndicatorsEnabled = newValue)
    }
    
    fun toggleFeatureFlag(flag: String) {
        val currentFlags = _uiState.value.featureFlags
        val newFlags = when (flag) {
            "stories" -> currentFlags.copy(storiesEnabled = !currentFlags.storiesEnabled)
            "calls" -> currentFlags.copy(callsEnabled = !currentFlags.callsEnabled)
            "group_chats" -> currentFlags.copy(groupChatsEnabled = !currentFlags.groupChatsEnabled)
            "channels" -> currentFlags.copy(channelsEnabled = !currentFlags.channelsEnabled)
            "media_messages" -> currentFlags.copy(mediaMessagesEnabled = !currentFlags.mediaMessagesEnabled)
            else -> currentFlags
        }
        _uiState.value = _uiState.value.copy(featureFlags = newFlags)
    }
    
    fun generateTestData() {
        viewModelScope.launch {
            // Generate additional test data
            // This would involve adding more users, chats, messages, etc.
            loadCurrentState()
        }
    }
    
    fun simulateIncomingMessage() {
        viewModelScope.launch {
            // Simulate an incoming message in a random chat
            // This would involve adding a new message to an existing chat
        }
    }
    
    fun clearAppData() {
        viewModelScope.launch {
            mockInitializer.resetMockData()
            loadCurrentState()
        }
    }
}

/**
 * UI state for the developer menu.
 */
data class DeveloperMenuUiState(
    val useMockBackend: Boolean = false,
    val aiResponsesEnabled: Boolean = true,
    val typingIndicatorsEnabled: Boolean = true,
    val mockStats: MockDataStats = MockDataStats(),
    val featureFlags: FeatureFlagsState = FeatureFlagsState(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Mock data statistics.
 */
data class MockDataStats(
    val userCount: Int = 0,
    val chatCount: Int = 0,
    val messageCount: Int = 0,
    val contactCount: Int = 0,
    val currentUserHandle: String = "None"
)

/**
 * Feature flags state.
 */
data class FeatureFlagsState(
    val storiesEnabled: Boolean = true,
    val callsEnabled: Boolean = true,
    val groupChatsEnabled: Boolean = true,
    val channelsEnabled: Boolean = true,
    val mediaMessagesEnabled: Boolean = true
)
