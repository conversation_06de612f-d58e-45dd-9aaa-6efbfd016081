package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * UI state for conversation list operations.
 */
data class ConversationListState(
    // Base state properties (composition)
    val properties: UiStateProperties = UiStateProperties(),

    // Operation states
    val archiveConversationOperation: OperationState = OperationState(),
    val muteConversationOperation: OperationState = OperationState(),
    val pinConversationOperation: OperationState = OperationState(),

    // Data
    val totalChatCount: Int = 0,
    val showArchivedChats: Boolean = false,
    val newChatId: String? = null
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error
}

/**
 * Extension function to check if any operation is in progress.
 */
fun ConversationListState.isAnyOperationInProgress(): Boolean {
    return archiveConversationOperation.isInProgress ||
            muteConversationOperation.isInProgress ||
            pinConversationOperation.isInProgress
}

/**
 * Extension function to reset all operation states.
 */
fun ConversationListState.resetAllOperations(): ConversationListState {
    return copy(
        properties = properties.copy(error = null),
        archiveConversationOperation = OperationState(),
        muteConversationOperation = OperationState(),
        pinConversationOperation = OperationState(),
        newChatId = null
    )
}
