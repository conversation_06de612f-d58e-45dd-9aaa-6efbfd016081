package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * UI state for message operations.
 */
data class MessageState(
    // Base state properties (composition)
    val properties: UiStateProperties = UiStateProperties(),

    // Operation states
    val sendMessageOperation: OperationState = OperationState(),
    val editMessageOperation: OperationState = OperationState(),
    val deleteMessageOperation: OperationState = OperationState(),
    val forwardMessageOperation: OperationState = OperationState(),
    val searchMessagesOperation: OperationState = OperationState(),

    // Data
    val totalMessageCount: Int = 0,
    val selectedAttachments: List<MediaAttachment> = emptyList(),
    val searchQuery: String = "",
    val searchResults: List<Message> = emptyList(),
    val messageToEdit: Message? = null
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error
}

/**
 * Extension function to check if any operation is in progress.
 */
fun MessageState.isAnyOperationInProgress(): Boolean {
    return sendMessageOperation.isInProgress ||
            editMessageOperation.isInProgress ||
            deleteMessageOperation.isInProgress ||
            forwardMessageOperation.isInProgress ||
            searchMessagesOperation.isInProgress
}

/**
 * Extension function to reset all operation states.
 */
fun MessageState.resetAllOperations(): MessageState {
    return copy(
        properties = properties.copy(error = null),
        sendMessageOperation = OperationState(),
        editMessageOperation = OperationState(),
        deleteMessageOperation = OperationState(),
        forwardMessageOperation = OperationState(),
        searchMessagesOperation = OperationState(),
        messageToEdit = null
    )
}
