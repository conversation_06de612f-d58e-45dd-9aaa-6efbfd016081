package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.meena.data.models.MessageReactionSummary

/**
 * A component for displaying message reactions.
 *
 * @param reactions The list of reaction summaries to display.
 * @param currentUserId The current user's ID.
 * @param onReactionClick The callback for when a reaction is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun MessageReactionsView(
    reactions: List<MessageReactionSummary>,
    currentUserId: String,
    onReactionClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        reactions.forEach { reaction ->
            ReactionBubble(
                emoji = reaction.emoji,
                count = reaction.count,
                isSelected = reaction.hasUserReacted(currentUserId),
                onClick = { onReactionClick(reaction.emoji) }
            )
        }
    }
}

/**
 * A bubble for displaying a single reaction.
 *
 * @param emoji The emoji to display.
 * @param count The number of users who reacted with this emoji.
 * @param isSelected Whether the current user has reacted with this emoji.
 * @param onClick The callback for when the bubble is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun ReactionBubble(
    emoji: String,
    count: Int,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
    }
    
    val borderColor = if (isSelected) {
        MaterialTheme.colorScheme.primary
    } else {
        Color.Transparent
    }
    
    val textColor = if (isSelected) {
        MaterialTheme.colorScheme.onPrimaryContainer
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(16.dp)
            )
            .clickable { onClick() }
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = emoji,
                fontSize = 16.sp,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = count.toString(),
                style = MaterialTheme.typography.labelSmall,
                color = textColor
            )
        }
    }
}
