package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * UI state for the conversation list screen.
 */
data class ConversationListUiState(
    val properties: UiStateProperties = UiStateProperties(),
    val archiveOperation: OperationState = OperationState(),
    val muteOperation: OperationState = OperationState(),
    val pinOperation: OperationState = OperationState(),
    val createChatOperation: OperationState = OperationState(),
    val totalChatCount: Int = 0,
    val showArchivedChats: Boolean = false,
    val newChatId: String? = null
) : UiState {
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error

    // Convenience properties for backward compatibility
    val isArchiving: <PERSON>olean get() = archiveOperation.isInProgress
    val isMuting: Boolean get() = muteOperation.isInProgress
    val isPinning: Boolean get() = pinOperation.isInProgress
    val isChatArchived: Boolean get() = archiveOperation.isSuccessful
    val isChatMuted: Boolean get() = muteOperation.isSuccessful
    val isChatPinned: Boolean get() = pinOperation.isSuccessful
}
