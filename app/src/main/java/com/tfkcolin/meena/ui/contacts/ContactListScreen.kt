package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FabPosition // Added import
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.isFavorite
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.SwipeToDelete
import com.tfkcolin.meena.ui.contacts.components.ContactListItem
import com.tfkcolin.meena.ui.contacts.components.ContactSearchBar
import com.tfkcolin.meena.ui.contacts.components.ContactSection
import com.tfkcolin.meena.ui.contacts.components.RelationshipFilterChips

/**
 * Contact list screen.
 *
 * @param onNavigateToAddContact Navigate to the add contact screen.
 * @param onNavigateToContactDetail Navigate to the contact detail screen.
 * @param onNavigateToContactGroups Navigate to the contact groups screen.
 * @param contactsState The contacts UI state.
 * @param contacts List of all contacts.
 * @param onClearContactsError Callback to clear contact-related errors.
 * @param onResetContactOperationStates Callback to reset all contact operation states.
 * @param onLoadContacts Callback to reload contacts.
 * @param onSearchContacts Callback to trigger contact search.
 * @param onFilterContactsByRelationship Callback to filter contacts by relationship.
 * @param onDeleteContact Callback to delete a contact.
 * @param onRemoveFromFavorites Callback to remove a contact from favorites.
 * @param onAddToFavorites Callback to add a contact to favorites.
 * @param onUnblockContact Callback to unblock a contact.
 * @param onBlockContact Callback to block a contact.
 */
@Composable
fun ContactListScreen(
    onNavigateToAddContact: () -> Unit,
    onNavigateToContactDetail: (String) -> Unit,
    onNavigateToContactGroups: () -> Unit,
    contactsState: ContactsState,
    contacts: List<Contact>,
    onClearContactsError: () -> Unit,
    onResetContactOperationStates: () -> Unit,
    onLoadContacts: () -> Unit,
    onSearchContacts: (String) -> Unit,
    onFilterContactsByRelationship: (String?) -> Unit,
    onDeleteContact: (String) -> Unit,
    onRemoveFromFavorites: (String) -> Unit,
    onAddToFavorites: (String) -> Unit,
    onUnblockContact: (String) -> Unit,
    onBlockContact: (String) -> Unit,
    onShowSnackbar: (String) -> Unit // Added parameter
) {
    // Removed snackbarHostState

    var searchQuery by remember { mutableStateOf("") }
    var showSearch by remember { mutableStateOf(false) }

    // Filter contacts based on selected relationship
    val filteredContacts = if (contactsState.selectedRelationship != null) {
        contacts.filter { it.relationship == contactsState.selectedRelationship }
    } else {
        contacts
    }

    // Filter contacts based on search query
    val searchResults = if (searchQuery.isNotEmpty()) {
        filteredContacts.filter {
            it.displayName?.contains(searchQuery, ignoreCase = true) == true ||
            it.notes?.contains(searchQuery, ignoreCase = true) == true ||
            it.contactId.contains(searchQuery, ignoreCase = true)
        }
    } else {
        filteredContacts
    }

    // Get favorite contacts
    val favoriteContacts = contacts.filter { it.isFavorite() }

    // Get recent contacts
    val recentContacts = contactsState.recentContacts

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            onShowSnackbar(it) // Use callback to show snackbar
            onClearContactsError()
        }
    }
    val contactAddedSuccessfully = stringResource(R.string.contact_added_successfully)
    val contactUpdatedSuccessfully = stringResource(R.string.contact_updated_successfully)
    val contactBlockedSuccessfully = stringResource(R.string.contact_blocked_successfully)
    val contactUnblockedSuccessfully = stringResource(R.string.contact_unblocked_successfully)

    // Show operation success/error messages
    LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
        if (contactsState.addContactOperation.isSuccessful) {
            onShowSnackbar(contactAddedSuccessfully) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.updateContactOperation.isSuccessful) {
        if (contactsState.updateContactOperation.isSuccessful) {
            onShowSnackbar(contactUpdatedSuccessfully) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.blockContactOperation.isSuccessful) {
        if (contactsState.blockContactOperation.isSuccessful) {
            onShowSnackbar(contactBlockedSuccessfully) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.isSuccessful) {
        if (contactsState.unblockContactOperation.isSuccessful) {
            onShowSnackbar(contactUnblockedSuccessfully) // Use callback
            onResetContactOperationStates()
        }
    }

    // Show operation error messages
    LaunchedEffect(contactsState.addContactOperation.error) {
        contactsState.addContactOperation.error?.let {
            onShowSnackbar(it) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.updateContactOperation.error) {
        contactsState.updateContactOperation.error?.let {
            onShowSnackbar(it) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.blockContactOperation.error) {
        contactsState.blockContactOperation.error?.let {
            onShowSnackbar(it) // Use callback
            onResetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.error) {
        contactsState.unblockContactOperation.error?.let {
            onShowSnackbar(it) // Use callback
            onResetContactOperationStates()
        }
    }

    // UI components are now managed centrally by NavGraph

    // Removed Scaffold
    Box(modifier = Modifier.fillMaxSize()) { // Added Box to contain content
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            if (contacts.isEmpty() && !contactsState.properties.isLoading) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = stringResource(R.string.no_contacts),
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = stringResource(R.string.add_your_first_contact),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(bottom = 80.dp)
                ) {
                    if (showSearch) {
                        item {
                            ContactSearchBar(
                                query = searchQuery,
                                onQueryChange = { searchQuery = it },
                                onSearch = { onSearchContacts(it) },
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                    }

                    item {
                        RelationshipFilterChips(
                            selectedRelationship = contactsState.selectedRelationship,
                            onRelationshipSelected = { onFilterContactsByRelationship(it) },
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    if (searchQuery.isNotEmpty()) {
                        item {
                            Text(
                                text = stringResource(R.string.search_results),
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }
                        if (searchResults.isEmpty()) {
                            item {
                                Text(
                                    text = stringResource(R.string.no_contacts_found_for_query, searchQuery),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                                )
                            }
                        } else {
                            items(searchResults) { contact ->
                                SwipeToDelete(
                                    item = contact,
                                    onDelete = { onDeleteContact(it.id) }
                                ) { swipeContact ->
                                    ContactListItem(
                                        contact = swipeContact,
                                        onClick = { onNavigateToContactDetail(swipeContact.id) },
                                        onRemove = { onDeleteContact(swipeContact.id) },
                                        onEdit = { onNavigateToContactDetail(swipeContact.id) },
                                        onMessage = { /* TODO */ },
                                        onToggleFavorite = {
                                            if (swipeContact.isFavorite()) {
                                                onRemoveFromFavorites(swipeContact.id)
                                            } else {
                                                onAddToFavorites(swipeContact.id)
                                            }
                                        },
                                        onToggleBlocked = {
                                            if (swipeContact.isBlocked()) {
                                                onUnblockContact(swipeContact.id)
                                            } else {
                                                onBlockContact(swipeContact.id)
                                            }
                                        },
                                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    } else {
                        if (favoriteContacts.isNotEmpty()) {
                            item {
                                ContactSection(
                                    title = stringResource(R.string.favorites_section),
                                    contacts = favoriteContacts,
                                    onContactClick = { onNavigateToContactDetail(it.id) },
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }

                        if (recentContacts.isNotEmpty()) {
                            item {
                                ContactSection(
                                    title = stringResource(R.string.recent_section),
                                    contacts = recentContacts,
                                    onContactClick = { onNavigateToContactDetail(it.id) },
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }

                        item {
                            Text(
                                text = stringResource(R.string.all_contacts_section),
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }

                        items(filteredContacts) { contact ->
                            SwipeToDelete(
                                item = contact,
                                onDelete = { onDeleteContact(it.id) }
                            ) { swipeContact ->
                                ContactListItem(
                                    contact = swipeContact,
                                    onClick = { onNavigateToContactDetail(swipeContact.id) },
                                    onRemove = { onDeleteContact(swipeContact.id) },
                                    onEdit = { onNavigateToContactDetail(swipeContact.id) },
                                    onMessage = { /* TODO */ },
                                    onToggleFavorite = {
                                        if (swipeContact.isFavorite()) {
                                            onRemoveFromFavorites(swipeContact.id)
                                        } else {
                                            onAddToFavorites(swipeContact.id)
                                        }
                                    },
                                    onToggleBlocked = {
                                        if (swipeContact.isBlocked()) {
                                            onUnblockContact(swipeContact.id)
                                        } else {
                                            onBlockContact(swipeContact.id)
                                        }
                                    },
                                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        if (contactsState.properties.isLoading && contacts.isEmpty()) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}
