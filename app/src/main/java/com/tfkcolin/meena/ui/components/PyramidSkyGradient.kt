package com.tfkcolin.meena.ui.components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.theme.MeenaTheme
import kotlin.random.Random

/**
 * Data class to hold the state of a single dust particle for animation.
 * We'll update its 'x' and 'y' directly.
 */
private data class DustParticle(
    val initialX: Float,
    val initialY: Float,
    val speedX: Float,
    val speedY: Float,
    var currentX: Float, // This will be updated by the animation
    var currentY: Float  // This will be updated by the animation
)

@Composable
fun PyramidSkyGradient(modifier: Modifier = Modifier){

    // Custom background using Canvas with animations
    val infiniteTransition = rememberInfiniteTransition()

    // --- Cloud Animations ---
    val cloudOffsetX by infiniteTransition.animateFloat(
        initialValue = -300f, // Start further left
        targetValue = 1300f, // End further right
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 20000, easing = LinearEasing), // Slower movement
            repeatMode = RepeatMode.Restart
        )
    )
    val cloudOffsetY by infiniteTransition.animateFloat(
        initialValue = 50f,
        targetValue = 80f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 25000, easing = LinearEasing), // Slower vertical bobbing
            repeatMode = RepeatMode.Reverse
        )
    )
    val cloudScale1 by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3000), // Slower pulsating
            repeatMode = RepeatMode.Reverse
        )
    )
    val cloudScale2 by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 3500), // Slower pulsating
            repeatMode = RepeatMode.Reverse
        )
    )

    // --- Pyramid Shimmer Animation ---
    val pyramidShimmerOffset by infiniteTransition.animateFloat(
        initialValue = -0.3f, // Relative to pyramid width
        targetValue = 1.6f,   // Relative to pyramid width
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart // Or Reverse, depending on desired effect
        )
    )

    // --- Dust Particle Animations (Managed outside Canvas) ---
    val dustParticles = remember {
        List(15) { // More dust particles
            val initialX = Random.nextFloat() * 1000f
            val initialY = 0.6f + Random.nextFloat() * 0.4f // Relative to screen height
            DustParticle(
                initialX = initialX,
                initialY = initialY,
                speedX = (Random.nextFloat() * 0.5f + 0.1f) * (if (Random.nextBoolean()) 1f else -1f), // Slower movement, random direction
                speedY = Random.nextFloat() * 0.1f - 0.05f, // Slight vertical drift
                currentX = initialX,
                currentY = initialY
            )
        }
    }

    // Launch coroutines for each dust particle's animation
    dustParticles.forEach { particle ->
        val xAnimatable = remember { Animatable(particle.initialX) }
        val yAnimatable = remember { Animatable(particle.initialY) }

        LaunchedEffect(particle) {
            // Animate X
            xAnimatable.animateTo(
                targetValue = particle.initialX + particle.speedX * 500f, // Adjust multiplier for range
                animationSpec = infiniteRepeatable(
                    animation = tween(durationMillis = Random.nextInt(8000, 15000), easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse // Make them drift back and forth
                )
            )
        }
        LaunchedEffect(particle) {
            // Animate Y (subtle vertical drift)
            yAnimatable.animateTo(
                targetValue = particle.initialY + particle.speedY * 100f, // Adjust multiplier for range
                animationSpec = infiniteRepeatable(
                    animation = tween(durationMillis = Random.nextInt(7000, 12000), easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse
                )
            )
        }
        // Update the currentX/Y in the particle object itself so Canvas can read it
        // This is a bit of a hacky way to ensure the Canvas rediscovers the values,
        // better to pass them directly, as shown below.
        particle.currentX = xAnimatable.value
        particle.currentY = yAnimatable.value
    }


    Canvas(modifier = modifier) {
        val width = size.width
        val height = size.height

        // More nuanced gradient for the sky (sunrise/sunset feel)
        val skyGradient = Brush.verticalGradient(
            colors = listOf(
                Color(0xFF87CEEB), // Light blue at top
                Color(0xFFADD8E6), // Lighter blue
                Color(0xFFE0FFFF).copy(alpha = 0.8f), // Pale Cyan (horizon)
                Color(0xFFFFDAB9).copy(alpha = 0.7f), // Peach (lower horizon)
                Color(0xFFFFC0CB).copy(alpha = 0.5f) // Light pink (bottom of sky near dunes)
            ),
            startY = 0f,
            endY = height * 0.65f // Extend gradient further down
        )
        drawRect(skyGradient)

        // Animated Clouds (drawing based on animated values from outside)
        val cloudColor = Color.White.copy(alpha = 0.7f)
        drawCircle(
            color = cloudColor,
            center = Offset(cloudOffsetX, height * 0.1f + cloudOffsetY), // Using animated Y
            radius = 60.dp.toPx() * cloudScale1
        )
        drawCircle(
            color = cloudColor,
            center = Offset(cloudOffsetX + 300f, height * 0.05f + cloudOffsetY * 1.2f),
            radius = 45.dp.toPx() * cloudScale2
        )
        drawCircle(
            color = cloudColor,
            center = Offset(cloudOffsetX - 150f, height * 0.15f + cloudOffsetY * 0.8f),
            radius = 70.dp.toPx() * cloudScale1
        )
        // Add more clouds if desired
        drawCircle(
            color = cloudColor.copy(alpha = 0.5f),
            center = Offset(cloudOffsetX * 0.5f + width * 0.8f, height * 0.2f - cloudOffsetY * 0.5f),
            radius = 50.dp.toPx() * cloudScale2
        )


        // Detailed Sand Dunes
        val sandColor1 = Color(0xFFDEB887) // Base
        val sandColor2 = Color(0xFFC19A6F) // Deeper shade
        val sandColor3 = Color(0xFFA0522D) // Darker, for background dunes

        // Background dune
        val dune3Path = Path().apply {
            moveTo(0f, height * 0.7f)
            cubicTo(width * 0.3f, height * 0.6f, width * 0.7f, height * 0.8f, width, height * 0.7f)
            lineTo(width, height)
            lineTo(0f, height)
            close()
        }
        drawPath(dune3Path, sandColor3.copy(alpha = 0.7f))


        // Middle dune
        val dune2Path = Path().apply {
            moveTo(0f, height * 0.6f)
            cubicTo(width * 0.2f, height * 0.7f, width * 0.6f, height * 0.55f, width * 0.9f, height * 0.65f)
            cubicTo(width * 1.1f, height * 0.75f, width * 1.3f, height * 0.6f, width * 1.5f, height * 0.68f) // Extend beyond
            lineTo(width, height)
            lineTo(0f, height)
            close()
        }
        drawPath(dune2Path, sandColor2.copy(alpha = 0.9f))

        // Foreground dune
        val dune1Path = Path().apply {
            moveTo(0f, height * 0.75f)
            cubicTo(width * 0.25f, height * 0.85f, width * 0.5f, height * 0.7f, width * 0.75f, height * 0.8f)
            cubicTo(width * 0.9f, height * 0.85f, width * 1.0f, height * 0.75f, width * 1.1f, height * 0.8f) // Extend beyond
            lineTo(width, height)
            lineTo(0f, height)
            close()
        }
        drawPath(dune1Path, sandColor1)


        // Draw the pyramid with subtle shimmer
        val pyramidBaseWidth = width * 0.3f
        val pyramidHeight = height * 0.2f
        // Position pyramid on the middle dune
        val pyramidX = width / 2 - pyramidBaseWidth / 2
        val pyramidY = height * 0.6f - pyramidHeight * 1.2f // Adjust to sit on a dune

        val pyramidColorBase = Color(0xFFD2B48C)
        val pyramidPath = Path().apply {
            moveTo(pyramidX, pyramidY + pyramidHeight)
            lineTo(pyramidX + pyramidBaseWidth / 2, pyramidY)
            lineTo(pyramidX + pyramidBaseWidth, pyramidY + pyramidHeight)
            close()
        }
        drawPath(pyramidPath, pyramidColorBase)

        // Shimmer effect (drawn using animated value)
        clipPath(pyramidPath) {
            drawRect(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.Transparent,
                        Color(0xFFFFEBEE).copy(alpha = 0.4f), // Lighter, subtle shimmer
                        Color.Transparent
                    ),
                    start = Offset(pyramidX + pyramidBaseWidth * pyramidShimmerOffset, pyramidY),
                    end = Offset(pyramidX + pyramidBaseWidth * (pyramidShimmerOffset + 0.3f), pyramidY + pyramidHeight)
                )
            )
        }

        // Animated subtle dust particles (drawing based on current state of particles)
        dustParticles.forEach { particle ->
            drawCircle(
                color = Color(0xFFFFFACD).copy(alpha = 0.15f),
                radius = 2.dp.toPx(),
                center = Offset(particle.currentX, particle.currentY * size.height) // Scale Y
            )
        }
    }
}

@Preview(showBackground = true, widthDp = 500, heightDp = 1000)
@Composable
fun PyramidSkyGradientPreview() {
    MeenaTheme {
        PyramidSkyGradient()
    }
}