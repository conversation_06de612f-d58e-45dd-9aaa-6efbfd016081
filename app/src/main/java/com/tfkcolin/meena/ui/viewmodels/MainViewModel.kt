package com.tfkcolin.meena.ui.viewmodels

import androidx.compose.material3.SnackbarDuration
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(): ViewModel() {
    private val _snackbarEvents = Channel<SnackbarEvent>(Channel.BUFFERED)
    val snackbarEvents = _snackbarEvents.receiveAsFlow() // Expose as Flow

    fun showSnackbar(message: String, actionLabel: String? = null, duration: SnackbarDuration = SnackbarDuration.Short) {
        viewModelScope.launch {
            _snackbarEvents.send(SnackbarEvent.ShowMessage(message, actionLabel, duration))
        }
    }
}

sealed class SnackbarEvent {
    data class ShowMessage(val message: String, val actionLabel: String? = null, val duration: SnackbarDuration = SnackbarDuration.Short) : SnackbarEvent()
    // Add other types if needed, e.g., for errors, warnings
}