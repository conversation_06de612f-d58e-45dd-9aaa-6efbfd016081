package com.tfkcolin.meena.ui.chat

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.R
import com.tfkcolin.meena.domain.usecases.chat.AddGroupChatParticipantsUseCase
import com.tfkcolin.meena.domain.usecases.chat.CreateGroupChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.LeaveGroupChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.RemoveGroupChatParticipantsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateGroupChatAdminsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateGroupChatUseCase
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for group chat operations.
 */
@HiltViewModel
class GroupChatViewModel @Inject constructor(
    private val createGroupChatUseCase: CreateGroupChatUseCase,
    private val updateGroupChatUseCase: UpdateGroupChatUseCase,
    private val addGroupChatParticipantsUseCase: AddGroupChatParticipantsUseCase,
    private val removeGroupChatParticipantsUseCase: RemoveGroupChatParticipantsUseCase,
    private val updateGroupChatAdminsUseCase: UpdateGroupChatAdminsUseCase,
    private val leaveGroupChatUseCase: LeaveGroupChatUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for group chat operations
    private val _uiState = MutableStateFlow(GroupChatState())
    val uiState: StateFlow<GroupChatState> = _uiState.asStateFlow()

    /**
     * Create a new group chat.
     *
     * @param name The group name.
     * @param description The group description.
     * @param initialMembers The list of initial member user handles.
     * @param privacyType The privacy type.
     */
    fun createGroupChat(
        name: String,
        description: String?,
        initialMembers: List<String>,
        privacyType: String
    ) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                createGroupOperation = it.createGroupOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            executeUseCase(
                useCase = {
                    createGroupChatUseCase(
                        name = name,
                        description = description,
                        initialMembers = initialMembers,
                        privacyType = privacyType
                    )
                },
                onSuccess = { groupId ->
                    _uiState.update { it.copy(
                        createGroupOperation = it.createGroupOperation.success(),
                        newGroupChatId = groupId
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_group_create)
                    )
                    _uiState.update { it.copy(
                        createGroupOperation = it.createGroupOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(error, appContext.getString(R.string.error_group_create))
                },
                showLoading = false // We're managing loading state manually
            )
        }
    }

    /**
     * Update a group chat.
     *
     * @param groupId The group ID.
     * @param name The new group name.
     * @param description The new group description.
     * @param pictureUrl The new picture URL.
     * @param privacyType The new privacy type (not supported by the use case).
     */
    fun updateGroupChat(
        groupId: String,
        name: String?,
        description: String?,
        pictureUrl: String?,
        privacyType: String?
    ) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                updateGroupOperation = it.updateGroupOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            executeUseCase(
                useCase = {
                    updateGroupChatUseCase(
                        groupId = groupId,
                        name = name,
                        description = description,
                        pictureUrl = pictureUrl
                    )
                },
                onSuccess = {
                    _uiState.update { it.copy(
                        updateGroupOperation = it.updateGroupOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_group_update)
                    )
                    _uiState.update { it.copy(
                        updateGroupOperation = it.updateGroupOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(error, appContext.getString(R.string.error_group_update))
                },
                showLoading = false // We're managing loading state manually
            )
        }
    }

    /**
     * Add participants to a group chat.
     *
     * @param groupId The group ID.
     * @param userHandles The list of user handles to add.
     */
    fun addGroupChatParticipants(groupId: String, userHandles: List<String>) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                addParticipantsOperation = it.addParticipantsOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            executeUseCase(
                useCase = { addGroupChatParticipantsUseCase(groupId, userHandles) },
                onSuccess = {
                    _uiState.update { it.copy(
                        addParticipantsOperation = it.addParticipantsOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_group_update)
                    )
                    _uiState.update { it.copy(
                        addParticipantsOperation = it.addParticipantsOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(error, appContext.getString(R.string.error_group_update))
                },
                showLoading = false
            )
        }
    }

    /**
     * Remove participants from a group chat.
     *
     * @param groupId The group ID.
     * @param userHandles The list of user handles to remove.
     */
    fun removeGroupChatParticipants(groupId: String, userHandles: List<String>) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                removeParticipantsOperation = it.removeParticipantsOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            // Process each participant one by one since the use case only supports removing one at a time
            var allSuccessful = true
            var lastError: Throwable? = null

            for (userHandle in userHandles) {
                try {
                    val result = removeGroupChatParticipantsUseCase(groupId, userHandle)
                    result.fold(
                        onSuccess = { /* Continue to the next participant */ },
                        onFailure = { error ->
                            allSuccessful = false
                            lastError = error
                            throw error // This will be caught by the outer try-catch
                        }
                    )
                } catch (e: Exception) {
                    allSuccessful = false
                    lastError = e
                    break // Stop processing on first error
                }
            }

            if (allSuccessful) {
                _uiState.update { it.copy(
                    removeParticipantsOperation = it.removeParticipantsOperation.success()
                ) }
            } else {
                val errorMessage = errorHandler.getErrorMessage(
                    lastError ?: Exception("Unknown error"),
                    appContext.getString(R.string.error_group_update)
                )
                _uiState.update { it.copy(
                    removeParticipantsOperation = it.removeParticipantsOperation.failure(errorMessage),
                    properties = it.properties.copy(error = errorMessage)
                ) }
                setError(errorMessage)
            }
        }
    }

    /**
     * Update the admins of a group chat.
     *
     * @param groupId The group ID.
     * @param userHandles The list of user handles to make admins.
     */
    fun updateGroupChatAdmins(groupId: String, userHandles: List<String>) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                updateAdminsOperation = it.updateAdminsOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            // Process each admin one by one since the use case only supports updating one at a time
            var allSuccessful = true
            var lastError: Throwable? = null

            for (userHandle in userHandles) {
                try {
                    val result = updateGroupChatAdminsUseCase(
                        groupId = groupId,
                        userHandle = userHandle,
                        role = "admin" // Set the role to "admin" for all users in the list
                    )

                    result.fold(
                        onSuccess = { /* Continue to the next admin */ },
                        onFailure = { error ->
                            allSuccessful = false
                            lastError = error
                            throw error // This will be caught by the outer try-catch
                        }
                    )
                } catch (e: Exception) {
                    allSuccessful = false
                    lastError = e
                    break // Stop processing on first error
                }
            }

            if (allSuccessful) {
                _uiState.update { it.copy(
                    updateAdminsOperation = it.updateAdminsOperation.success()
                ) }
            } else {
                val errorMessage = errorHandler.getErrorMessage(
                    lastError ?: Exception("Unknown error"),
                    appContext.getString(R.string.error_group_update)
                )
                _uiState.update { it.copy(
                    updateAdminsOperation = it.updateAdminsOperation.failure(errorMessage),
                    properties = it.properties.copy(error = errorMessage)
                ) }
                setError(errorMessage)
            }
        }
    }

    /**
     * Leave a group chat.
     *
     * @param groupId The group ID.
     */
    fun leaveGroupChat(groupId: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                leaveGroupOperation = it.leaveGroupOperation.start(),
                properties = it.properties.copy(error = null)
            ) }

            executeUseCase(
                useCase = { leaveGroupChatUseCase(groupId) },
                onSuccess = {
                    _uiState.update { it.copy(
                        leaveGroupOperation = it.leaveGroupOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_group_leave)
                    )
                    _uiState.update { it.copy(
                        leaveGroupOperation = it.leaveGroupOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(error, appContext.getString(R.string.error_group_leave))
                },
                showLoading = false
            )
        }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _uiState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
    }

    /**
     * Reset group chat operation states.
     */
    fun resetGroupChatOperationStates() {
        _uiState.update { it.resetAllOperations() }
    }
}


