package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.tfkcolin.meena.R

@Composable
fun UserAvatar(
    imageUrl: String,
    userName: String,
    size: Dp = 40.dp,
    isOnline: Boolean = false,
    borderWidth: Dp = 2.dp,
    borderColor: Color = MaterialTheme.colorScheme.primary,
    onlineIndicatorColor: Color = Color.Green,
    onClick: (() -> Unit)? = null
) {
    Box(
        modifier = Modifier
            .size(size)
            .then(
                if (onClick != null) {
                    Modifier.clickable(
                        onClick = onClick
                    )
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.BottomEnd
    ) {
        // Subtle shadow effect for depth
        Box(
            modifier = Modifier
                .size(size)
                .clip(CircleShape)
                .shadow(2.dp, CircleShape)
        ) {
            // Avatar image with fallback
            AsyncImage(
                model = imageUrl,
                contentDescription = "Avatar for $userName",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .border(borderWidth, borderColor, CircleShape),
                error = painterResource(id = R.drawable.baseline_person_24),
                placeholder = painterResource(id = R.drawable.baseline_person_24)
            )
        }

        // Online status indicator
        if (isOnline) {
            Box(
                modifier = Modifier
                    .size(size * 0.3f)
                    .offset(x = (-size * 0.05f), y = (-size * 0.05f))
                    .background(
                        color = MaterialTheme.colorScheme.surface,
                        shape = CircleShape
                    )
                    .padding(2.dp)
                    .background(onlineIndicatorColor, CircleShape)
                    .border(
                        width = borderWidth / 2,
                        color = MaterialTheme.colorScheme.surface,
                        shape = CircleShape
                    )
            )
        }
    }
}