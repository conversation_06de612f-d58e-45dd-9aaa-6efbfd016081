package com.tfkcolin.meena.ui.settings

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.preferences.UserPreferences
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for settings screens.
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userPreferences: UserPreferences,
    private val mockDataStorage: MockDataStorage, // Inject MockDataStorage
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for settings
    private val _settingsState = MutableStateFlow(SettingsState())
    val settingsState: StateFlow<SettingsState> = _settingsState.asStateFlow()

    init {
        // Load settings
        loadSettings()
    }

    /**
     * Load settings from preferences.
     */
    private fun loadSettings() {
        viewModelScope.launch {
            try {
                _settingsState.update { 
                    it.copy(
                        theme = userPreferences.getTheme(),
                        language = userPreferences.getLanguage(),
                        notificationsEnabled = userPreferences.areNotificationsEnabled(),
                        messagePreviewsEnabled = userPreferences.areMessagePreviewsEnabled(),
                        readReceiptsEnabled = userPreferences.areReadReceiptsEnabled(),
                        typingIndicatorsEnabled = userPreferences.areTypingIndicatorsEnabled(),
                        properties = it.properties.copy(isLoading = false)
                    )
                }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update theme setting.
     */
    fun updateTheme(theme: String) {
        viewModelScope.launch {
            try {
                userPreferences.saveTheme(theme)
                _settingsState.update { it.copy(theme = theme) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update language setting.
     */
    fun updateLanguage(language: String) {
        viewModelScope.launch {
            try {
                userPreferences.saveLanguage(language)
                _settingsState.update { it.copy(language = language) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update notifications setting.
     */
    fun updateNotificationsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                userPreferences.setNotificationsEnabled(enabled)
                _settingsState.update { it.copy(notificationsEnabled = enabled) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update message previews setting.
     */
    fun updateMessagePreviewsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                userPreferences.setMessagePreviewsEnabled(enabled)
                _settingsState.update { it.copy(messagePreviewsEnabled = enabled) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update read receipts setting.
     */
    fun updateReadReceiptsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                userPreferences.setReadReceiptsEnabled(enabled)
                _settingsState.update { it.copy(readReceiptsEnabled = enabled) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Update typing indicators setting.
     */
    fun updateTypingIndicatorsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                userPreferences.setTypingIndicatorsEnabled(enabled)
                _settingsState.update { it.copy(typingIndicatorsEnabled = enabled) }
            } catch (e: Exception) {
                setError(e)
            }
        }
    }

    /**
     * Reset all mock data in the application.
     */
    fun resetMockData() {
        viewModelScope.launch {
            _settingsState.update { it.copy(
                resetMockDataOperation = it.resetMockDataOperation.start()
            ) }
            try {
                mockDataStorage.clearAllData()
                // Re-initialize mock data after clearing, to ensure a current user exists
                mockDataStorage.initializeIfNeeded()
                _settingsState.update { it.copy(
                    resetMockDataOperation = it.resetMockDataOperation.success(),
                    infoMessage = "Mock data reset successfully. App may restart."
                ) }
            } catch (e: Exception) {
                _settingsState.update { it.copy(
                    resetMockDataOperation = it.resetMockDataOperation.failure(
                        errorHandler.getErrorMessage(e, "Failed to reset mock data.")
                    )
                ) }
                setError(e)
            }
        }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _settingsState.update {
            it.copy(
                properties = it.properties.copy(error = null)
            )
        }
    }
}

/**
 * State for settings screens.
 */
data class SettingsState(
    val theme: String = "system",
    val language: String = "en",
    val notificationsEnabled: Boolean = true,
    val messagePreviewsEnabled: Boolean = true,
    val readReceiptsEnabled: Boolean = true,
    val typingIndicatorsEnabled: Boolean = true,
    val resetMockDataOperation: OperationState = OperationState(),
    val infoMessage: String? = null,
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
