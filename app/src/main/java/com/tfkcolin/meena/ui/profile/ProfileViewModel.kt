package com.tfkcolin.meena.ui.profile

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.domain.repositories.IAuthRepository
import com.tfkcolin.meena.domain.repositories.IUserRepository
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.TokenManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the profile screen.
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val userRepository: IUserRepository,
    private val authRepository: IAuthRepository,
    private val tokenManager: TokenManager,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for profile
    private val _profileState = MutableStateFlow(ProfileState())
    val profileState: StateFlow<ProfileState> = _profileState.asStateFlow()

    init {
        // Load user data
        loadUserData()
    }

    /**
     * Load user data from repository.
     */
    fun loadUserData() {
        viewModelScope.launch {
            _profileState.update { it.copy(properties = it.properties.copy(isLoading = true)) }

            userRepository.getCurrentUser().collect { user ->
                _profileState.update {
                    it.copy(
                        user = user,
                        properties = it.properties.copy(isLoading = false)
                    )
                }
            }
        }
    }

    /**
     * Update user profile.
     */
    fun updateProfile(displayName: String, bio: String) {
        viewModelScope.launch {
            _profileState.update { it.copy(properties = it.properties.copy(isLoading = true)) }

            try {
                // Get the current user
                val currentUser = _profileState.value.user

                if (currentUser != null) {
                    // Create updated user object
                    val updatedUser = currentUser.copy(
                        displayName = displayName,
                        bio = bio
                    )

                    // Call the repository to update the profile
                    val result = userRepository.updateProfile(
                        displayName = displayName,
                        bio = bio
                    )

                    // Check the result
                    if (result.isSuccess) {
                        // The user will be updated in the state via the getCurrentUser flow
                    } else {
                        // If there's an error, throw it to be caught by the catch block
                        result.exceptionOrNull()?.let { throw it }
                    }

                    // Update the local state
                    _profileState.update {
                        it.copy(
                            user = updatedUser,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }
                } else {
                    _profileState.update {
                        it.copy(
                            properties = it.properties.copy(
                                isLoading = false,
                                error = "User not found"
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                setError(e)
                _profileState.update { it.copy(properties = it.properties.copy(isLoading = false)) }
            }
        }
    }

    /**
     * Log out the user.
     */
    fun logout() {
        viewModelScope.launch {
            tokenManager.clearTokens()
            _profileState.update { it.copy(isLoggedOut = true) }
        }
    }

    /**
     * Get the user's Meena ID.
     */
    fun getMeenaId(): String {
        return _profileState.value.user?.userHandle ?: ""
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _profileState.update {
            it.copy(
                properties = it.properties.copy(error = null)
            )
        }
    }
}

/**
 * State for profile screen.
 */
data class ProfileState(
    val user: User? = null,
    val isLoggedOut: Boolean = false,
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
