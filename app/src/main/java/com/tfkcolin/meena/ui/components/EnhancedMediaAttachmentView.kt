package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AudioFile
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Upload
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaType
import com.tfkcolin.meena.data.models.UploadProgress
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import java.io.File

/**
 * Enhanced media attachment view component with download and upload progress indicators.
 *
 * @param attachment The media attachment to display.
 * @param onClick The click handler.
 * @param onDownload The download handler.
 * @param modifier The modifier for the component.
 * @param viewModel The media view model.
 */
@Composable
fun EnhancedMediaAttachmentView(
    modifier: Modifier = Modifier,
    attachment: MediaAttachment,
    onClick: () -> Unit,
    onDownload: (() -> Unit)? = null,
    mediaAttachmentHelper: MediaAttachmentHelper, // Added helper instance
    viewModel: MediaViewModel = hiltViewModel()
) {
    val context = LocalContext.current

    // Get upload and download progress
    val uploadProgress by viewModel.getUploadProgress(attachment.id)
        .collectAsState(initial = null)
    val downloadProgress by viewModel.getDownloadProgress(attachment.id)
        .collectAsState(initial = null)

    // Safe type checking for progress
    val uploadingProgress = uploadProgress as? UploadProgress.Uploading
    val uploadError = uploadProgress as? UploadProgress.Error
    val downloadingProgress = downloadProgress as? DownloadProgress.Downloading
    val downloadError = downloadProgress as? DownloadProgress.Error

    // Check if the file is cached
    val cachedFile = viewModel.getCachedFile(attachment)
    val isFileCached = remember(cachedFile) { cachedFile.exists() }

    // Determine if we need to show download button
    val showDownloadButton = !isFileCached &&
        downloadProgress == null &&
        onDownload != null &&
        attachment.url.startsWith("http")

    when (attachment.type) {
        MediaType.IMAGE.name.lowercase() -> {
            // Image attachment
            Box(
                modifier = modifier
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(if (isFileCached) cachedFile else attachment.url)
                        .crossfade(true)
                        .build(),
                    contentDescription = attachment.name ?: "Image",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1.5f)
                )

                // Show progress indicators
                when {
                    uploadingProgress != null -> {
                        UploadProgressIndicator(
                            progress = uploadingProgress.progress
                        )
                    }
                    uploadError != null -> {
                        ErrorIndicator(
                            error = uploadError.error
                        )
                    }
                    downloadingProgress != null -> {
                        DownloadProgressIndicator(
                            progress = downloadingProgress.progress
                        )
                    }
                    downloadError != null -> {
                        ErrorIndicator(
                            error = downloadError.error
                        )
                    }
                    showDownloadButton -> {
                        DownloadButton(onClick = onDownload!!)
                    }
                }
            }
        }
        MediaType.VIDEO.name.lowercase() -> {
            // Video attachment
            Box(
                modifier = modifier
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(if (isFileCached && attachment.thumbnailUrl == null)
                            cachedFile
                        else
                            attachment.thumbnailUrl ?: attachment.url
                        )
                        .crossfade(true)
                        .build(),
                    contentDescription = attachment.name ?: "Video",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1.5f)
                )

                // Play button overlay
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.3f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "Play",
                        tint = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                }

                // Duration
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(8.dp)
                        .background(
                            color = Color.Black.copy(alpha = 0.6f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 4.dp, vertical = 2.dp)
                ) {
                    Text(
                        text = mediaAttachmentHelper.formatDuration(attachment.duration),
                        color = Color.White,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // Show progress indicators
                when {
                    uploadingProgress != null -> {
                        UploadProgressIndicator(
                            progress = uploadingProgress.progress
                        )
                    }
                    uploadError != null -> {
                        ErrorIndicator(
                            error = uploadError.error
                        )
                    }
                    downloadingProgress != null -> {
                        DownloadProgressIndicator(
                            progress = downloadingProgress.progress
                        )
                    }
                    downloadError != null -> {
                        ErrorIndicator(
                            error = downloadError.error
                        )
                    }
                    showDownloadButton -> {
                        DownloadButton(onClick = onDownload!!)
                    }
                }
            }
        }
        MediaType.AUDIO.name.lowercase() -> {
            // Audio attachment
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.AudioFile,
                    contentDescription = "Audio",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(40.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = attachment.name ?: "Audio file",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = mediaAttachmentHelper.formatDuration(attachment.duration),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )

                        Text(
                            text = mediaAttachmentHelper.formatFileSize(attachment.size),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }

                    // Show progress indicators
                    when {
                        uploadingProgress != null -> {
                            Spacer(modifier = Modifier.height(4.dp))
                            LinearProgressIndicator(
                            progress = { uploadingProgress.progress / 100f },
                            modifier = Modifier.fillMaxWidth(),
                            )
                        }
                        downloadingProgress != null -> {
                            Spacer(modifier = Modifier.height(4.dp))
                            LinearProgressIndicator(
                            progress = { downloadingProgress.progress / 100f },
                            modifier = Modifier.fillMaxWidth(),
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))

                when {
                    uploadingProgress != null -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    uploadError != null -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Error",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    downloadingProgress != null -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    downloadError != null -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Error",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    showDownloadButton -> {
                        IconButton(
                            onClick = onDownload!!,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Download,
                                contentDescription = "Download",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                    else -> {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Play",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
        MediaType.DOCUMENT.name.lowercase() -> {
            // Document attachment
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Description,
                    contentDescription = "Document",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(40.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = attachment.name ?: "Document",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = mediaAttachmentHelper.formatFileSize(attachment.size),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )

                    // Show progress indicators
                    when {
                        uploadingProgress != null -> {
                            Spacer(modifier = Modifier.height(4.dp))
                            LinearProgressIndicator(
                            progress = { uploadingProgress.progress / 100f },
                            modifier = Modifier.fillMaxWidth(),
                            )
                        }
                        downloadingProgress != null -> {
                            Spacer(modifier = Modifier.height(4.dp))
                            LinearProgressIndicator(
                            progress = { downloadingProgress.progress / 100f },
                            modifier = Modifier.fillMaxWidth(),
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))

                when {
                    uploadingProgress != null -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    uploadError != null -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Error",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    downloadingProgress != null -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    downloadError != null -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Error",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    showDownloadButton -> {
                        IconButton(
                            onClick = onDownload!!,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Download,
                                contentDescription = "Download",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
        }
        MediaType.LOCATION.name.lowercase() -> {
            // Location attachment
            Box(
                modifier = modifier
                    .fillMaxWidth()
                    .aspectRatio(1.5f)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
                    .background(MaterialTheme.colorScheme.surfaceVariant),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = "Location",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(48.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Location",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )

                    if (attachment.latitude != null && attachment.longitude != null) {
                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = "Lat: ${attachment.latitude}, Lng: ${attachment.longitude}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        }
        else -> {
            // Unknown attachment type
            Box(
                modifier = modifier
                    .fillMaxWidth()
                    .height(100.dp)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onClick)
                    .padding(12.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Unknown attachment type",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * Upload progress indicator component.
 *
 * @param progress The upload progress as a percentage (0-100).
 */
@Composable
private fun UploadProgressIndicator(progress: Int) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
            progress = { progress / 100f },
            modifier = Modifier.size(48.dp),
            color = Color.White,
            trackColor = Color.White.copy(alpha = 0.3f),
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Uploading $progress%",
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * Download progress indicator component.
 *
 * @param progress The download progress as a percentage (0-100).
 */
@Composable
private fun DownloadProgressIndicator(progress: Int) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
            progress = { progress / 100f },
            modifier = Modifier.size(48.dp),
            color = Color.White,
            trackColor = Color.White.copy(alpha = 0.3f),
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Downloading $progress%",
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * Error indicator component.
 *
 * @param error The error message.
 */
@Composable
private fun ErrorIndicator(error: String) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "Error",
                tint = Color.White,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Error: $error",
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * Download button component.
 *
 * @param onClick The click handler.
 */
@Composable
private fun DownloadButton(onClick: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.3f)),
        contentAlignment = Alignment.Center
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier
                .size(56.dp)
                .background(
                    color = MaterialTheme.colorScheme.primary,
                    shape = CircleShape
                )
        ) {
            Icon(
                imageVector = Icons.Default.Download,
                contentDescription = "Download",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}
