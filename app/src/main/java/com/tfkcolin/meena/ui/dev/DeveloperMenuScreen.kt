package com.tfkcolin.meena.ui.dev

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.config.AppConfig

/**
 * Developer menu for controlling mock backend behavior and testing features.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeveloperMenuScreen(
    onNavigateBack: () -> Unit,
    viewModel: DeveloperMenuViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Developer Menu") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Mock Backend Section
            item {
                DeveloperSection(
                    title = "Mock Backend",
                    icon = Icons.Default.Settings
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Use Mock Backend")
                            Switch(
                                checked = uiState.useMockBackend,
                                onCheckedChange = { viewModel.toggleMockBackend() }
                            )
                        }
                        
                        if (uiState.useMockBackend) {
                            Button(
                                onClick = { viewModel.resetMockData() },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(Icons.Default.Refresh, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Reset Mock Data")
                            }
                        }
                    }
                }
            }
            
            // AI Chat Simulation Section
            if (uiState.useMockBackend) {
                item {
                    DeveloperSection(
                        title = "AI Chat Simulation",
                        icon = Icons.Default.SmartToy
                    ) {
                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Enable AI Responses")
                                Switch(
                                    checked = uiState.aiResponsesEnabled,
                                    onCheckedChange = { viewModel.toggleAIResponses() }
                                )
                            }
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text("Show Typing Indicators")
                                Switch(
                                    checked = uiState.typingIndicatorsEnabled,
                                    onCheckedChange = { viewModel.toggleTypingIndicators() }
                                )
                            }
                        }
                    }
                }
            }
            
            // Mock Data Info Section
            if (uiState.useMockBackend) {
                item {
                    DeveloperSection(
                        title = "Mock Data Statistics",
                        icon = Icons.Default.Analytics
                    ) {
                        Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                            InfoRow("Users", uiState.mockStats.userCount.toString())
                            InfoRow("Chats", uiState.mockStats.chatCount.toString())
                            InfoRow("Messages", uiState.mockStats.messageCount.toString())
                            InfoRow("Contacts", uiState.mockStats.contactCount.toString())
                            InfoRow("Current User", uiState.mockStats.currentUserHandle)
                        }
                    }
                }
            }
            
            // Feature Flags Section
            item {
                DeveloperSection(
                    title = "Feature Flags",
                    icon = Icons.Default.Flag
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        FeatureFlagRow("Stories", uiState.featureFlags.storiesEnabled) {
                            viewModel.toggleFeatureFlag("stories")
                        }
                        FeatureFlagRow("Calls", uiState.featureFlags.callsEnabled) {
                            viewModel.toggleFeatureFlag("calls")
                        }
                        FeatureFlagRow("Group Chats", uiState.featureFlags.groupChatsEnabled) {
                            viewModel.toggleFeatureFlag("group_chats")
                        }
                        FeatureFlagRow("Channels", uiState.featureFlags.channelsEnabled) {
                            viewModel.toggleFeatureFlag("channels")
                        }
                        FeatureFlagRow("Media Messages", uiState.featureFlags.mediaMessagesEnabled) {
                            viewModel.toggleFeatureFlag("media_messages")
                        }
                    }
                }
            }
            
            // Debug Actions Section
            item {
                DeveloperSection(
                    title = "Debug Actions",
                    icon = Icons.Default.BugReport
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        Button(
                            onClick = { viewModel.generateTestData() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Generate Test Data")
                        }
                        
                        Button(
                            onClick = { viewModel.simulateIncomingMessage() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(Icons.Default.Message, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Simulate Incoming Message")
                        }
                        
                        Button(
                            onClick = { viewModel.clearAppData() },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.DeleteForever, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Clear All App Data")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DeveloperSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            content()
        }
    }
}

@Composable
private fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun FeatureFlagRow(
    label: String,
    enabled: Boolean,
    onToggle: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(label)
        Switch(
            checked = enabled,
            onCheckedChange = { onToggle() }
        )
    }
}
