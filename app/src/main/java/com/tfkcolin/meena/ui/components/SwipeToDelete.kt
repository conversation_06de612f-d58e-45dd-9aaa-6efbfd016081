package com.tfkcolin.meena.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SwipeToDismissBox
import androidx.compose.material3.SwipeToDismissBoxState
import androidx.compose.material3.SwipeToDismissBoxValue
import androidx.compose.material3.rememberSwipeToDismissBoxState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * A composable that provides swipe-to-delete functionality using Material 3's SwipeToDismissBox.
 *
 * @param item The item to be displayed and potentially deleted.
 * @param onDelete The callback to be invoked when the item is deleted.
 * @param content The content to be displayed for the item.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> SwipeToDelete(
    item: T,
    onDelete: (T) -> Unit,
    content: @Composable RowScope.(T) -> Unit // SwipeToDismissBox's content is a RowScope
) {
    var isRemoved by remember { mutableStateOf(false) }
    val dismissState = rememberSwipeToDismissBoxState(
        confirmValueChange = { dismissValue ->
            if (dismissValue == SwipeToDismissBoxValue.EndToStart) { // Changed to EndToStart for right-to-left swipe
                isRemoved = true
                true
            } else {
                false
            }
        },
        // Optional: Set positional threshold if needed, e.g., FractionalThreshold(0.5f)
    )

    LaunchedEffect(isRemoved) {
        if (isRemoved) {
            delay(300) // Animation duration
            onDelete(item)
        }
    }

    AnimatedVisibility(
        visible = !isRemoved,
        exit = shrinkHorizontally(
            animationSpec = tween(durationMillis = 300),
            shrinkTowards = Alignment.Start
        ) + fadeOut(animationSpec = tween(durationMillis = 300))
    ) {
        SwipeToDismissBox(
            state = dismissState,
            backgroundContent = { DeleteBackground(dismissState) },
            content = { content(item) },
            enableDismissFromStartToEnd = false, // Disables swipe from left to right
            enableDismissFromEndToStart = true // Enables swipe from right to left
        )
    }
}

/**
 * The background shown when swiping to delete.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DeleteBackground(dismissState: SwipeToDismissBoxState) {
    val color = when (dismissState.dismissDirection) {
        SwipeToDismissBoxValue.EndToStart -> MaterialTheme.colorScheme.errorContainer // M3 uses errorContainer typically
        else -> Color.Transparent
    }

    val alignment = when (dismissState.dismissDirection) {
        SwipeToDismissBoxValue.EndToStart -> Alignment.CenterEnd
        else -> Alignment.CenterStart // Default to start if not dismissing or other directions allowed
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color)
            .padding(horizontal = 16.dp),
        contentAlignment = alignment
    ) {
        Icon(
            imageVector = Icons.Default.Delete,
            contentDescription = "Delete",
            tint = MaterialTheme.colorScheme.onErrorContainer // M3 uses onErrorContainer for content on errorContainer
        )
    }
}