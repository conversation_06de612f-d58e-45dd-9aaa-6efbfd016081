package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Screen for displaying the generated secret recovery phrase.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param recoveryPhrase The secret recovery phrase to display.
 * @param confirmationChecked The current state of the confirmation checkbox.
 * @param onConfirmationChange Callback for when the confirmation checkbox state changes.
 * @param onContinueClick Callback to continue to the next screen.
 */
@Composable
fun SecretPhraseScreen(
    onNavigateBack: () -> Unit,
    recoveryPhrase: String?,
    confirmationChecked: Boolean,
    onConfirmationChange: (Boolean) -> Unit,
    onContinueClick: () -> Unit
) {
    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp) // Adjusted vertical padding
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.your_secret_recovery_phrase_headline), // Extracted
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = stringResource(R.string.secret_phrase_instruction), // Extracted
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f)
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // Secret phrase display
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                color = MaterialTheme.colorScheme.surfaceVariant,
                shape = MaterialTheme.shapes.medium
            ) {
                Text(
                    text = recoveryPhrase ?: stringResource(R.string.loading_text), // Extracted
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(24.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Warning text
            Text(
                text = stringResource(R.string.secret_phrase_important_warning), // Extracted
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.error
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            // Confirmation checkbox
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 32.dp)
            ) {
                Checkbox(
                    checked = confirmationChecked,
                    onCheckedChange = onConfirmationChange,
                    colors = CheckboxDefaults.colors(
                        checkedColor = MaterialTheme.colorScheme.primary,
                        uncheckedColor = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.6f)
                    )
                )
                Text(
                    text = stringResource(R.string.secret_phrase_confirmation_checkbox), // Extracted
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                )
            }

            // Continue button
            MeenaPrimaryButton(
                text = stringResource(R.string.continue_button), // Extracted (and previously identified as duplicate)
                onClick = onContinueClick,
                enabled = confirmationChecked,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SecretPhraseScreenPreview() {
    MeenaTheme {
        SecretPhraseScreen(
            onNavigateBack = {},
            recoveryPhrase = "word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12",
            confirmationChecked = true,
            onConfirmationChange = {},
            onContinueClick = {},
        )
    }
}