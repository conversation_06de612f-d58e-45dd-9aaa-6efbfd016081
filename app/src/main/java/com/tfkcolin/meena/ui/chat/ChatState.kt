package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * Combined UI state for chat operations.
 * Aggregates states from specialized ViewModels.
 */
data class ChatState(
    // Base state properties (composition)
    val properties: UiStateProperties = UiStateProperties(),

    // Conversation list state
    val conversationListState: ConversationListState = ConversationListState(),

    // Message state
    val messageState: MessageState = MessageState(),

    // Group chat state
    val groupChatState: GroupChatState = GroupChatState()
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error

    // Convenience properties for commonly accessed state

    // Loading states
    val isSending: Boolean get() = messageState.sendMessageOperation.isInProgress
    val isSearching: Boolean get() = messageState.searchMessagesOperation.isInProgress
    val isEditing: Boolean get() = messageState.editMessageOperation.isInProgress
    val isDeleting: Boolean get() = messageState.deleteMessageOperation.isInProgress
    val isArchiving: Boolean get() = conversationListState.archiveConversationOperation.isInProgress
    val isMuting: Boolean get() = conversationListState.muteConversationOperation.isInProgress
    val isPinning: Boolean get() = conversationListState.pinConversationOperation.isInProgress
    val isForwarding: Boolean get() = messageState.forwardMessageOperation.isInProgress
    val isCreatingGroup: Boolean get() = groupChatState.createGroupOperation.isInProgress
    val isUpdatingGroup: Boolean get() = groupChatState.updateGroupOperation.isInProgress
    val isAddingParticipants: Boolean get() = groupChatState.addParticipantsOperation.isInProgress
    val isRemovingParticipants: Boolean get() = groupChatState.removeParticipantsOperation.isInProgress
    val isUpdatingAdmins: Boolean get() = groupChatState.updateAdminsOperation.isInProgress
    val isLeavingGroup: Boolean get() = groupChatState.leaveGroupOperation.isInProgress

    // Success states
    val isMessageSent: Boolean get() = messageState.sendMessageOperation.isSuccessful
    val isMessageEdited: Boolean get() = messageState.editMessageOperation.isSuccessful
    val isMessageDeleted: Boolean get() = messageState.deleteMessageOperation.isSuccessful
    val isMessageForwarded: Boolean get() = messageState.forwardMessageOperation.isSuccessful
    val isChatArchived: Boolean get() = conversationListState.archiveConversationOperation.isSuccessful
    val isChatMuted: Boolean get() = conversationListState.muteConversationOperation.isSuccessful
    val isChatPinned: Boolean get() = conversationListState.pinConversationOperation.isSuccessful
    val isGroupCreated: Boolean get() = groupChatState.createGroupOperation.isSuccessful
    val isGroupUpdated: Boolean get() = groupChatState.updateGroupOperation.isSuccessful
    val areParticipantsAdded: Boolean get() = groupChatState.addParticipantsOperation.isSuccessful
    val areParticipantsRemoved: Boolean get() = groupChatState.removeParticipantsOperation.isSuccessful
    val areAdminsUpdated: Boolean get() = groupChatState.updateAdminsOperation.isSuccessful
    val isGroupLeft: Boolean get() = groupChatState.leaveGroupOperation.isSuccessful

    // Data
    val totalChatCount: Int get() = conversationListState.totalChatCount
    val totalMessageCount: Int get() = messageState.totalMessageCount
    val showArchivedChats: Boolean get() = conversationListState.showArchivedChats
    val newChatId: String? get() = conversationListState.newChatId
    val newGroupChatId: String? get() = groupChatState.newGroupChatId
    val selectedAttachments: List<MediaAttachment> get() = messageState.selectedAttachments
    val searchQuery: String get() = messageState.searchQuery
    val searchResults: List<Message> get() = messageState.searchResults
    val messageToEdit: Message? get() = messageState.messageToEdit
}

/**
 * Extension function to check if any operation is in progress.
 */
fun ChatState.isAnyOperationInProgress(): Boolean {
    return conversationListState.isAnyOperationInProgress() ||
            messageState.isAnyOperationInProgress() ||
            groupChatState.isAnyOperationInProgress()
}

/**
 * Extension function to reset all operation states.
 */
fun ChatState.resetAllOperations(): ChatState {
    return copy(
        properties = properties.copy(error = null),
        conversationListState = conversationListState.resetAllOperations(),
        messageState = messageState.resetAllOperations(),
        groupChatState = groupChatState.resetAllOperations()
    )
}
