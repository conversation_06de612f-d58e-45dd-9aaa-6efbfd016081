package com.tfkcolin.meena.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.SettingsItem

private val SupportedLanguages = listOf(
    "en" to "English",
    "es" to "Español (Spanish)",
    "fr" to "Français (French)",
    "de" to "Deutsch (German)",
    "zh" to "中文 (Chinese)",
    "ja" to "日本語 (Japanese)",
//    "ko" to "한국어 (Korean)",
    "ar" to "العربية (Arabic)",
//    "hi" to "हिन्दी (Hindi)",
    "pt" to "Português (Portuguese)"
)

/**
 * Language settings screen.
 * Allows users to change the app language.
 *
 * @param onNavigateBack Navigate back to the profile screen.
 * @param currentLanguage Current selected language code.
 * @param onUpdateLanguage Callback to update the language.
 * @param error Error message to display, if any.
 * @param onClearError Callback to clear the error message.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSettingsScreen(
    onShowSnackbar: (String) -> Unit,
    onNavigateBack: () -> Unit,
    currentLanguage: String,
    onUpdateLanguage: (String) -> Unit,
    error: String?,
    onClearError: () -> Unit
) {
    val scrollState = rememberScrollState()

    // Show error message
    LaunchedEffect(error) {
        error?.let {
            onShowSnackbar(it)
            onClearError()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        // Language section
        Text(
            text = stringResource(R.string.select_language),
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = stringResource(R.string.choose_your_preferred_language_for_the_app),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        // Language options
        SupportedLanguages.forEach { (code, name) ->
            LanguageOption(
                name = name,
                isSelected = currentLanguage == code,
                onClick = { onUpdateLanguage(code) }
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * Language option item.
 *
 * @param name The name of the language.
 * @param isSelected Whether the language is selected.
 * @param onClick The action to perform when the language is clicked.
 */
@Composable
private fun LanguageOption(
    name: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    SettingsItem(
        title = name,
        onClick = onClick,
        endContent = {
            RadioButton(
                selected = isSelected,
                onClick = onClick
            )
        }
    )
}
