package com.tfkcolin.meena.ui.base

/**
 * Represents the state of an operation.
 * This class is used to track the progress of operations like network requests, database operations, etc.
 *
 * @param isInProgress Whether the operation is in progress.
 * @param isSuccessful Whether the operation was successful.
 * @param error The error message if the operation failed.
 */
data class OperationState(
    val isInProgress: Boolean = false,
    val isSuccessful: Boolean = false,
    val error: String? = null
)

/**
 * Extension function to start an operation.
 *
 * @return A new OperationState with isInProgress set to true.
 */
fun OperationState.start(): OperationState {
    return copy(
        isInProgress = true,
        isSuccessful = false,
        error = null
    )
}

/**
 * Extension function to mark an operation as successful.
 *
 * @return A new OperationState with isInProgress set to false and isSuccessful set to true.
 */
fun OperationState.success(): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = true,
        error = null
    )
}

/**
 * Extension function to mark an operation as failed.
 *
 * @param error The error message.
 * @return A new OperationState with isInProgress set to false and the error message set.
 */
fun OperationState.failure(error: String): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = false,
        error = error
    )
}

/**
 * Extension function to reset an operation.
 *
 * @return A new OperationState with all fields reset to their default values.
 */
fun OperationState.reset(): OperationState {
    return OperationState()
}
