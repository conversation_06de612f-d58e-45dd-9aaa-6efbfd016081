package com.tfkcolin.meena.ui.auth

import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.models.AuthResponse
import com.tfkcolin.meena.domain.usecases.auth.IsLoggedInUseCase
import com.tfkcolin.meena.domain.usecases.auth.LoginUseCase
import com.tfkcolin.meena.domain.usecases.auth.RecoverAccountUseCase
import com.tfkcolin.meena.domain.usecases.auth.RegisterUseCase
import com.tfkcolin.meena.domain.usecases.auth.TwoFactorAuthUseCase
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for authentication screens.
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val registerUseCase: RegisterUseCase,
    private val loginUseCase: LoginUseCase,
    private val twoFactorAuthUseCase: TwoFactorAuthUseCase,
    private val recoverAccountUseCase: RecoverAccountUseCase,
    private val isLoggedInUseCase: IsLoggedInUseCase,
    private val tokenManager: TokenManager,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for authentication
    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    // Recovery phrase for registration
    private val _recoveryPhrase = MutableStateFlow<String?>(null)
    val recoveryPhrase: StateFlow<String?> = _recoveryPhrase.asStateFlow()

    init {
        // Check if user is already logged in
        _authState.update { it.copy(isLoggedIn = isLoggedInUseCase()) }

        // Load recovery phrase from TokenManager if available
        tokenManager.getRecoveryPhrase()?.let { phrase ->
            _recoveryPhrase.value = phrase
        }
    }

    /**
     * Register a new user.
     */
    fun register(
        userHandle: String? = null,
        password: String,
        email: String? = null,
        phoneNumber: String? = null,
        recoveryPin: String? = null,
        displayName: String? = null
    ) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                registerOperation = it.registerOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = {
                    registerUseCase(
                        userHandle = userHandle,
                        password = password,
                        email = email,
                        phoneNumber = phoneNumber,
                        recoveryPin = recoveryPin,
                        recoveryPhrase = null, // Server will generate
                        displayName = displayName
                    )
                },
                onSuccess = { response ->
                    _authState.update {
                        it.copy(
                            registerOperation = it.registerOperation.success(),
                            // Don't set isLoggedIn to true yet - we need to show Meena ID and recovery phrase first
                            userId = response.user.id,
                            userHandle = response.user.userHandle,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }

                    // Save recovery phrase if provided
                    response.recoveryPhrase?.let { phrase ->
                        _recoveryPhrase.value = phrase
                        // Also save it in TokenManager for persistence
                        tokenManager.saveRecoveryPhrase(phrase)
                    }
                },
                onError = { error ->
                    println("AuthViewModel: Registration error received - ${error.javaClass.simpleName}: ${error.message}")
                    error.printStackTrace()
                    val errorMessage = getErrorMessage(error, "Registration failed")
                    println("AuthViewModel: Processed error message: $errorMessage")
                    _authState.update {
                        it.copy(
                            registerOperation = it.registerOperation.failure(errorMessage),
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Log in a user.
     */
    fun login(identifier: String, password: String) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                loginOperation = it.loginOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = {
                    loginUseCase(
                        identifier = identifier,
                        password = password
                    )
                },
                onSuccess = { response ->
                    if (response.requires2fa) {
                        _authState.update {
                            it.copy(
                                loginOperation = it.loginOperation.success(),
                                requires2fa = true,
                                userId = response.user.id,
                                userHandle = response.user.userHandle,
                                properties = it.properties.copy(isLoading = false)
                            )
                        }
                    } else {
                        _authState.update {
                            it.copy(
                                loginOperation = it.loginOperation.success(),
                                isLoggedIn = true,
                                userId = response.user.id,
                                userHandle = response.user.userHandle,
                                properties = it.properties.copy(isLoading = false)
                            )
                        }
                    }
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Login failed")
                    _authState.update {
                        it.copy(
                            loginOperation = it.loginOperation.failure(errorMessage),
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Sign in anonymously with just a display name and optional Meena ID.
     * This is the primary authentication method for the app.
     *
     * @param displayName The display name to use.
     * @param userHandle Optional custom Meena ID. If null, a random one will be generated.
     */
    fun anonymousSignIn(displayName: String, userHandle: String? = null) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                registerOperation = it.registerOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            // Generate a random password for anonymous users
            val randomPassword = generateRandomPassword()

            executeUseCase(
                useCase = {
                    registerUseCase(
                        userHandle = userHandle,
                        password = randomPassword,
                        email = null,
                        phoneNumber = null,
                        recoveryPin = null,
                        recoveryPhrase = null, // Server will generate
                        displayName = displayName // Pass the display name
                    )
                },
                onSuccess = { response ->
                    // Log the response to help with debugging
                    println("Registration success: userHandle=${response.user.userHandle}, userId=${response.user.id}")

                    _authState.update {
                        it.copy(
                            registerOperation = it.registerOperation.success(),
                            // Don't set isLoggedIn to true yet - we need to show Meena ID and recovery phrase first
                            userId = response.user.id,
                            userHandle = response.user.userHandle,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }

                    // Save recovery phrase if provided
                    response.recoveryPhrase?.let { phrase ->
                        _recoveryPhrase.value = phrase
                        // Also save it in TokenManager for persistence
                        tokenManager.saveRecoveryPhrase(phrase)
                    }
                },
                onError = { error ->
                    println("AuthViewModel: Anonymous sign-in error received - ${error.javaClass.simpleName}: ${error.message}")
                    error.printStackTrace()
                    val errorMessage = getErrorMessage(error, "Sign-in failed")
                    println("AuthViewModel: Processed error message: $errorMessage")
                    _authState.update {
                        it.copy(
                            registerOperation = it.registerOperation.failure(errorMessage),
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Complete two-factor authentication.
     */
    fun twoFactorAuth(code: String, method: String = "totp") {
        launchWithErrorHandling {
            val userId = _authState.value.userId ?: return@launchWithErrorHandling

            _authState.update { it.copy(
                twoFactorAuthOperation = it.twoFactorAuthOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = {
                    twoFactorAuthUseCase(
                        userId = userId,
                        code = code,
                        method = method
                    )
                },
                onSuccess = { response ->
                    _authState.update {
                        it.copy(
                            twoFactorAuthOperation = it.twoFactorAuthOperation.success(),
                            isLoggedIn = true,
                            requires2fa = false,
                            userId = response.user.id,
                            userHandle = response.user.userHandle,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Two-factor authentication failed")
                    _authState.update {
                        it.copy(
                            twoFactorAuthOperation = it.twoFactorAuthOperation.failure(errorMessage),
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Recover an account.
     *
     * This uses the RecoverAccountUseCase to handle the recovery process.
     */
    fun recoverAccount(
        userHandle: String,
        recoveryPhrase: String,
        recoveryPin: String,
        newPassword: String
    ) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                recoverAccountOperation = it.recoverAccountOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase<AuthResponse>(
                useCase = {
                    recoverAccountUseCase(
                        userHandle = userHandle,
                        recoveryPhrase = recoveryPhrase,
                        recoveryPin = recoveryPin,
                        newPassword = newPassword
                    )
                },
                onSuccess = { response ->
                    _authState.update {
                        it.copy(
                            recoverAccountOperation = it.recoverAccountOperation.success(),
                            isLoggedIn = true,
                            userId = response.user.id,
                            userHandle = response.user.userHandle,
                            properties = it.properties.copy(isLoading = false)
                        )
                    }
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Account recovery failed")
                    _authState.update {
                        it.copy(
                            recoverAccountOperation = it.recoverAccountOperation.failure(errorMessage),
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _authState.update {
            it.copy(
                properties = it.properties.copy(error = null)
            )
        }
    }

    /**
     * Reset operation states.
     */
    fun resetOperationStates() {
        _authState.update {
            it.copy(
                registerOperation = it.registerOperation.reset(),
                loginOperation = it.loginOperation.reset(),
                twoFactorAuthOperation = it.twoFactorAuthOperation.reset(),
                recoverAccountOperation = it.recoverAccountOperation.reset()
            )
        }
    }

    /**
     * Complete the registration process.
     * This should be called after the user has seen their Meena ID and recovery phrase.
     */
    fun completeRegistration() {
        // Mark registration as complete in TokenManager
        tokenManager.setRegistrationComplete(true)

        // Update the auth state to reflect that the user is now logged in
        _authState.update {
            it.copy(isLoggedIn = true)
        }
    }

    /**
     * Get error message from a throwable.
     */
    private fun getErrorMessage(throwable: Throwable, fallback: String? = null): String {
        return errorHandler.getErrorMessage(throwable, fallback)
    }

    /**
     * Generate a random password for anonymous users.
     *
     * @return A random password that meets the requirements.
     */
    private fun generateRandomPassword(): String {
        val charPool = ('a'..'z') + ('A'..'Z') + ('0'..'9') + listOf('!', '@', '#', '$', '%', '&', '*')
        return (1..16)
            .map { charPool.random() }
            .joinToString("")
    }
}

/**
 * State for authentication screens.
 */
data class AuthState(
    val isLoggedIn: Boolean = false,
    val requires2fa: Boolean = false,
    val userId: String? = null,
    val userHandle: String? = null,
    val registerOperation: OperationState = OperationState(),
    val loginOperation: OperationState = OperationState(),
    val twoFactorAuthOperation: OperationState = OperationState(),
    val recoverAccountOperation: OperationState = OperationState(),
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
