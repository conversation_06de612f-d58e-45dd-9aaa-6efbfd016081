package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.meena.ui.components.foundation.UserAvatar

/**
 * A Telegram-style chat list item with swipe actions.
 *
 * @param chatName The name of the chat.
 * @param lastMessage The last message in the chat.
 * @param lastMessageTime The formatted time of the last message.
 * @param avatarUrl The URL of the chat avatar.
 * @param unreadCount The number of unread messages.
 * @param isPinned Whether the chat is pinned.
 * @param isMuted Whether the chat is muted.
 * @param isOnline Whether the chat participant is online.
 * @param isGroup Whether the chat is a group chat.
 * @param isChannel Whether the chat is a channel.
 * @param isVerified Whether the chat is verified.
 * @param hasAttachment Whether the last message has an attachment.
 * @param attachmentType The type of attachment (photo, video, voice, file).
 * @param senderName The name of the sender (for group chats).
 * @param onItemClick The callback for when the item is clicked.
 * @param onLongClick The callback for when the item is long-clicked.
 * @param onPinClick The callback for when the pin action is clicked.
 * @param onMuteClick The callback for when the mute action is clicked.
 * @param onDeleteClick The callback for when the delete action is clicked.
 * @param onArchiveClick The callback for when the archive action is clicked.
 * @param onMarkAsReadClick The callback for when the mark as read action is clicked.
 */
@Composable
fun TelegramChatListItem(
    chatName: String,
    lastMessage: String,
    lastMessageTime: String,
    avatarUrl: String? = null,
    unreadCount: Int = 0,
    isPinned: Boolean = false,
    isMuted: Boolean = false,
    isOnline: Boolean = false,
    isGroup: Boolean = false,
    isChannel: Boolean = false,
    isVerified: Boolean = false,
    hasAttachment: Boolean = false,
    attachmentType: String = "",
    senderName: String? = null,
    onItemClick: () -> Unit = {},
    onLongClick: () -> Unit = {},
    onPinClick: () -> Unit = {},
    onMuteClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {},
    onArchiveClick: () -> Unit = {},
    onMarkAsReadClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = onItemClick,
                onLongClick = onLongClick
            ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Avatar with online indicator
            Box(modifier = Modifier.padding(end = 12.dp)) {
                UserAvatar(
                    imageUrl = avatarUrl,
                    userName = chatName,
                    size = 52.dp,
                    isOnline = isOnline
                )
                
                // Group or channel indicator
                if (isGroup || isChannel) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .offset(x = 4.dp, y = 4.dp)
                            .size(16.dp)
                            .background(MaterialTheme.colorScheme.primary, CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = if (isGroup) Icons.Rounded.Group else Icons.Rounded.Campaign,
                            contentDescription = if (isGroup) "Group chat" else "Channel",
                            tint = Color.White,
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
            }
            
            // Chat info
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 8.dp)
            ) {
                // Chat name row with verification badge if needed
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = chatName,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = if (unreadCount > 0) FontWeight.SemiBold else FontWeight.Normal
                    )
                    
                    if (isVerified) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Rounded.Verified,
                            contentDescription = "Verified",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    if (isPinned) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Rounded.PushPin,
                            contentDescription = "Pinned chat",
                            tint = MaterialTheme.colorScheme.outline,
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
                
                // Last message preview
                val displayMessage = buildString {
                    if (senderName != null && (isGroup || isChannel)) {
                        append(senderName)
                        append(": ")
                    }
                    
                    if (hasAttachment) {
                        when (attachmentType) {
                            "photo" -> append("📷 Photo")
                            "video" -> append("📹 Video")
                            "voice" -> append("🎤 Voice message")
                            "file" -> append("📎 File")
                            else -> append("📎 Attachment")
                        }
                        
                        if (lastMessage.isNotEmpty()) {
                            append(" ")
                            append(lastMessage)
                        }
                    } else {
                        append(lastMessage)
                    }
                }
                
                Text(
                    text = displayMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (unreadCount > 0) 
                        MaterialTheme.colorScheme.onSurface
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = if (unreadCount > 0) FontWeight.Medium else FontWeight.Normal
                )
            }
            
            // Time and status section
            Column(
                horizontalAlignment = Alignment.End
            ) {
                // Time
                Text(
                    text = lastMessageTime,
                    style = MaterialTheme.typography.labelSmall,
                    color = if (unreadCount > 0) 
                        MaterialTheme.colorScheme.primary
                    else 
                        MaterialTheme.colorScheme.outline
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // Unread indicator or muted icon
                when {
                    unreadCount > 0 -> {
                        // Telegram style unread badge
                        Box(
                            modifier = Modifier
                                .background(
                                    if (isMuted) MaterialTheme.colorScheme.surfaceVariant
                                    else MaterialTheme.colorScheme.primary,
                                    CircleShape
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (unreadCount > 99) "99+" else unreadCount.toString(),
                                color = if (isMuted) 
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                else 
                                    MaterialTheme.colorScheme.onPrimary,
                                style = MaterialTheme.typography.labelSmall,
                                fontSize = 12.sp
                            )
                        }
                    }
                    isMuted -> {
                        // Muted icon
                        Icon(
                            imageVector = Icons.Rounded.VolumeOff,
                            contentDescription = "Muted chat",
                            tint = MaterialTheme.colorScheme.outline,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}
