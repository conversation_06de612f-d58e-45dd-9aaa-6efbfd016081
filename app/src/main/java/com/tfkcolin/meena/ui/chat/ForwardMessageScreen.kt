package com.tfkcolin.meena.ui.chat

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.models.ConversationListItem

/**
 * Forward message screen.
 *
 * @param messageId The message ID to forward.
 * @param onNavigateBack Navigate back to the previous screen.
 * @param chatUiState The chat UI state.
 * @param conversationListItems List of conversations to forward to.
 * @param onClearChatError Callback to clear chat-related errors.
 * @param onResetAllOperationStates Callback to reset all operation states.
 * @param onForwardMessage Callback to forward the message.
 */
@Composable
fun ForwardMessageScreen(
    onShowSnackbar: (String) -> Unit,
    messageId: String,
    onNavigateBack: () -> Unit,
    chatUiState: ChatUiState,
    conversationListItems: List<ConversationListItem>,
    onClearChatError: () -> Unit,
    onResetAllOperationStates: () -> Unit,
    onForwardMessage: (String, String, String?) -> Unit
) {
    // State for selected chat and additional content
    var selectedConversation by remember { mutableStateOf<ConversationListItem?>(null) }
    var additionalContent by remember { mutableStateOf("") }

    // Show error message
    LaunchedEffect(chatUiState.error) {
        chatUiState.error?.let {
            onShowSnackbar(it)
            onClearChatError()
        }
    }

    val messageForwardedSuccessfully = stringResource(id = R.string.message_forwarded_successfully)

    // Handle successful forwarding
    LaunchedEffect(chatUiState.messageState.forwardOperation.isSuccessful) {
        if (chatUiState.messageState.forwardOperation.isSuccessful) {
            onShowSnackbar(messageForwardedSuccessfully)
            onResetAllOperationStates()
            onNavigateBack()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Message preview (in a real app, we would show the actual message content)
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(id = R.string.message_preview_headline),
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(id = R.string.original_message_content_placeholder),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Additional content field
            OutlinedTextField(
                value = additionalContent,
                onValueChange = { additionalContent = it },
                label = { Text(stringResource(id = R.string.add_comment_optional_label)) },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 3
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(id = R.string.select_chat_to_forward_to_prompt),
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Chat list
            LazyColumn(
                modifier = Modifier.weight(1f),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(conversationListItems) { conversation ->
                    ForwardChatItem(
                        conversationListItem = conversation,
                        isSelected = conversation == selectedConversation,
                        onClick = { selectedConversation = conversation }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Forward button
            Button(
                onClick = {
                    selectedConversation?.let { conversation ->
                        // Forward the message
                        onForwardMessage(
                            messageId,
                            conversation.id,
                            if (additionalContent.isBlank()) null else additionalContent
                        )
                    }
                },
                enabled = selectedConversation != null && !chatUiState.messageState.forwardOperation.isInProgress,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.Send,
                    contentDescription = null
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(stringResource(id = R.string.forward_button))
            }
        }

        if (chatUiState.messageState.forwardOperation.isInProgress) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

/**
 * Forward chat item component.
 *
 * @param conversationListItem The conversation item to display.
 * @param isSelected Whether the conversation is selected.
 * @param onClick The click handler.
 * @param modifier The modifier for the component.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForwardChatItem(
    conversationListItem: ConversationListItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = androidx.compose.material3.CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = conversationListItem.name,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                )
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = conversationListItem.lastMessage ?: stringResource(id = R.string.no_messages_yet),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}