package com.tfkcolin.meena.ui.components.foundation

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.roundToInt

/**
 * A text overlay that can be dragged around the screen.
 *
 * @param overlay The text overlay data.
 * @param onPositionChange Callback when the position changes.
 * @param onEditRequest Callback when the edit button is clicked.
 * @param onDeleteRequest Callback when the delete button is clicked.
 */
@Composable
fun DraggableTextOverlay(
    overlay: TextOverlay,
    onPositionChange: (Offset) -> Unit,
    onEditRequest: () -> Unit,
    onDeleteRequest: () -> Unit
) {
    val density = LocalDensity.current
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    var parentWidth by remember { mutableStateOf(0) }
    var parentHeight by remember { mutableStateOf(0) }
    var componentWidth by remember { mutableStateOf(0) }
    var componentHeight by remember { mutableStateOf(0) }
    
    // Calculate initial position based on the overlay's position (0-1 range)
    var initialPositionSet by remember { mutableStateOf(false) }
    
    Box(
        modifier = Modifier
            .offset {
                if (!initialPositionSet && parentWidth > 0 && parentHeight > 0) {
                    offsetX = overlay.position.x * parentWidth - (componentWidth / 2)
                    offsetY = overlay.position.y * parentHeight - (componentHeight / 2)
                    initialPositionSet = true
                }
                IntOffset(offsetX.roundToInt(), offsetY.roundToInt())
            }
            .onGloballyPositioned { coordinates ->
                // Get parent size
                coordinates.parentCoordinates?.let { parent ->
                    parentWidth = parent.size.width
                    parentHeight = parent.size.height
                }
                // Get component size
                componentWidth = coordinates.size.width
                componentHeight = coordinates.size.height
            }
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragEnd = {
                        // Calculate the new position in 0-1 range
                        val newX = (offsetX + componentWidth / 2) / parentWidth
                        val newY = (offsetY + componentHeight / 2) / parentHeight
                        onPositionChange(Offset(newX, newY))
                    }
                ) { change, dragAmount ->
                    change.consume()
                    offsetX += dragAmount.x
                    offsetY += dragAmount.y
                    
                    // Keep within bounds
                    offsetX = offsetX.coerceIn(0f, (parentWidth - componentWidth).toFloat())
                    offsetY = offsetY.coerceIn(0f, (parentHeight - componentHeight).toFloat())
                }
            }
    ) {
        Box(
            modifier = Modifier
                .shadow(4.dp)
                .padding(8.dp)
        ) {
            Text(
                text = overlay.text,
                color = Color.White,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(8.dp)
            )
            
            // Edit and delete buttons
            Row(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(4.dp)
            ) {
                IconButton(
                    onClick = onEditRequest,
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(Color.Black.copy(alpha = 0.5f))
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit text",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                IconButton(
                    onClick = onDeleteRequest,
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(Color.Black.copy(alpha = 0.5f))
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Delete text",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * Data class for text overlay.
 */
data class TextOverlay(
    val id: String = "",
    val text: String = "",
    val position: Offset = Offset.Zero
)
