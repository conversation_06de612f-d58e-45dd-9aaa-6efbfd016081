package com.tfkcolin.meena.ui.components.foundation

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest

/**
 * A composable that displays a user avatar with an optional online indicator.
 *
 * @param imageUrl The URL of the user's avatar image.
 * @param userName The user's name (used for the placeholder and content description).
 * @param size The size of the avatar.
 * @param isOnline Whether the user is online.
 * @param onClick The callback for when the avatar is clicked.
 * @param modifier The modifier for the component.
 */
@Composable
fun UserAvatar(
    imageUrl: String?,
    userName: String,
    size: Dp = 40.dp,
    isOnline: Boolean = false,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .clickable(onClick = onClick)
    ) {
        if (imageUrl != null && imageUrl.isNotEmpty()) {
            // Load avatar image
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(imageUrl)
                    .crossfade(true)
                    .build(),
                contentDescription = "$userName's avatar",
                contentScale = ContentScale.Crop,
                modifier = Modifier.size(size)
            )
        } else {
            // Placeholder with initials
            val initials = userName
                .split(" ")
                .take(2)
                .joinToString("") { it.take(1).uppercase() }
            
            Box(
                modifier = Modifier
                    .size(size)
                    .background(MaterialTheme.colorScheme.primaryContainer, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                if (initials.isNotEmpty()) {
                    Text(
                        text = initials,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        fontSize = (size.value * 0.4).sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "User",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.size(size * 0.6f)
                    )
                }
            }
        }
        
        // Online indicator
        if (isOnline) {
            Box(
                modifier = Modifier
                    .size(size * 0.3f)
                    .offset(x = size * 0.05f, y = size * 0.05f)
                    .align(Alignment.BottomEnd)
                    .background(Color.Green, CircleShape)
                    .border(1.dp, MaterialTheme.colorScheme.surface, CircleShape)
            )
        }
    }
}
