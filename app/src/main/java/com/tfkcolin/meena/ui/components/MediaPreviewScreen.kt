package com.tfkcolin.meena.ui.components

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaType
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import kotlinx.coroutines.flow.Flow
import androidx.core.net.toUri

/**
 * Screen for previewing media attachments in full-screen mode.
 *
 * @param attachments The list of media attachments to preview.
 * @param initialAttachmentIndex The index of the initial attachment to display.
 * @param onNavigateBack Navigate back to the previous screen.
 * @param mediaAttachmentHelper Helper for handling media attachments.
 * @param onDownloadAttachment Callback for downloading an attachment.
 * @param downloadProgress Flow of download progress for the current attachment.
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
@Composable
fun MediaPreviewScreen(
    attachments: List<MediaAttachment>,
    initialAttachmentIndex: Int = 0,
    onNavigateBack: () -> Unit,
    mediaAttachmentHelper: MediaAttachmentHelper,
    onDownloadAttachment: (MediaAttachment) -> Unit,
    downloadProgress: Flow<DownloadProgress?>? = null
) {
    val context = LocalContext.current
    val pagerState = rememberPagerState(initialPage = initialAttachmentIndex) { attachments.size }
    val currentAttachment = attachments.getOrNull(pagerState.currentPage)

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val attachment = attachments[page]
            var scale by remember { mutableFloatStateOf(1f) }
            var offsetX by remember { mutableFloatStateOf(0f) }
            var offsetY by remember { mutableFloatStateOf(0f) }

            // Reset zoom and pan when page changes
            LaunchedEffect(page) {
                scale = 1f
                offsetX = 0f
                offsetY = 0f
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer(
                        scaleX = scale,
                        scaleY = scale,
                        translationX = offsetX,
                        translationY = offsetY
                    )
                    .pointerInput(Unit) {
                        detectTransformGestures { _, pan, zoom, _ ->
                            scale = (scale * zoom).coerceIn(1f, 3f)

                            // Only allow panning when zoomed in
                            if (scale > 1f) {
                                val maxX = (scale - 1) * size.width / 2
                                val maxY = (scale - 1) * size.height / 2

                                offsetX = (offsetX + pan.x).coerceIn(-maxX, maxX)
                                offsetY = (offsetY + pan.y).coerceIn(-maxY, maxY)
                            } else {
                                offsetX = 0f
                                offsetY = 0f
                            }
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                when (attachment.type) {
                    MediaType.IMAGE.name.lowercase() -> {
                        EnhancedMediaAttachmentView(
                            attachment = attachment,
                            onClick = { /* Do nothing on click in preview mode */ },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    MediaType.VIDEO.name.lowercase() -> {
                        VideoPlayer(
                            attachment = attachment,
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    MediaType.AUDIO.name.lowercase() -> {
                        AudioPlayer(
                            attachment = attachment,
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    MediaType.DOCUMENT.name.lowercase() -> {
                        DocumentViewer(
                            attachment = attachment,
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    else -> {
                        // Fallback for unknown types
                        EnhancedMediaAttachmentView(
                            attachment = attachment,
                            onClick = { /* Do nothing on click in preview mode */ },
                            mediaAttachmentHelper = mediaAttachmentHelper,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }
        }

        // Download progress indicator
        if (currentAttachment != null) {
            val progress by downloadProgress?.collectAsState(initial = null) ?: remember { mutableStateOf<DownloadProgress?>(null) }

            when (progress) {
                is DownloadProgress.Downloading -> {
                    val downloadingProgress = (progress as DownloadProgress.Downloading).progress
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            progress = { downloadingProgress / 100f },
                            modifier = Modifier.size(48.dp),
                            color = MaterialTheme.colorScheme.primary,
                            strokeWidth = 5.dp,
                            trackColor = MaterialTheme.colorScheme.primaryContainer,
                        )
                    }
                }
                is DownloadProgress.Preparing -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp)
                        )
                    }
                }
                else -> {
                    // No progress indicator needed
                }
            }
        }

        // Page indicator for multiple attachments
        if (attachments.size > 1) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .align(Alignment.BottomCenter),
                horizontalArrangement = Arrangement.Center
            ) {
                repeat(attachments.size) { index ->
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .padding(horizontal = 2.dp)
                            .background(
                                color = if (pagerState.currentPage == index) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                                },
                                shape = MaterialTheme.shapes.small
                            )
                    )
                }
            }
        }
    }
}
