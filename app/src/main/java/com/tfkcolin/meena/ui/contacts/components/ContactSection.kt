package com.tfkcolin.meena.ui.contacts.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.Contact

/**
 * Contact section component for displaying a horizontal list of contacts.
 *
 * @param title The section title.
 * @param contacts The list of contacts to display.
 * @param onContactClick The click handler for a contact.
 * @param emptyText The text to display when the list is empty.
 * @param modifier The modifier for the component.
 */
@Composable
fun ContactSection(
    title: String,
    contacts: List<Contact>,
    onContactClick: (Contact) -> Unit,
    emptyText: String = "No contacts",
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            modifier = Modifier.padding(horizontal = 16.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (contacts.isEmpty()) {
            Text(
                text = emptyText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        } else {
            LazyRow(
                contentPadding = PaddingValues(horizontal = 16.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                items(contacts) { contact ->
                    ContactHorizontalItem(
                        contact = contact,
                        onClick = { onContactClick(contact) },
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }
            }
        }
    }
}
