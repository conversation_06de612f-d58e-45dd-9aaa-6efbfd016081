package com.tfkcolin.meena.ui.auth

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.FabPosition
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.theme.MeenaTheme
import androidx.compose.runtime.* // Import for LaunchedEffect, remember, Animatable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.res.stringResource
import com.tfkcolin.meena.R
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Two-factor authentication screen.
 *
 * @param onNavigateBack Navigate back to the previous screen.
 * @param isLoading Indicates if an authentication operation is in progress.
 * @param errorMessage An error message to display, if any.
 * @param code The current value of the verification code input.
 * @param onCodeChange Callback for when the verification code changes.
 * @param onVerifyCode Callback to initiate the verification process.
 * @param onClearError Callback to clear the current error message.
 */
@Composable
fun TwoFactorAuthScreen(
    onNavigateBack: () -> Unit,
    isLoading: Boolean,
    code: String,
    onCodeChange: (String) -> Unit,
    onVerifyCode: (String) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    val animatedAlpha = remember { Animatable(0.1f) }

    LaunchedEffect(Unit) {
        animatedAlpha.animateTo(
            targetValue = 0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 3000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Gradient with animated overlay
        Canvas(modifier = Modifier.matchParentSize()) {
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFF673AB7), // Deeper purple-blue
                    Color(0xFF512DA8) // Darker purple-blue
                )
            )
            drawRect(gradient)

            // Add a subtle animated overlay effect
            clipPath(Path().apply {
                // This creates a subtle curve from bottom left to bottom right, darkening the bottom
                moveTo(0f, size.height * 0.8f)
                cubicTo(
                    size.width * 0.2f, size.height * 0.9f,
                    size.width * 0.8f, size.height * 0.9f,
                    size.width, size.height * 0.8f
                )
                lineTo(size.width, size.height)
                lineTo(0f, size.height)
                close()
            }) {
                drawRect(Color.Black.copy(alpha = animatedAlpha.value)) // Subtle dark overlay with animation
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp, vertical = 32.dp), // Adjusted vertical padding
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.two_factor_auth_title),
                style = MaterialTheme.typography.headlineMedium.copy( // Consistent headline style
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary // Text color for contrast on dark background
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.two_factor_auth_description),
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f) // Softer text color
                ),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            MeenaTextField(
                value = code,
                onValueChange = {
                    // Only allow digits and limit to 6 characters
                    if (it.length <= 6 && it.all { char -> char.isDigit() }) {
                        onCodeChange(it)
                    }
                },
                label = stringResource(R.string.verification_code_label),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                        if (code.length == 6) {
                            onVerifyCode(code)
                        }
                    }
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(24.dp))

            MeenaPrimaryButton(
                text = stringResource(R.string.verify_button),
                onClick = { onVerifyCode(code) },
                enabled = !isLoading && code.length == 6,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun TwoFactorAuthScreenPreview() {
    MeenaTheme {
        TwoFactorAuthScreen(
            onNavigateBack = {},
            isLoading = false,
            code = "",
            onCodeChange = {},
            onVerifyCode = {},
        )
    }
}