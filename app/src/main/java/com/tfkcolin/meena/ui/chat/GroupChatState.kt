package com.tfkcolin.meena.ui.chat

import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties

/**
 * UI state for group chat operations.
 * Extends BaseUiState to include common loading and error states.
 */
data class GroupChatState(
    // Base state properties (composition)
    val properties: UiStateProperties = UiStateProperties(),

    // Operation states
    val createGroupOperation: OperationState = OperationState(),
    val updateGroupOperation: OperationState = OperationState(),
    val addParticipantsOperation: OperationState = OperationState(),
    val removeParticipantsOperation: OperationState = OperationState(),
    val updateAdminsOperation: OperationState = OperationState(),
    val leaveGroupOperation: OperationState = OperationState(),

    // Data
    val newGroupChatId: String? = null
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error
}

/**
 * Extension function to check if any operation is in progress.
 */
fun GroupChatState.isAnyOperationInProgress(): Boolean {
    return createGroupOperation.isInProgress ||
            updateGroupOperation.isInProgress ||
            addParticipantsOperation.isInProgress ||
            removeParticipantsOperation.isInProgress ||
            updateAdminsOperation.isInProgress ||
            leaveGroupOperation.isInProgress
}

/**
 * Extension function to reset all operation states.
 */
fun GroupChatState.resetAllOperations(): GroupChatState {
    return copy(
        properties = properties.copy(error = null),
        createGroupOperation = OperationState(),
        updateGroupOperation = OperationState(),
        addParticipantsOperation = OperationState(),
        removeParticipantsOperation = OperationState(),
        updateAdminsOperation = OperationState(),
        leaveGroupOperation = OperationState(),
        newGroupChatId = null
    )
}
