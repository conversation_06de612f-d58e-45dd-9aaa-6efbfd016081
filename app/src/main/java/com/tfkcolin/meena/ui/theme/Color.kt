package com.tfkcolin.meena.ui.theme

import androidx.compose.ui.graphics.Color

// Existing colors (can be removed if not used elsewhere)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// New Meena Design System Colors (placeholders - adjust based on actual design images)
val MeenaPrimary = Color(0xFF6200EE) // Example primary color
val MeenaPrimaryVariant = Color(0xFF3700B3) // Example primary variant
val MeenaSecondary = Color(0xFF03DAC5) // Example secondary color
val MeenaSecondaryVariant = Color(0xFF018786) // Example secondary variant
val MeenaBackground = Color(0xFFFFFFFF) // Example light background
val MeenaSurface = Color(0xFFFFFFFF) // Example light surface
val MeenaError = Color(0xFFB00020) // Example error color
val MeenaOnPrimary = Color(0xFFFFFFFF) // Text/icons on primary
val MeenaOnSecondary = Color(0xFF000000) // Text/icons on secondary
val MeenaOnBackground = Color(0xFF000000) // Text/icons on background
val MeenaOnSurface = Color(0xFF000000) // Text/icons on surface
val MeenaOnError = Color(0xFFFFFFFF) // Text/icons on error

// Dark theme counterparts (placeholders - adjust based on actual design images)
val MeenaDarkPrimary = Color(0xFFBB86FC)
val MeenaDarkPrimaryVariant = Color(0xFF3700B3)
val MeenaDarkSecondary = Color(0xFF03DAC5)
val MeenaDarkSecondaryVariant = Color(0xFF03DAC5)
val MeenaDarkBackground = Color(0xFF121212)
val MeenaDarkSurface = Color(0xFF121212)
val MeenaDarkError = Color(0xFFCF6679)
val MeenaDarkOnPrimary = Color(0xFF000000)
val MeenaDarkOnSecondary = Color(0xFF000000)
val MeenaDarkOnBackground = Color(0xFFFFFFFF)
val MeenaDarkOnSurface = Color(0xFFFFFFFF)
val MeenaDarkOnError = Color(0xFF000000)
