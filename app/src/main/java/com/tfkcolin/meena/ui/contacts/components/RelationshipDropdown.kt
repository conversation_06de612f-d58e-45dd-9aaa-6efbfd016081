package com.tfkcolin.meena.ui.contacts.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * Relationship dropdown component.
 *
 * @param selectedRelationship The currently selected relationship.
 * @param onRelationshipSelected The callback for when a relationship is selected.
 * @param relationships The list of relationships to display.
 * @param isExpanded Whether the dropdown is expanded.
 * @param onExpandedChange The callback for when the expanded state changes.
 * @param modifier The modifier for the component.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RelationshipDropdown(
    selectedRelationship: String,
    onRelationshipSelected: (String) -> Unit,
    relationships: List<Pair<String, String>>,
    isExpanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    // Find the label for the selected relationship
    val selectedRelationshipLabel = relationships.find { it.first == selectedRelationship }?.second ?: "Friend"

    ExposedDropdownMenuBox(
        expanded = isExpanded,
        onExpandedChange = onExpandedChange,
        modifier = modifier
    ) {
        OutlinedTextField(
            value = selectedRelationshipLabel,
            onValueChange = {},
            readOnly = true,
            label = { Text("Relationship") },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isExpanded) },
            colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors(),
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )

        ExposedDropdownMenu(
            expanded = isExpanded,
            onDismissRequest = { onExpandedChange(false) }
        ) {
            relationships.forEach { (value, label) ->
                DropdownMenuItem(
                    text = { Text(label) },
                    onClick = {
                        onRelationshipSelected(value)
                        onExpandedChange(false)
                    }
                )
            }
        }
    }
}
