package com.tfkcolin.meena.ui.profile

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.ui.components.LoadingIndicator
import com.tfkcolin.meena.ui.components.QRCodeDisplay
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    onShowSnackbar: (String) -> Unit,
    onLogout: () -> Unit,
    onNavigateToSecuritySettings: () -> Unit = {},
    onNavigateToEditProfile: () -> Unit = {},
    onNavigateToNotificationsSettings: () -> Unit = {},
    onNavigateToAppearanceSettings: () -> Unit = {},
    onNavigateToLanguageSettings: () -> Unit = {},
    profileState: ProfileState,
    onClearProfileError: () -> Unit,
    onPerformLogout: () -> Unit,
    onGetMeenaId: () -> String
) {
    val scope = rememberCoroutineScope()
    var showLogoutDialog by remember { mutableStateOf(false) }

    LaunchedEffect(profileState.isLoggedOut) {
        if (profileState.isLoggedOut) onLogout()
    }

    LaunchedEffect(profileState.error) {
        profileState.error?.let {
            onShowSnackbar(it)
            onClearProfileError()
        }
    }

    Box(modifier = Modifier
        .fillMaxSize()
    ) {
        when {
            profileState.isLoading -> LoadingIndicator()
            profileState.user != null -> {
                ProfileContent(
                    user = profileState.user,
                    onNavigateToEditProfile = onNavigateToEditProfile,
                    onNavigateToSecuritySettings = onNavigateToSecuritySettings,
                    onNavigateToNotificationsSettings = onNavigateToNotificationsSettings,
                    onNavigateToAppearanceSettings = onNavigateToAppearanceSettings,
                    onNavigateToLanguageSettings = onNavigateToLanguageSettings,
                    onLogoutClick = { showLogoutDialog = true },
                    onShowSnackbar = { message ->
                        scope.launch { onShowSnackbar(message) }
                    }
                )
            }
            else -> {
                Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(
                        stringResource(R.string.user_data_not_available),
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }

    if (showLogoutDialog) {
        LogoutConfirmationDialog(
            onConfirm = { showLogoutDialog = false; onPerformLogout() },
            onDismiss = { showLogoutDialog = false }
        )
    }
}

@Composable
private fun ProfileContent(
    user: User,
    onNavigateToEditProfile: () -> Unit,
    onNavigateToSecuritySettings: () -> Unit,
    onNavigateToNotificationsSettings: () -> Unit,
    onNavigateToAppearanceSettings: () -> Unit,
    onNavigateToLanguageSettings: () -> Unit,
    onLogoutClick: () -> Unit,
    onShowSnackbar: (String) -> Unit
) {
    var showQrDialog by remember { mutableStateOf(false) }
    var visible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) { visible = true }

    if (showQrDialog) {
        QRCodeDialog(
            user = user,
            onDismiss = { showQrDialog = false },
            onShowSnackbar = onShowSnackbar
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        AnimatedVisibility(
            visible = visible,
            enter = slideInVertically { -it } + fadeIn(tween(600)),
        ) {
            ProfileHeaderCard(user = user, onClick = { showQrDialog = true })
        }
        AnimatedVisibility(
            visible = visible,
            enter = slideInVertically(tween(600, 150)) { -it } + fadeIn(tween(600, 150)),
        ) {
            SettingsGroup(
                onNavigateToEditProfile,
                onNavigateToSecuritySettings,
                onNavigateToNotificationsSettings,
                onNavigateToAppearanceSettings,
                onNavigateToLanguageSettings
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        AnimatedVisibility(
            visible = visible,
            enter = slideInVertically(tween(600, 300)) { it } + fadeIn(tween(600, 300)),
        ) {
            LogoutButton(onClick = onLogoutClick)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileHeaderCard(user: User, onClick: () -> Unit) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(64.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primaryContainer),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = user.displayName?.firstOrNull()?.toString() ?: "U",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    fontWeight = FontWeight.Bold
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = user.displayName ?: stringResource(R.string.anonymous_user),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "@${user.userHandle}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = stringResource(R.string.show_qr_code),
                modifier = Modifier.size(28.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun SettingsItem(
    icon: ImageVector,
    @StringRes textId: Int,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.primaryContainer),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                modifier = Modifier.size(22.dp)
            )
        }

        Spacer(modifier = Modifier.width(16.dp))
        Text(
            text = stringResource(textId),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.weight(1f))

        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = stringResource(R.string.navigate),
            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun QRCodeDialog(
    user: User,
    onDismiss: () -> Unit,
    onShowSnackbar: (String) -> Unit
) {
    val clipboardManager = LocalClipboardManager.current
    Dialog(onDismissRequest = onDismiss) {
        var visible by remember { mutableStateOf(false) }

        LaunchedEffect(Unit) { visible = true }

        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(tween(300)) + scaleIn(
                spring(Spring.DampingRatioMediumBouncy, Spring.StiffnessLow),
                initialScale = 0.8f
            ),
            exit = fadeOut(tween(200)) + scaleOut(targetScale = 0.8f)
        ) {
            Card(
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    QRCodeDisplay(
                        meenaId = user.userHandle,
                        displayName = user.displayName
                    )

                    val meenaIdCopied = stringResource(R.string.meena_id_copied)
                    TextButton(
                        onClick = {
                            clipboardManager.setText(AnnotatedString(user.userHandle))
                            onShowSnackbar(meenaIdCopied)
                        },
                        modifier = Modifier.padding(bottom = 8.dp)
                    ) {
                        Icon(
                            Icons.Default.ContentCopy,
                            contentDescription = null,
                            Modifier.size(ButtonDefaults.IconSize)
                        )
                        Spacer(Modifier.size(ButtonDefaults.IconSpacing))
                        Text(stringResource(R.string.copy_id))
                    }
                }
            }
        }
    }
}

@Composable
fun SettingsGroup(
    onNavigateToEditProfile: () -> Unit,
    onNavigateToSecuritySettings: () -> Unit,
    onNavigateToNotificationsSettings: () -> Unit,
    onNavigateToAppearanceSettings: () -> Unit,
    onNavigateToLanguageSettings: () -> Unit
) {
    Column(modifier = Modifier.clip(RoundedCornerShape(20.dp))) {
        SettingsItem(
            icon = Icons.Default.Edit,
            textId = R.string.edit_profile,
            onClick = onNavigateToEditProfile
        )
        HorizontalDivider(
            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.2f),
            modifier = Modifier.padding(start = 72.dp)
        )
        SettingsItem(
            icon = Icons.Default.Security,
            textId = R.string.security,
            onClick = onNavigateToSecuritySettings
        )
        HorizontalDivider(
            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.2f),
            modifier = Modifier.padding(start = 72.dp)
        )
        SettingsItem(
            icon = Icons.Default.Notifications,
            textId = R.string.notifications,
            onClick = onNavigateToNotificationsSettings
        )
        HorizontalDivider(
            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.2f),
            modifier = Modifier.padding(start = 72.dp)
        )
        SettingsItem(
            icon = Icons.Default.DarkMode,
            textId = R.string.appearance,
            onClick = onNavigateToAppearanceSettings
        )
        HorizontalDivider(
            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.2f),
            modifier = Modifier.padding(start = 72.dp)
        )
        SettingsItem(
            icon = Icons.Default.Language,
            textId = R.string.language,
            onClick = onNavigateToLanguageSettings
        )
    }
}

@Composable
fun LogoutButton(onClick: () -> Unit) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(52.dp),
        shape = RoundedCornerShape(16.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.errorContainer,
            contentColor = MaterialTheme.colorScheme.onErrorContainer
        )
    ) {
        Icon(Icons.AutoMirrored.Filled.ExitToApp, stringResource(R.string.logout))
        Spacer(Modifier.size(ButtonDefaults.IconSpacing))
        Text(
            stringResource(R.string.logout),
            fontWeight = FontWeight.Bold,
            fontSize = 16.sp
        )
    }
}

@Composable
fun LogoutConfirmationDialog(onConfirm: () -> Unit, onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(stringResource(R.string.confirm_logout)) },
        text = { Text(stringResource(R.string.logout_confirmation_message)) },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) { Text(stringResource(R.string.logout)) }
        },
        dismissButton = { TextButton(onClick = onDismiss) { Text(stringResource(R.string.cancel)) } }
    )
}

@Preview(showBackground = true, widthDp = 360, heightDp = 800)
@Composable
fun ProfileScreenPreview() {
    val previewUser = User(
        userId = "12345",
        userHandle = "meena_user_123",
        displayName = "Meena Colin",
        profilePictureUrl = null,
        bio = "Lead Developer at Meena App. Passionate about clean code and beautiful UIs.",
        phoneNumber = null, email = null, createdAt = null, lastSeenAt = null
    )
    val previewState = ProfileState(user = previewUser)
    MaterialTheme {
        ProfileScreen(
            onLogout = {},
            profileState = previewState,
            onClearProfileError = {},
            onPerformLogout = {},
            onGetMeenaId = { "content_for_qr_code_12345" },
            onNavigateToSecuritySettings = {},
            onNavigateToEditProfile = {},
            onNavigateToNotificationsSettings = {},
            onNavigateToAppearanceSettings = {},
            onNavigateToLanguageSettings = {},
            onShowSnackbar = {  },
        )
    }
}