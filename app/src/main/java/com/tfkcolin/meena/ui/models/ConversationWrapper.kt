package com.tfkcolin.meena.ui.models

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.data.models.PrivacyType
import com.tfkcolin.meena.data.models.getOtherParticipantId
import com.tfkcolin.meena.data.models.getParticipantCount

/**
 * A wrapper class for Chat that provides type-specific functionality
 * while maintaining compatibility with the API contract.
 */
sealed class ConversationWrapper(val chat: Chat) {
    // Common properties from the underlying Chat
    val id: String get() = chat.id
    val lastMessage: String? get() = chat.lastMessage
    val lastMessageTimestamp: Long? get() = chat.lastMessageTimestamp
    val unreadCount: Int get() = chat.unreadCount
    val isArchived: Boolean get() = chat.isArchived
    val isMuted: Boolean get() = chat.isMuted
    val isPinned: Boolean get() = chat.isPinned
    val conversationType: ConversationType get() = chat.getConversationType()
    val privacyType: PrivacyType? get() = chat.getPrivacyType()
    val participantCount: Int get() = chat.getParticipantCount()
    val isEncrypted: Boolean get() = chat.isEncrypted
    val lastMessageSenderId: String? get() = chat.lastMessageSenderId
    val lastMessageType: String? get() = chat.lastMessageType

    /**
     * Represents a one-to-one conversation.
     */
    class OneToOneConversation(
        chat: Chat,
        val otherParticipantId: String,
        val otherParticipantName: String,
        val otherParticipantAvatarUrl: String?
    ) : ConversationWrapper(chat) {
        val displayName: String get() = otherParticipantName
        val avatarUrl: String? get() = otherParticipantAvatarUrl
    }

    /**
     * Represents a group conversation.
     */
    class GroupConversation(
        chat: Chat,
        val adminIds: List<String>
    ) : ConversationWrapper(chat) {
        val displayName: String get() = chat.name ?: "Unnamed Group"
        val avatarUrl: String? get() = chat.avatarUrl
        val description: String? get() = chat.description

        /**
         * Check if a user is an admin of this group.
         *
         * @param userId The user ID to check.
         * @return True if the user is an admin.
         */
        fun isUserAdmin(userId: String): Boolean {
            return adminIds.contains(userId)
        }
    }

    /**
     * Represents a channel conversation.
     */
    class ChannelConversation(
        chat: Chat,
        val subscriberCount: Int
    ) : ConversationWrapper(chat) {
        val displayName: String get() = chat.name ?: "Unnamed Channel"
        val avatarUrl: String? get() = chat.avatarUrl
        val description: String? get() = chat.description
    }

    companion object {
        /**
         * Create a ConversationWrapper from a Chat.
         *
         * @param chat The Chat to wrap.
         * @param currentUserId The current user's ID.
         * @param contactNameMap A map of user IDs to display names.
         * @param contactAvatarMap A map of user IDs to avatar URLs.
         * @return The appropriate ConversationWrapper subclass.
         */
        fun fromChat(
            chat: Chat,
            currentUserId: String,
            contactNameMap: Map<String, String>,
            contactAvatarMap: Map<String, String?> = emptyMap()
        ): ConversationWrapper {
            return when (chat.getConversationType()) {
                ConversationType.ONE_TO_ONE -> {
                    val otherParticipantId = chat.getOtherParticipantId(currentUserId) ?: ""
                    OneToOneConversation(
                        chat = chat,
                        otherParticipantId = otherParticipantId,
                        otherParticipantName = contactNameMap[otherParticipantId] ?: "Unknown",
                        otherParticipantAvatarUrl = contactAvatarMap[otherParticipantId]
                    )
                }
                ConversationType.GROUP -> {
                    GroupConversation(
                        chat = chat,
                        adminIds = chat.getAdminIdsList()
                    )
                }
                ConversationType.CHANNEL -> {
                    ChannelConversation(
                        chat = chat,
                        subscriberCount = chat.getParticipantIdsList().size
                    )
                }
            }
        }
    }
}
