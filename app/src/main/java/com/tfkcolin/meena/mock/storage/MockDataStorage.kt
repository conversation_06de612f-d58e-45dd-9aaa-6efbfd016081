package com.tfkcolin.meena.mock.storage

import android.content.Context
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.local.ChatDao
import com.tfkcolin.meena.data.local.ContactDao
import com.tfkcolin.meena.data.local.MessageDao
import com.tfkcolin.meena.data.local.UserDao
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.utils.TokenManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Persistent storage for mock data that survives app restarts.
 * Now uses Room Database for persistence.
 */
@Singleton
class MockDataStorage @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userDao: UserDao,
    private val contactDao: ContactDao,
    private val chatDao: ChatDao,
    private val messageDao: MessageDao,
    internal val tokenManager: TokenManager // Inject TokenManager to get current user ID
) {

    // In-memory caches for performance (optional, can be removed if direct DAO access is preferred)
    // For now, keeping them for compatibility with existing MockInitializer structure
    private var _currentUser: User? = null

    /**
     * Initialize mock data if needed.
     * This will ensure a current user exists for mock operations.
     */
    suspend fun initializeIfNeeded() = withContext(Dispatchers.IO) {
        if (AppConfig.DevConfig.RESET_MOCK_DATA_ON_START) {
            clearAllData() // Clear existing data if reset is enabled
        }

        // Ensure a current user exists for mock operations
        val currentUserId = tokenManager.getUserId()
        if (currentUserId != null) {
            _currentUser = userDao.getUserById(currentUserId)
        }

        if (_currentUser == null) {
            // If no current user, generate a mock one and save it
            val newUser = MockDataGenerator.generateMockUser()
            userDao.insertUser(newUser)
            _currentUser = newUser
            tokenManager.saveUserId(newUser.userId)
            tokenManager.saveUserHandle(newUser.userHandle)
            tokenManager.markRegistrationComplete() // Mark registration complete for mock user
        }
    }

    // Users
    suspend fun getUsers(): List<User> = userDao.getAllUsers()
    suspend fun getUser(id: String): User? = userDao.getUserById(id)
    fun getCurrentUser(): User? = _currentUser // This will be updated by initializeIfNeeded

    suspend fun addUser(user: User) {
        userDao.insertUser(user)
    }

    suspend fun updateUser(user: User) {
        userDao.insertUser(user) // REPLACE strategy handles update
        if (_currentUser?.userId == user.userId) {
            _currentUser = user
        }
    }

    suspend fun deleteUser(userId: String) {
        userDao.deleteUser(userId)
    }

    // Contacts
    suspend fun getContacts(userId: String): List<Contact> = contactDao.getContacts(userId)
    suspend fun getContact(userId: String, contactId: String): Contact? = contactDao.getContact(userId, contactId)

    suspend fun addContact(userId: String, contact: Contact) {
        contactDao.insertContact(contact)
    }

    suspend fun removeContact(userId: String, contactId: String) {
        val contactToRemove = contactDao.getContact(userId, contactId)
        if (contactToRemove != null) {
            contactDao.deleteContact(contactToRemove.id)
        }
    }

    // Chats
    suspend fun getChats(userId: String): List<Chat> = chatDao.getChatsForUser(userId)
    suspend fun getChat(id: String): Chat? = chatDao.getChatById(id)

    suspend fun addChat(chat: Chat) {
        chatDao.insertChat(chat)
    }

    suspend fun updateChat(chat: Chat) {
        chatDao.insertChat(chat) // REPLACE strategy handles update
    }

    suspend fun deleteChat(chatId: String) {
        chatDao.deleteChat(chatId)
        messageDao.deleteMessagesForChat(chatId) // Also remove all messages for this chat
    }

    // Messages
    suspend fun getMessages(chatId: String): List<Message> = messageDao.getMessagesForChat(chatId)

    suspend fun addMessage(message: Message) {
        messageDao.insertMessage(message)
    }

    suspend fun updateMessage(message: Message) {
        messageDao.insertMessage(message) // REPLACE strategy handles update
    }

    suspend fun deleteMessage(chatId: String, messageId: String) {
        messageDao.deleteMessage(messageId)
    }

    /**
     * Clear all mock data from the database.
     */
    suspend fun clearAllData() = withContext(Dispatchers.IO) {
        messageDao.deleteAllMessages()
        chatDao.deleteAllChats()
        contactDao.deleteAllContacts(tokenManager.getUserId() ?: "") // Clear contacts for current user
        userDao.deleteAllUsers() // Clear all users, including the current one
        _currentUser = null // Clear in-memory cache
        tokenManager.clearTokens() // Clear authentication tokens
    }
}
