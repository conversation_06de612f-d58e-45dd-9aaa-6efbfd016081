package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaUploadResponse
import com.tfkcolin.meena.data.models.UploadRequest
import com.tfkcolin.meena.data.models.UploadUrlResponse
import com.tfkcolin.meena.data.models.UploadCompleteResponse
import com.tfkcolin.meena.data.models.EncryptedMediaUploadRequest
import com.tfkcolin.meena.data.models.EncryptedMediaUploadResponse
import com.tfkcolin.meena.data.models.ChunkedUploadInitRequest
import com.tfkcolin.meena.data.models.ChunkedUploadInitResponse
import com.tfkcolin.meena.data.models.ChunkedUploadStatusResponse
import com.tfkcolin.meena.data.models.ChunkUploadResponse
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of MediaApi for testing and development.
 */
@Singleton
class MockMediaApi @Inject constructor(
    private val mockAuthApi: MockAuthApi
) : MediaApi {

    override suspend fun requestUploadUrl(request: UploadRequest): UploadUrlResponse {
        simulateNetworkDelay()

        val mediaId = UUID.randomUUID().toString()
        return UploadUrlResponse(
            mediaId = mediaId,
            uploadUrl = "https://mock-upload-url.com/upload/$mediaId",
            requiredFormFields = mapOf(
                "key" to mediaId,
                "Content-Type" to request.contentType
            )
        )
    }

    override suspend fun completeUpload(mediaId: String): UploadCompleteResponse {
        simulateNetworkDelay()

        return UploadCompleteResponse(
            mediaId = mediaId,
            fileUrl = "https://mock-storage.com/files/$mediaId",
            thumbnailUrl = "https://mock-storage.com/thumbnails/$mediaId",
            fileName = "mock_file_${Random.nextInt(1000, 9999)}.jpg",
            fileSize = Random.nextLong(100000, 10000000),
            mimeType = "image/jpeg",
            width = 800,
            height = 600,
            duration = null
        )
    }

    override suspend fun uploadMedia(
        authToken: String,
        file: MultipartBody.Part
    ): Response<MediaUploadResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Simulate file upload processing
            simulateUploadDelay()
            
            // Generate mock media attachment
            val mediaId = UUID.randomUUID().toString()
            val fileName = "mock_file_${Random.nextInt(1000, 9999)}.jpg"
            val fileSize = Random.nextLong(100000, 10000000) // 100KB to 10MB
            
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "",
                type = "image",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                name = fileName,
                size = fileSize,
                duration = null, // For images
                width = 800,
                height = 600
            )
            
            val response = MediaUploadResponse(
                mediaId = mediaId,
                fileName = fileName,
                fileSize = fileSize,
                mimeType = "image/jpeg",
                url = mediaAttachment.url,
                thumbnailUrl = mediaAttachment.thumbnailUrl,
                uploadedAt = System.currentTimeMillis()
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Upload failed: ${e.message}")
        }
    }

    override suspend fun getMedia(
        authToken: String,
        mediaId: String
    ): Response<MediaAttachment> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Generate mock media attachment
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "",
                type = "image",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                name = "mock_file_${Random.nextInt(1000, 9999)}.jpg",
                size = Random.nextLong(100000, 10000000),
                duration = null,
                width = 800,
                height = 600
            )

            return Response.success(mediaAttachment)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun deleteMedia(
        authToken: String,
        mediaId: String
    ): Response<Void> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // In a real implementation, you'd delete the media file
            // For mock, we just simulate success
            
            return Response.success(null)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Delete failed: ${e.message}")
        }
    }

    override suspend fun getEncryptedUploadUrl(
        authToken: String,
        request: EncryptedMediaUploadRequest
    ): Response<EncryptedMediaUploadResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val mediaId = UUID.randomUUID().toString()
            val response = EncryptedMediaUploadResponse(
                mediaId = mediaId,
                uploadUrl = "https://mock-encrypted-upload.com/upload/$mediaId",
                expiresAt = "2024-12-31T23:59:59Z"
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun initiateChunkedUpload(
        authToken: String,
        request: ChunkedUploadInitRequest
    ): Response<ChunkedUploadInitResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val uploadId = UUID.randomUUID().toString()
            val response = ChunkedUploadInitResponse(
                uploadId = uploadId,
                expiresAt = "2024-12-31T23:59:59Z"
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun uploadChunk(
        authToken: String,
        uploadId: String,
        chunkIndex: Int,
        chunk: RequestBody
    ): Response<ChunkUploadResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chunkSize = Random.nextLong(1000, 10000)
            val response = ChunkUploadResponse(
                chunkIndex = chunkIndex,
                receivedSize = chunkSize,
                totalReceived = chunkSize * (chunkIndex + 1)
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun getChunkedUploadStatus(
        authToken: String,
        uploadId: String
    ): Response<ChunkedUploadStatusResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val response = ChunkedUploadStatusResponse(
                uploadId = uploadId,
                fileName = "mock_file.jpg",
                contentType = "image/jpeg",
                totalSize = 1000000,
                uploadedChunks = listOf(0, 1, 2),
                totalReceived = 750000,
                expiresAt = "2024-12-31T23:59:59Z"
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun completeChunkedUpload(
        authToken: String,
        uploadId: String
    ): Response<MediaAttachment> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val mediaId = UUID.randomUUID().toString()
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "",
                type = "image",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                name = "mock_chunked_file.jpg",
                size = Random.nextLong(100000, 10000000),
                duration = null,
                width = 800,
                height = 600
            )

            return Response.success(mediaAttachment)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private suspend fun simulateUploadDelay() {
        // Simulate file upload time (1-3 seconds)
        delay(Random.nextLong(1000, 3000))
    }
    
    private suspend fun simulateVideoUploadDelay() {
        // Simulate video upload time (3-8 seconds)
        delay(Random.nextLong(3000, 8000))
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    override suspend fun uploadMedia(
        authToken: String,
        requestBody: RequestBody
    ): Response<MediaAttachment> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            simulateUploadDelay()

            val mediaId = UUID.randomUUID().toString()
            val mediaAttachment = MediaAttachment(
                id = mediaId,
                messageId = "",
                type = "image",
                url = "https://picsum.photos/800/600?random=${Random.nextInt()}",
                thumbnailUrl = "https://picsum.photos/200/150?random=${Random.nextInt()}",
                name = "mock_file_${Random.nextInt(1000, 9999)}.jpg",
                size = Random.nextLong(100000, 10000000),
                duration = null,
                width = 800,
                height = 600
            )

            return Response.success(mediaAttachment)

        } catch (e: Exception) {
            return createErrorResponse(500, "Upload failed: ${e.message}")
        }
    }

    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
