package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.api.ChatListResponse
import com.tfkcolin.meena.data.api.ChatResponse
import com.tfkcolin.meena.data.api.MessageListResponse
import com.tfkcolin.meena.data.api.MessageResponse
import com.tfkcolin.meena.data.api.SendMessageRequest
import com.tfkcolin.meena.data.api.EditMessageRequest
import com.tfkcolin.meena.data.api.CreateChatRequest
import com.tfkcolin.meena.data.api.ChatSettingsRequest
import com.tfkcolin.meena.data.api.UpdateBatchMessageStatusRequest
import com.tfkcolin.meena.data.api.ForwardMessageRequest
import com.tfkcolin.meena.data.api.MessageReactionRequest
import com.tfkcolin.meena.data.api.CreateGroupRequest
import com.tfkcolin.meena.data.api.GroupResponse
import com.tfkcolin.meena.data.api.GroupMembersResponse
import com.tfkcolin.meena.data.api.GroupMembersRequest
import com.tfkcolin.meena.data.api.GroupMember
import com.tfkcolin.meena.data.api.GroupMemberResponse
import com.tfkcolin.meena.data.api.UpdateGroupRequest
import com.tfkcolin.meena.data.api.UpdateRoleRequest
import com.tfkcolin.meena.data.api.UserInfo
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of ChatApi with AI-powered responses.
 */
@Singleton
class MockChatApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi,
    private val aiResponseGenerator: AIResponseGenerator = AIResponseGenerator()
) : ChatApi {
    
    override suspend fun getChats(
        authToken: String,
        limit: Int,
        offset: Int
    ): Response<ChatListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Get all chats where user is a participant
            val allChats = mockDataStorage.getChats(userId)
            val userChats = allChats.filter { chat ->
                chat.getParticipantIdsList().contains(userId)
            }.sortedByDescending { it.lastMessageTimestamp ?: 0 }
            
            // Apply pagination
            val paginatedChats = userChats.drop(offset).take(limit)
            
            // Convert to response format - ChatApi expects List<Chat>, not List<ChatResponse>
            val response = ChatListResponse(
                chats = paginatedChats,
                total_count = userChats.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun getMessages(
        authToken: String,
        chatId: String,
        limit: Int,
        offset: Int
    ): Response<com.tfkcolin.meena.data.api.MessageListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Verify user has access to this chat
            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")
            
            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Get messages for this chat
            val allMessages = mockDataStorage.getMessages(chatId)
                .sortedByDescending { it.timestamp }
            
            // Apply pagination
            val paginatedMessages = allMessages.drop(offset).take(limit)
            
            // Convert to response format - ChatApi expects List<Message>, not List<MessageResponse>
            val response = com.tfkcolin.meena.data.api.MessageListResponse(
                messages = paginatedMessages,
                total_count = allMessages.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun sendMessage(
        authToken: String,
        chatId: String,
        request: com.tfkcolin.meena.data.api.SendMessageRequest
    ): Response<com.tfkcolin.meena.data.api.MessageResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Verify user has access to this chat
            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")
            
            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Get recipient ID for one-to-one chats
            val recipientId = if (chat.conversationType == "one_to_one") {
                chat.getParticipantIdsList().find { it != userId } ?: ""
            } else {
                "" // Group chats don't have specific recipients
            }

            // Create new message
            val message = Message(
                id = UUID.randomUUID().toString(),
                chatId = chatId,
                senderId = userId,
                recipientId = recipientId,
                content = request.content,
                contentType = request.content_type,
                mediaUrl = request.media_url,
                hasAttachments = request.media_url != null,
                timestamp = System.currentTimeMillis(),
                status = "sent",
                isEdited = false,
                deletedFor = null,
                replyToMessageId = request.reply_to_message_id,
                forwardFromMessageId = null,
                forwardFromChatId = null,
                forwardFromUserId = null,
                reactions = null
            )
            
            // Save message
            mockDataStorage.addMessage(message)
            
            // Update chat's last message
            val updatedChat = chat.copy(
                lastMessage = message.content,
                lastMessageTimestamp = message.timestamp,
                lastMessageSenderId = userId,
                lastMessageType = message.contentType
            )
            mockDataStorage.updateChat(updatedChat)

            // Generate AI response if this is a one-to-one chat and the message is from current user
            if (chat.conversationType == "one_to_one" && AppConfig.FeatureFlags.ENABLE_AI_CHAT_RESPONSES) {
                generateAIResponse(chat, message, userId)
            }

            // ChatApi expects MessageResponse with Message inside
            val messageResponse = com.tfkcolin.meena.data.api.MessageResponse(
                message = message
            )

            return Response.success(messageResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun editMessage(
        authToken: String,
        messageId: String,
        request: com.tfkcolin.meena.data.api.EditMessageRequest
    ): Response<com.tfkcolin.meena.data.api.MessageResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the message
            val allChats = mockDataStorage.getChats(userId)
            var targetMessage: Message? = null
            var targetChatId: String? = null
            
            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    targetMessage = message
                    targetChatId = chatId
                    break
                }
            }
            
            if (targetMessage == null || targetChatId == null) {
                return createErrorResponse(404, "Message not found")
            }
            
            // Verify user owns the message
            if (targetMessage.senderId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Update message
            val updatedMessage = targetMessage.copy(
                content = request.content,
                isEdited = true
            )

            mockDataStorage.updateMessage(updatedMessage)

            // ChatApi expects MessageResponse with Message inside
            val messageResponse = com.tfkcolin.meena.data.api.MessageResponse(
                message = updatedMessage
            )

            return Response.success(messageResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    


    // Additional required methods from ChatApi interface

    override suspend fun getChatById(
        authToken: String,
        chatId: String
    ): Response<ChatResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")

            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }

            return Response.success(ChatResponse(chat = chat))

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun createChat(
        authToken: String,
        request: CreateChatRequest
    ): Response<ChatResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Create new chat
            // Ensure unique participant IDs for one-to-one chat
            val participants = (listOf(userId) + request.participant_ids).distinct()

            // For one-to-one chats, there should only be two participants
            if (participants.size != 2) {
                return createErrorResponse(400, "One-to-one chat must have exactly two unique participants.")
            }

            val chat = Chat(
                id = UUID.randomUUID().toString(),
                conversationType = "one_to_one",
                participantIds = participants.joinToString(","),
                createdBy = userId,
                createdAt = System.currentTimeMillis()
            )

            mockDataStorage.addChat(chat)

            // Just get the other participant ID, no contact creation
            val otherParticipantId = participants.first { it != userId }

            return Response.success(ChatResponse(chat = chat))

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun updateChatSettings(
        authToken: String,
        chatId: String,
        request: ChatSettingsRequest
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")

            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }

            val updatedChat = chat.copy(
                isMuted = request.is_muted ?: chat.isMuted,
                isArchived = request.is_archived ?: chat.isArchived,
                isPinned = request.is_pinned ?: chat.isPinned
            )

            mockDataStorage.updateChat(updatedChat)

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun deleteChat(
        authToken: String,
        chatId: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(chatId)
                ?: return createErrorResponse(404, "Chat not found")

            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }

            mockDataStorage.deleteChat(chatId)

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun updateMessageStatus(
        authToken: String,
        messageId: String,
        status: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find and update the message status
            val allChats = mockDataStorage.getChats(userId)
            var found = false

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    val updatedMessage = message.copy(status = status)
                    mockDataStorage.updateMessage(updatedMessage)
                    found = true
                    break
                }
            }

            if (!found) {
                return createErrorResponse(404, "Message not found")
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun updateBatchMessageStatus(
        authToken: String,
        request: UpdateBatchMessageStatusRequest
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Update multiple message statuses
            val allChats = mockDataStorage.getChats(userId)

            for (messageId in request.message_ids) {
                for ((chatId, _) in allChats) {
                    val messages = mockDataStorage.getMessages(chatId)
                    val message = messages.find { it.id == messageId }
                    if (message != null) {
                        val updatedMessage = message.copy(status = request.status)
                        mockDataStorage.updateMessage(updatedMessage)
                        break
                    }
                }
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun deleteMessageForSelf(
        authToken: String,
        messageId: String,
        scope: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find and update the message to mark as deleted for self
            val allChats = mockDataStorage.getChats(userId)
            var found = false

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    val updatedMessage = message.copy(deletedFor = "self")
                    mockDataStorage.updateMessage(updatedMessage)
                    found = true
                    break
                }
            }

            if (!found) {
                return createErrorResponse(404, "Message not found")
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun deleteMessageForEveryone(
        authToken: String,
        messageId: String,
        scope: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find and update the message to mark as deleted for everyone
            val allChats = mockDataStorage.getChats(userId)
            var found = false

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId && it.senderId == userId }
                if (message != null) {
                    val updatedMessage = message.copy(deletedFor = "everyone")
                    mockDataStorage.updateMessage(updatedMessage)
                    found = true
                    break
                }
            }

            if (!found) {
                return createErrorResponse(404, "Message not found or access denied")
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun forwardMessage(
        authToken: String,
        messageId: String,
        request: ForwardMessageRequest
    ): Response<MessageResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find the original message
            val allChats = mockDataStorage.getChats(userId)
            var originalMessage: Message? = null

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    originalMessage = message
                    break
                }
            }

            if (originalMessage == null) {
                return createErrorResponse(404, "Message not found")
            }

            // Create forwarded message
            val forwardedMessage = Message(
                id = UUID.randomUUID().toString(),
                chatId = request.chat_id,
                senderId = userId,
                recipientId = "", // Will be determined by chat type
                content = request.additional_content ?: originalMessage.content,
                contentType = originalMessage.contentType,
                mediaUrl = originalMessage.mediaUrl,
                hasAttachments = originalMessage.hasAttachments,
                timestamp = System.currentTimeMillis(),
                status = "sent",
                isEdited = false,
                deletedFor = null,
                replyToMessageId = null,
                forwardFromMessageId = originalMessage.id,
                forwardFromChatId = originalMessage.chatId,
                forwardFromUserId = originalMessage.senderId,
                reactions = null
            )

            mockDataStorage.addMessage(forwardedMessage)

            return Response.success(MessageResponse(message = forwardedMessage))

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun addReaction(
        authToken: String,
        messageId: String,
        request: MessageReactionRequest
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find and update the message with reaction
            val allChats = mockDataStorage.getChats(userId)
            var found = false

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    val currentReactions = message.reactions?.toMutableMap() ?: mutableMapOf()
                    val emojiReactions = currentReactions[request.emoji]?.toMutableList() ?: mutableListOf()

                    if (!emojiReactions.contains(userId)) {
                        emojiReactions.add(userId)
                        currentReactions[request.emoji] = emojiReactions

                        val updatedMessage = message.copy(reactions = currentReactions)
                        mockDataStorage.updateMessage(updatedMessage)
                    }

                    found = true
                    break
                }
            }

            if (!found) {
                return createErrorResponse(404, "Message not found")
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun removeReaction(
        authToken: String,
        messageId: String,
        request: MessageReactionRequest
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find and update the message to remove reaction
            val allChats = mockDataStorage.getChats(userId)
            var found = false

            for ((chatId, _) in allChats) {
                val messages = mockDataStorage.getMessages(chatId)
                val message = messages.find { it.id == messageId }
                if (message != null) {
                    val currentReactions = message.reactions?.toMutableMap() ?: mutableMapOf()
                    val emojiReactions = currentReactions[request.emoji]?.toMutableList()

                    if (emojiReactions != null && emojiReactions.contains(userId)) {
                        emojiReactions.remove(userId)
                        if (emojiReactions.isEmpty()) {
                            currentReactions.remove(request.emoji)
                        } else {
                            currentReactions[request.emoji] = emojiReactions
                        }

                        val updatedMessage = message.copy(reactions = if (currentReactions.isEmpty()) null else currentReactions)
                        mockDataStorage.updateMessage(updatedMessage)
                    }

                    found = true
                    break
                }
            }

            if (!found) {
                return createErrorResponse(404, "Message not found")
            }

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun replyToMessage(
        authToken: String,
        chatId: String,
        request: SendMessageRequest
    ): Response<MessageResponse> {
        // This is the same as sendMessage but with reply_to_message_id set
        return sendMessage(authToken, chatId, request)
    }

    // Group-related methods (simplified implementations)

    override suspend fun createGroup(
        authToken: String,
        request: CreateGroupRequest
    ): Response<GroupResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Create new group chat
            val groupId = UUID.randomUUID().toString()
            val participants = (listOf(userId) + (request.initial_members ?: emptyList())).distinct()

            val chat = Chat(
                id = groupId,
                conversationType = "group",
                privacyType = request.privacy_type,
                participantIds = participants.joinToString(","),
                name = request.name,
                description = request.description,
                createdBy = userId,
                adminIds = userId, // Creator is admin
                createdAt = System.currentTimeMillis()
            )

            mockDataStorage.addChat(chat)

            // Create a simple GroupResponse with all required parameters
            val groupResponse = GroupResponse(
                group_id = groupId,
                name = request.name,
                description = request.description,
                picture_url = null,
                privacy_type = request.privacy_type,
                creator = UserInfo(
                    user_id = userId,
                    user_handle = "user_$userId",
                    display_name = "User $userId",
                    avatar_url = null
                ),
                member_count = participants.size,
                settings = emptyMap()
            )

            return Response.success(groupResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun getGroup(
        authToken: String,
        groupId: String
    ): Response<GroupResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }

            val privacyType = chat.privacyType ?: "private"
            val groupResponse = GroupResponse(
                group_id = groupId,
                name = chat.name ?: "Group",
                description = chat.description,
                picture_url = null,
                privacy_type = privacyType,
                creator = UserInfo(
                    user_id = chat.createdBy ?: "unknown",
                    user_handle = "user_${chat.createdBy ?: "unknown"}",
                    display_name = "User ${chat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = chat.getParticipantIdsList().size,
                settings = emptyMap()
            )

            return Response.success(groupResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun updateGroup(
        authToken: String,
        groupId: String,
        request: UpdateGroupRequest
    ): Response<GroupResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getAdminIdsList().contains(userId)) {
                return createErrorResponse(403, "Admin access required")
            }

            // Update group details
            val updatedChat = chat.copy(
                name = request.name ?: chat.name,
                description = request.description ?: chat.description
            )

            mockDataStorage.updateChat(updatedChat)

            val privacyType = chat.privacyType ?: "private"
            val groupResponse = GroupResponse(
                group_id = groupId,
                name = updatedChat.name ?: "Group",
                description = updatedChat.description,
                picture_url = request.picture_url,
                privacy_type = privacyType,
                creator = UserInfo(
                    user_id = chat.createdBy ?: "unknown",
                    user_handle = "user_${chat.createdBy ?: "unknown"}",
                    display_name = "User ${chat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = chat.getParticipantIdsList().size,
                settings = emptyMap()
            )

            return Response.success(groupResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun getGroupMembers(
        authToken: String,
        groupId: String,
        limit: Int,
        offset: Int
    ): Response<GroupMembersResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getParticipantIdsList().contains(userId)) {
                return createErrorResponse(403, "Access denied")
            }

            // Get group members (simplified)
            val memberIds = chat.getParticipantIdsList()
            val paginatedMemberIds = memberIds.drop(offset).take(limit)

            val members = paginatedMemberIds.mapNotNull { memberId ->
                mockDataStorage.getUser(memberId)?.let { user ->
                    GroupMember(
                        user = UserInfo(
                            user_id = user.userId,
                            user_handle = user.userHandle,
                            display_name = user.displayName,
                            avatar_url = user.profilePictureUrl
                        ),
                        role = if (chat.getAdminIdsList().contains(memberId)) "admin" else "member",
                        joined_at = chat.createdAt
                    )
                }
            }

            val response = GroupMembersResponse(
                members = members,
                total_count = memberIds.size
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun addGroupMembers(
        authToken: String,
        groupId: String,
        request: GroupMembersRequest
    ): Response<GroupResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getAdminIdsList().contains(userId)) {
                return createErrorResponse(403, "Admin access required")
            }

            // Add new members
            val currentParticipants = chat.getParticipantIdsList().toMutableList()
            val newMemberIds = request.user_handles // Assuming these are user IDs for simplicity

            newMemberIds.forEach { memberId ->
                if (!currentParticipants.contains(memberId)) {
                    currentParticipants.add(memberId)
                }
            }

            val updatedChat = chat.copy(
                participantIds = currentParticipants.joinToString(",")
            )

            mockDataStorage.updateChat(updatedChat)

            val privacyType = chat.privacyType ?: "private"
            val groupResponse = GroupResponse(
                group_id = groupId,
                name = chat.name ?: "Group",
                description = chat.description,
                picture_url = null,
                privacy_type = privacyType,
                creator = UserInfo(
                    user_id = chat.createdBy ?: "unknown",
                    user_handle = "user_${chat.createdBy ?: "unknown"}",
                    display_name = "User ${chat.createdBy ?: "unknown"}",
                    avatar_url = null
                ),
                member_count = currentParticipants.size,
                settings = emptyMap()
            )

            return Response.success(groupResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun removeGroupMember(
        authToken: String,
        groupId: String,
        userHandle: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getAdminIdsList().contains(userId)) {
                return createErrorResponse(403, "Admin access required")
            }

            // Remove member
            val currentParticipants = chat.getParticipantIdsList().toMutableList()
            val memberToRemove = userHandle // Assuming userHandle is actually userId for simplicity

            if (!currentParticipants.contains(memberToRemove)) {
                return createErrorResponse(404, "Member not found in group")
            }

            currentParticipants.remove(memberToRemove)

            val updatedChat = chat.copy(
                participantIds = currentParticipants.joinToString(",")
            )

            mockDataStorage.updateChat(updatedChat)

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun updateGroupMemberRole(
        authToken: String,
        groupId: String,
        userHandle: String,
        request: UpdateRoleRequest
    ): Response<GroupMemberResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            val chat = mockDataStorage.getChat(groupId)
                ?: return createErrorResponse(404, "Group not found")

            if (!chat.getAdminIdsList().contains(userId)) {
                return createErrorResponse(403, "Admin access required")
            }

            val memberToUpdate = userHandle // Assuming userHandle is actually userId for simplicity

            if (!chat.getParticipantIdsList().contains(memberToUpdate)) {
                return createErrorResponse(404, "Member not found in group")
            }

            // Update admin list based on role
            val currentAdmins = chat.getAdminIdsList().toMutableList()
            if (request.role == "admin" && !currentAdmins.contains(memberToUpdate)) {
                currentAdmins.add(memberToUpdate)
            } else if (request.role == "member" && currentAdmins.contains(memberToUpdate)) {
                currentAdmins.remove(memberToUpdate)
            }

            val updatedChat = chat.copy(
                adminIds = currentAdmins.joinToString(",")
            )

            mockDataStorage.updateChat(updatedChat)

            // Return the updated member info
            val user = mockDataStorage.getUser(memberToUpdate)
            val memberResponse = GroupMemberResponse(
                user = UserInfo(
                    user_id = user?.userId ?: memberToUpdate,
                    user_handle = user?.userHandle ?: "user_$memberToUpdate",
                    display_name = user?.displayName ?: "User $memberToUpdate",
                    avatar_url = user?.profilePictureUrl
                ),
                role = request.role,
                joined_at = chat.createdAt
            )

            return Response.success(memberResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
    
    /**
     * Generate AI response for one-to-one chats.
     */
    private suspend fun generateAIResponse(chat: Chat, userMessage: Message, currentUserId: String) {
        try {
            // Get the other participant (not the current user)
            val otherParticipantId = chat.getParticipantIdsList().find { it != currentUserId }
                ?: return

            // Check if the other participant is an AI persona
            val isAIPersona = MockDataGenerator.isMockAIContact(otherParticipantId)

            // Determine if AI should respond based on persona type and random chance
            val shouldRespond = if (isAIPersona) {
                Random.nextFloat() <= 0.95f // 95% chance to respond for AI personas
            } else {
                Random.nextFloat() <= 0.2f // 20% chance to respond for regular users
            }

            if (!shouldRespond) return

            // Get the specific mock AI contact ID. This should be non-null if isAIPersona is true.
            val mockAIContactId = MockDataGenerator.getMockAIContactIdFromUserId(otherParticipantId)
                ?: return // If null, it's not a valid mock AI contact, so return.

            // Generate AI response
            val aiResponse = aiResponseGenerator.generateResponse(
                userMessage = userMessage.content,
                chatId = chat.id,
                mockAIContactId = mockAIContactId // Corrected parameter name
            )

            // Create AI message
            val aiMessage = Message(
                id = UUID.randomUUID().toString(),
                chatId = chat.id,
                senderId = otherParticipantId,
                recipientId = currentUserId,
                content = aiResponse.text,
                contentType = "text",
                mediaUrl = null,
                hasAttachments = false,
                timestamp = System.currentTimeMillis() + Random.nextLong(1000, 5000), // Slight delay
                status = "sent",
                isEdited = false,
                deletedFor = null,
                replyToMessageId = null,
                forwardFromMessageId = null,
                forwardFromChatId = null,
                forwardFromUserId = null,
                reactions = null
            )
            
            // Add delay before sending AI response
            delay(aiResponse.typingDuration)
            
            // Save AI message
            mockDataStorage.addMessage(aiMessage)
            
            // Update chat's last message
            val updatedChat = chat.copy(
                lastMessage = aiMessage.content,
                lastMessageTimestamp = aiMessage.timestamp,
                lastMessageSenderId = otherParticipantId,
                lastMessageType = aiMessage.contentType,
                unreadCount = chat.unreadCount + 1 // Increment unread count
            )
            mockDataStorage.updateChat(updatedChat)
            
        } catch (e: Exception) {
            // Log error but don't fail the original message send
            if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                println("Error generating AI response: ${e.message}")
            }
        }
    }
}
