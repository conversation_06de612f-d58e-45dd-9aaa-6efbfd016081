package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of AuthApi for testing and development.
 */
@Singleton
class MockAuthApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val tokenManager: TokenManager
) : AuthApi {
    
    override suspend fun register(request: RegisterRequest): Response<AuthResponse> {
        simulateNetworkDelay()

        try {
            println("MockAuthApi: Registration request received - userHandle: ${request.userHandle}, password: ${request.password.isNotBlank()}")

            // Validate request - for anonymous sign-in, only password is required
            if (request.password.isBlank()) {
                val errorMsg = "Password is required"
                println("MockAuthApi: Registration validation failed - $errorMsg")
                return createErrorResponse(400, errorMsg)
            }
            
            // Check if user handle already exists (only if provided)
            val existingUsers = mockDataStorage.getUsers()
            if (request.userHandle != null && existingUsers.any { it.userHandle == request.userHandle }) {
                val errorMsg = "User handle '${request.userHandle}' already exists"
                println("MockAuthApi: Registration failed - $errorMsg")
                return createErrorResponse(409, errorMsg)
            }
            
            // Generate new user
            val userHandle = request.userHandle ?: generateMeenaId()
            val newUser = User(
                userId = UUID.randomUUID().toString(),
                userHandle = userHandle,
                displayName = request.displayName ?: userHandle,
                profilePictureUrl = "https://i.pravatar.cc/150?u=${UUID.randomUUID()}",
                bio = "",
                email = request.email,
                phoneNumber = request.phoneNumber,
                subscriptionTier = "free",
                verificationStatus = "none",
                isActive = true,
                createdAt = System.currentTimeMillis().toString(),
                lastSeenAt = System.currentTimeMillis().toString()
            )

            println("MockAuthApi: Creating new user - userId: ${newUser.userId}, userHandle: ${newUser.userHandle}")
            
            // Save user
            mockDataStorage.addUser(newUser)
            println("MockAuthApi: User saved successfully")
            
            // Generate tokens
            val accessToken = generateAccessToken(newUser.userId)
            val refreshToken = generateRefreshToken(newUser.userId)

            // Store tokens using TokenManager
            tokenManager.saveTokens(accessToken, refreshToken)
            tokenManager.saveUserId(newUser.userId)
            tokenManager.saveUserHandle(newUser.userHandle)
            
            // Generate recovery phrase
            val recoveryPhrase = MockDataGenerator.generateRecoveryPhrase().joinToString(" ")
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = newUser.userId,
                    userHandle = newUser.userHandle,
                    displayName = newUser.displayName ?: newUser.userHandle,
                    bio = newUser.bio ?: "",
                    avatarUrl = newUser.profilePictureUrl ?: "",
                    verificationStatus = newUser.verificationStatus,
                    isVerified = newUser.verificationStatus == "verified",
                    isGoldMember = newUser.subscriptionTier == "gold",
                    lastActive = newUser.lastSeenAt ?: "",
                    createdAt = newUser.createdAt ?: "",
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400, // 24 hours
                recoveryPhrase = recoveryPhrase
            )
            
            println("MockAuthApi: Registration successful - userHandle: ${authResponse.user.userHandle}")
            return Response.success(authResponse)

        } catch (e: Exception) {
            val errorMsg = "Internal server error: ${e.message}"
            println("MockAuthApi: Registration failed with exception - $errorMsg")
            e.printStackTrace()
            return createErrorResponse(500, errorMsg)
        }
    }
    
    override suspend fun login(request: LoginRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            // Find user by identifier (user handle, email, or phone)
            val users = mockDataStorage.getUsers()
            val user = users.find {
                it.userHandle == request.identifier || 
                it.email == request.identifier || 
                it.phoneNumber == request.identifier 
            }
            
            if (user == null) {
                return createErrorResponse(401, "Invalid credentials")
            }
            
            // For mock purposes, accept any password for existing users
            // In real implementation, you'd verify the password hash
            
            // Generate tokens
            val accessToken = generateAccessToken(user.userId)
            val refreshToken = generateRefreshToken(user.userId)

            // Store tokens using TokenManager
            tokenManager.saveTokens(accessToken, refreshToken)
            tokenManager.saveUserId(user.userId)
            tokenManager.saveUserHandle(user.userHandle)

            // Update last active
            val updatedUser = user.copy(lastSeenAt = System.currentTimeMillis().toString())
            mockDataStorage.updateUser(updatedUser)
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.userId,
                    userHandle = user.userHandle,
                    displayName = user.displayName ?: user.userHandle,
                    bio = user.bio ?: "",
                    avatarUrl = user.profilePictureUrl ?: "",
                    verificationStatus = user.verificationStatus,
                    isVerified = user.verificationStatus == "verified",
                    isGoldMember = user.subscriptionTier == "gold",
                    lastActive = user.lastSeenAt ?: "",
                    createdAt = user.createdAt ?: "",
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400, // 24 hours
                requires2fa = Random.nextFloat() < 0.1f // 10% chance of requiring 2FA
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun refreshToken(request: TokenRefreshRequest): Response<AuthResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = tokenManager.getUserId()
            if (userId == null) {
                return createErrorResponse(401, "No user ID found in TokenManager")
            }
            
            val user = mockDataStorage.getUser(userId)
            if (user == null) {
                return createErrorResponse(404, "User not found")
            }
            
            // Generate new tokens
            val newAccessToken = generateAccessToken(userId)
            val newRefreshToken = generateRefreshToken(userId)
            
            // Store new tokens using TokenManager
            tokenManager.saveTokens(newAccessToken, newRefreshToken)
            
            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.userId,
                    userHandle = user.userHandle,
                    displayName = user.displayName ?: user.userHandle,
                    bio = user.bio ?: "",
                    avatarUrl = user.profilePictureUrl ?: "",
                    verificationStatus = user.verificationStatus,
                    isVerified = user.verificationStatus == "verified",
                    isGoldMember = user.subscriptionTier == "gold",
                    lastActive = user.lastSeenAt ?: "",
                    createdAt = user.createdAt ?: "",
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = newAccessToken,
                refreshToken = newRefreshToken,
                expiresIn = 86400
            )
            
            return Response.success(authResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun twoFactorAuth(request: TwoFactorAuthRequest): Response<AuthResponse> {
        simulateNetworkDelay()

        try {
            // For mock purposes, always succeed with 2FA
            val users = mockDataStorage.getUsers()
            val user = users.find { it.userId == request.userId }
                ?: return createErrorResponse(404, "User not found")

            // Generate tokens
            val accessToken = generateAccessToken(user.userId)
            val refreshToken = generateRefreshToken(user.userId)

            // Store tokens using TokenManager
            tokenManager.saveTokens(accessToken, refreshToken)
            tokenManager.saveUserId(user.userId)
            tokenManager.saveUserHandle(user.userHandle)

            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.userId,
                    userHandle = user.userHandle,
                    displayName = user.displayName ?: user.userHandle,
                    bio = user.bio ?: "",
                    avatarUrl = user.profilePictureUrl ?: "",
                    verificationStatus = user.verificationStatus,
                    isVerified = user.verificationStatus == "verified",
                    isGoldMember = user.subscriptionTier == "gold",
                    lastActive = user.lastSeenAt ?: "",
                    createdAt = user.createdAt ?: "",
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400
            )

            return Response.success(authResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun requestRecovery(request: AccountRecoveryRequest): Response<RecoveryResponse> {
        simulateNetworkDelay()

        try {
            // Find user by handle
            val users = mockDataStorage.getUsers()
            val user = users.find { it.userHandle == request.userHandle }

            if (user == null) {
                return createErrorResponse(404, "User not found")
            }

            // For mock purposes, accept any recovery phrase and PIN
            // Generate recovery token
            val recoveryToken = "recovery_${UUID.randomUUID()}"

            val response = RecoveryResponse(recoveryToken = recoveryToken)
            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun confirmRecovery(request: RecoveryConfirmRequest): Response<AuthResponse> {
        simulateNetworkDelay()

        try {
            // For mock purposes, accept any recovery token that starts with "recovery_"
            if (!request.recoveryToken.startsWith("recovery_")) {
                return createErrorResponse(400, "Invalid recovery token")
            }

            // For demo, use the first user as the recovered user
            val users = mockDataStorage.getUsers()
            val user = users.firstOrNull()
                ?: return createErrorResponse(404, "User not found")

            // Generate new tokens
            val accessToken = generateAccessToken(user.userId)
            val refreshToken = generateRefreshToken(user.userId)

            // Store tokens using TokenManager
            tokenManager.saveTokens(accessToken, refreshToken)
            tokenManager.saveUserId(user.userId)
            tokenManager.saveUserHandle(user.userHandle)

            val authResponse = AuthResponse(
                user = AuthUserProfile(
                    id = user.userId,
                    userHandle = user.userHandle,
                    displayName = user.displayName ?: user.userHandle,
                    bio = user.bio ?: "",
                    avatarUrl = user.profilePictureUrl ?: "",
                    verificationStatus = user.verificationStatus,
                    isVerified = user.verificationStatus == "verified",
                    isGoldMember = user.subscriptionTier == "gold",
                    lastActive = user.lastSeenAt ?: "",
                    createdAt = user.createdAt ?: "",
                    followerCount = 0,
                    followingCount = 0
                ),
                accessToken = accessToken,
                refreshToken = refreshToken,
                expiresIn = 86400
            )

            return Response.success(authResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun changePassword(request: PasswordChangeRequest): Response<Unit> {
        simulateNetworkDelay()

        // For mock purposes, always succeed if user is authenticated
        return Response.success(Unit)
    }

    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun generateAccessToken(userId: String): String {
        return "mock_access_token_${userId}_${System.currentTimeMillis()}"
    }
    
    private fun generateRefreshToken(userId: String): String {
        return "mock_refresh_token_${userId}_${System.currentTimeMillis()}"
    }

    private fun generateMeenaId(): String {
        // Generate a 9-character Meena ID (alphanumeric)
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..9)
            .map { chars.random() }
            .joinToString("")
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
    
    /**
     * Validate if a token is valid and return the associated user ID.
     */
    fun validateToken(token: String): String? {
        return tokenManager.getAccessToken()?.let {
            if (it == token) tokenManager.getUserId() else null
        }
    }
    
    /**
     * Get current authenticated user from token.
     */
    suspend fun getCurrentUserFromToken(token: String): User? {
        val userId = validateToken(token) ?: return null
        return mockDataStorage.getUser(userId)
    }
}
