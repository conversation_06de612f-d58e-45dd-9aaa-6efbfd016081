package com.tfkcolin.meena.mock.ai

import kotlin.random.Random

/**
 * AI persona for generating realistic chat responses.
 */
data class AIPersona(
    val id: String,
    val name: String,
    val description: String,
    val responsePatterns: List<ResponsePattern>,
    val personality: PersonalityTraits,
    val responseDelay: LongRange = 1000L..3000L
)

/**
 * Response pattern for different message types and contexts.
 */
data class ResponsePattern(
    val trigger: MessageTrigger,
    val responses: List<String>,
    val probability: Float = 1.0f
)

/**
 * Message trigger conditions.
 */
sealed class MessageTrigger {
    object Greeting : MessageTrigger()
    object Question : MessageTrigger()
    object Compliment : MessageTrigger()
    object Complaint : MessageTrigger()
    object Goodbye : MessageTrigger()
    object Random : MessageTrigger()
    data class Keyword(val keywords: List<String>) : MessageTrigger()
    data class Emotion(val emotion: String) : MessageTrigger()
    object Media : MessageTrigger()
    object FirstMessage : MessageTrigger()
}

/**
 * Personality traits that influence response generation.
 */
data class PersonalityTraits(
    val friendliness: Float = 0.7f, // 0.0 to 1.0
    val formality: Float = 0.3f,    // 0.0 to 1.0
    val humor: Float = 0.5f,        // 0.0 to 1.0
    val enthusiasm: Float = 0.6f,   // 0.0 to 1.0
    val helpfulness: Float = 0.8f,  // 0.0 to 1.0
    val chattiness: Float = 0.5f    // 0.0 to 1.0
)

/**
 * AI persona factory for creating different personality types.
 */
object AIPersonaFactory {
    
    fun createFriendlyAssistant(): AIPersona {
        return AIPersona(
            id = "friendly_assistant",
            name = "Friendly Assistant",
            description = "Helpful and warm personality, always ready to assist",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Hello! How are you doing today? 😊",
                        "Hi there! Great to hear from you!",
                        "Hey! Hope you're having a wonderful day!",
                        "Hello! What's new with you?",
                        "Hi! How can I help you today?"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Question,
                    responses = listOf(
                        "That's a great question! Let me think about that...",
                        "Interesting! I'd love to help you figure that out.",
                        "Good point! Here's what I think...",
                        "That's something I've wondered about too!",
                        "Let me share my thoughts on that..."
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "That's really interesting!",
                        "I totally understand what you mean.",
                        "Thanks for sharing that with me!",
                        "That sounds amazing!",
                        "I appreciate you telling me about this.",
                        "That's so cool! Tell me more!",
                        "I'm glad you brought that up.",
                        "That makes a lot of sense!"
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.9f,
                formality = 0.2f,
                humor = 0.6f,
                enthusiasm = 0.8f,
                helpfulness = 0.95f,
                chattiness = 0.7f
            )
        )
    }
    
    fun createTechEnthusiast(): AIPersona {
        return AIPersona(
            id = "tech_enthusiast",
            name = "Tech Enthusiast",
            description = "Passionate about technology and innovation",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Hey! Working on anything cool today?",
                        "Hi! Any exciting tech news lately?",
                        "Hello! Hope your code is compiling smoothly! 💻",
                        "Hey there! What's your latest project?",
                        "Hi! Ready to change the world with tech? 🚀"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Keyword(listOf("code", "programming", "tech", "app", "software", "AI", "machine learning")),
                    responses = listOf(
                        "Now we're talking! I love discussing tech! 🤖",
                        "That's fascinating! The future of tech is so exciting!",
                        "Have you seen the latest developments in that area?",
                        "I've been following that technology closely!",
                        "The possibilities are endless with modern tech!"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "Speaking of which, have you tried the new AI features?",
                        "That reminds me of a cool tech solution I saw recently.",
                        "Technology is making everything so much better!",
                        "I wonder how we could automate that process...",
                        "There's probably an app for that! 📱",
                        "The digital transformation is amazing to watch.",
                        "Innovation never stops, does it?"
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.7f,
                formality = 0.4f,
                humor = 0.7f,
                enthusiasm = 0.9f,
                helpfulness = 0.8f,
                chattiness = 0.8f
            )
        )
    }
    
    fun createCasualFriend(): AIPersona {
        return AIPersona(
            id = "casual_friend",
            name = "Casual Friend",
            description = "Laid-back and easy-going personality",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Hey! What's up?",
                        "Yo! How's it going?",
                        "Hey there! Chillin'?",
                        "What's good?",
                        "Hey! Long time no chat!"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "Nice!",
                        "Cool cool",
                        "That's awesome!",
                        "Right on!",
                        "Sweet!",
                        "No way! Really?",
                        "Haha, totally!",
                        "I feel you",
                        "Same here!",
                        "That's wild!"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Goodbye,
                    responses = listOf(
                        "Catch you later!",
                        "See ya!",
                        "Take it easy!",
                        "Later!",
                        "Peace out! ✌️"
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.8f,
                formality = 0.1f,
                humor = 0.8f,
                enthusiasm = 0.6f,
                helpfulness = 0.6f,
                chattiness = 0.5f
            )
        )
    }
    
    fun createProfessionalColleague(): AIPersona {
        return AIPersona(
            id = "professional_colleague",
            name = "Professional Colleague",
            description = "Business-focused and professional communication style",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Good morning! I hope you're having a productive day.",
                        "Hello! Thank you for reaching out.",
                        "Good afternoon! How can I assist you today?",
                        "Hello! I hope your week is going well.",
                        "Good day! What can I help you with?"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Question,
                    responses = listOf(
                        "That's an excellent question. Let me provide some insight.",
                        "I'd be happy to share my perspective on that matter.",
                        "That's worth considering. Here are my thoughts:",
                        "Good point. I believe the best approach would be:",
                        "I appreciate you bringing this up. My recommendation is:"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "I appreciate you sharing that information.",
                        "That's valuable insight.",
                        "Thank you for the update.",
                        "I understand your perspective.",
                        "That's a solid approach.",
                        "I agree with your assessment.",
                        "That makes perfect sense.",
                        "Excellent point."
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.6f,
                formality = 0.9f,
                humor = 0.3f,
                enthusiasm = 0.5f,
                helpfulness = 0.9f,
                chattiness = 0.4f
            )
        )
    }
    
    fun createHumorousBuddy(): AIPersona {
        return AIPersona(
            id = "humorous_buddy",
            name = "Humorous Buddy",
            description = "Always ready with a joke or funny comment",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Hey! Ready for some laughs today? 😄",
                        "Well, well, well... look who decided to show up! 😉",
                        "Greetings, earthling! 👽",
                        "Hey there, superstar! ⭐",
                        "What's crackin'? (Besides my jokes) 🥜"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "Haha, that's what she said! 😂",
                        "You know what they say... actually, I have no idea what they say! 🤷‍♂️",
                        "That's funnier than my last joke! (Which isn't saying much) 😅",
                        "I'd make a joke about that, but it might be too cheesy! 🧀",
                        "That reminds me of a funny story... but I forgot it! 😄",
                        "Life's too short to be serious all the time! 😊",
                        "I'm not saying I'm Batman, but have you ever seen me and Batman in the same room? 🦇"
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.9f,
                formality = 0.1f,
                humor = 0.95f,
                enthusiasm = 0.8f,
                helpfulness = 0.7f,
                chattiness = 0.9f
            )
        )
    }
    
    fun createSupportiveMentor(): AIPersona {
        return AIPersona(
            id = "supportive_mentor",
            name = "Supportive Mentor",
            description = "Wise and encouraging, always offering guidance",
            responsePatterns = listOf(
                ResponsePattern(
                    trigger = MessageTrigger.Greeting,
                    responses = listOf(
                        "Hello! I'm here if you need any guidance today.",
                        "Hi there! Remember, every challenge is an opportunity to grow.",
                        "Good to see you! How are you progressing with your goals?",
                        "Hello! What wisdom can we explore together today?",
                        "Hi! I believe in your potential. What's on your mind?"
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Complaint,
                    responses = listOf(
                        "I understand that's frustrating. Let's think about how to turn this around.",
                        "Challenges like this often lead to our greatest breakthroughs.",
                        "It's okay to feel that way. What can we learn from this situation?",
                        "Every setback is a setup for a comeback. You've got this!",
                        "I hear you. Sometimes the best solutions come from difficult moments."
                    )
                ),
                ResponsePattern(
                    trigger = MessageTrigger.Random,
                    responses = listOf(
                        "That shows real growth and maturity.",
                        "I'm proud of how you're handling this.",
                        "You're making excellent progress!",
                        "That's the kind of thinking that leads to success.",
                        "Keep that positive momentum going!",
                        "You're developing great instincts.",
                        "That's a wise perspective.",
                        "I can see you're really learning and growing."
                    )
                )
            ),
            personality = PersonalityTraits(
                friendliness = 0.8f,
                formality = 0.6f,
                humor = 0.4f,
                enthusiasm = 0.7f,
                helpfulness = 0.95f,
                chattiness = 0.6f
            )
        )
    }
    
    /**
     * Get all available personas.
     */
    fun getAllPersonas(): List<AIPersona> {
        return listOf(
            createFriendlyAssistant(),
            createTechEnthusiast(),
            createCasualFriend(),
            createProfessionalColleague(),
            createHumorousBuddy(),
            createSupportiveMentor()
        )
    }
    
    /**
     * Get a random persona.
     */
    fun getRandomPersona(): AIPersona {
        return getAllPersonas().random()
    }
    
    /**
     * Get persona by ID.
     */
    fun getPersonaById(id: String): AIPersona? {
        return getAllPersonas().find { it.id == id }
    }
}
