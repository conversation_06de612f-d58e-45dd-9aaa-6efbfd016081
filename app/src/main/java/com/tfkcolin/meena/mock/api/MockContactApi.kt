package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.data.MockDataGenerator
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of ContactApi for testing and development.
 */
@Singleton
class MockContactApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : ContactApi {
    
    override suspend fun getContacts(
        authToken: String,
        limit: Int,
        offset: Int,
        relationship: String?
    ): Response<ContactListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Get contacts for the user
            val allContacts = mockDataStorage.getContacts(userId)
            
            // Filter by relationship if specified
            val filteredContacts = if (relationship != null) {
                allContacts.filter { it.relationship == relationship }
            } else {
                allContacts
            }
            
            // Sort by last interaction (most recent first)
            val sortedContacts = filteredContacts.sortedByDescending { it.lastInteractionAt ?: "0" }
            
            // Apply pagination
            val paginatedContacts = sortedContacts.drop(offset).take(limit)
            
            // Convert to response format
            val contactResponses = paginatedContacts.map { contact ->
                val contactUser = mockDataStorage.getUser(contact.contactId)
                ContactResponse(
                    id = contact.id,
                    userId = contact.userId,
                    contactId = contact.contactId,
                    displayName = contact.displayName,
                    relationship = contact.relationship,
                    notes = contact.notes,
                    createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                    updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                    user = contactUser?.let { user ->
                        UserProfile(
                            id = user.userId,
                            handle = user.userHandle,
                            displayName = user.displayName,
                            avatarUrl = user.profilePictureUrl,
                            status = if (user.isActive) "online" else "offline",
                            lastSeen = user.lastSeenAt,
                            bio = user.bio
                        )
                    }
                )
            }
            
            val response = ContactListResponse(
                contacts = contactResponses.map { it.toContact() },
                totalCount = filteredContacts.size
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun addContact(
        authToken: String,
        request: AddContactRequest
    ): Response<ContactResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the user to add as contact
            val users = mockDataStorage.getUsers()
            val contactUser = users.find {
                it.userHandle == request.userHandle ||
                it.email == request.userHandle ||
                it.phoneNumber == request.userHandle
            } ?: return createErrorResponse(404, "User not found")
            
            // Check if contact already exists
            val existingContacts = mockDataStorage.getContacts(userId)
            if (existingContacts.any { it.contactId == contactUser.userId }) {
                return createErrorResponse(409, "Contact already exists")
            }
            
            // Create new contact
            val contact = Contact(
                id = UUID.randomUUID().toString(),
                userId = userId,
                contactId = contactUser.userId,
                displayName = request.displayName ?: contactUser.displayName,
                relationship = "friend",
                notes = request.notes,
                createdAt = System.currentTimeMillis().toString(),
                updatedAt = System.currentTimeMillis().toString(),
                isFavorite = false,
                lastInteractionAt = System.currentTimeMillis().toString(),
                user = UserProfile(
                    id = contactUser.userId,
                    handle = contactUser.userHandle,
                    displayName = contactUser.displayName,
                    avatarUrl = contactUser.profilePictureUrl,
                    status = if (contactUser.isActive) "online" else "offline",
                    lastSeen = contactUser.lastSeenAt,
                    bio = contactUser.bio
                )
            )
            
            // Save contact
            mockDataStorage.addContact(userId, contact)
            
            // Create response
            val contactResponse = ContactResponse(
                id = contact.id,
                userId = contact.userId,
                contactId = contact.contactId,
                displayName = contact.displayName,
                relationship = contact.relationship,
                notes = contact.notes,
                createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                user = contact.user
            )
            
            return Response.success(contactResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun updateContact(
        authToken: String,
        contactId: String,
        request: UpdateContactRequest
    ): Response<ContactResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")
            
            // Update contact
            val updatedContact = contact.copy(
                displayName = request.displayName ?: contact.displayName,
                notes = request.notes ?: contact.notes,
                relationship = request.relationship ?: contact.relationship,
                updatedAt = System.currentTimeMillis().toString()
            )
            
            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contactId)
            mockDataStorage.addContact(userId, updatedContact)
            
            val contactResponse = ContactResponse(
                id = updatedContact.id,
                userId = updatedContact.userId,
                contactId = updatedContact.contactId,
                displayName = updatedContact.displayName,
                relationship = updatedContact.relationship,
                notes = updatedContact.notes,
                createdAt = updatedContact.createdAt ?: System.currentTimeMillis().toString(),
                updatedAt = updatedContact.updatedAt ?: System.currentTimeMillis().toString(),
                user = updatedContact.user
            )
            
            return Response.success(contactResponse)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun deleteContact(
        authToken: String,
        contactId: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Check if contact exists
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")
            
            // Remove contact
            mockDataStorage.removeContact(userId, contactId)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    

    
    override suspend fun getFavoriteContacts(
        authToken: String,
        limit: Int,
        offset: Int
    ): Response<ContactListResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Get favorite contacts for the user
            val allContacts = mockDataStorage.getContacts(userId)
            val favoriteContacts = allContacts.filter { it.isFavorite }

            // Sort by last interaction (most recent first)
            val sortedContacts = favoriteContacts.sortedByDescending { it.lastInteractionAt ?: "0" }

            // Apply pagination
            val paginatedContacts = sortedContacts.drop(offset).take(limit)

            // Convert to response format
            val contactResponses = paginatedContacts.map { contact ->
                val contactUser = mockDataStorage.getUser(contact.contactId)
                ContactResponse(
                    id = contact.id,
                    userId = contact.userId,
                    contactId = contact.contactId,
                    displayName = contact.displayName,
                    relationship = contact.relationship,
                    notes = contact.notes,
                    createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                    updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                    user = contactUser?.let { user ->
                        UserProfile(
                            id = user.userId,
                            handle = user.userHandle,
                            displayName = user.displayName,
                            avatarUrl = user.profilePictureUrl,
                            status = if (user.isActive) "online" else "offline",
                            lastSeen = user.lastSeenAt,
                            bio = user.bio
                        )
                    }
                )
            }

            val response = ContactListResponse(
                contacts = contactResponses.map { it.toContact() },
                totalCount = favoriteContacts.size
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun getContactById(
        authToken: String,
        contactId: String
    ): Response<ContactResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")

            val contactResponse = ContactResponse(
                id = contact.id,
                userId = contact.userId,
                contactId = contact.contactId,
                displayName = contact.displayName,
                relationship = contact.relationship,
                notes = contact.notes,
                createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                user = contact.user
            )

            return Response.success(contactResponse)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun searchContacts(
        authToken: String,
        query: String,
        limit: Int,
        offset: Int
    ): Response<ContactListResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Search in user's contacts
            val allContacts = mockDataStorage.getContacts(userId)
            val searchResults = allContacts.filter { contact ->
                contact.displayName?.contains(query, ignoreCase = true) == true ||
                contact.user?.handle?.contains(query, ignoreCase = true) == true ||
                contact.user?.displayName?.contains(query, ignoreCase = true) == true
            }

            // Apply pagination
            val paginatedContacts = searchResults.drop(offset).take(limit)

            // Convert to response format
            val contactResponses = paginatedContacts.map { contact ->
                ContactResponse(
                    id = contact.id,
                    userId = contact.userId,
                    contactId = contact.contactId,
                    displayName = contact.displayName,
                    relationship = contact.relationship,
                    notes = contact.notes,
                    createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                    updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                    user = contact.user
                )
            }

            val response = ContactListResponse(
                contacts = contactResponses.map { it.toContact() },
                totalCount = searchResults.size
            )

            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun getRecentContacts(
        authToken: String,
        limit: Int
    ): Response<ContactListResponse> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Get contacts for the user and sort by last interaction
            val allContacts = mockDataStorage.getContacts(userId)
            val recentContacts = allContacts
                .filter { it.lastInteractionAt != null }
                .sortedByDescending { it.lastInteractionAt }
                .take(limit)

            // Convert to response format
            val contactResponses = recentContacts.map { contact ->
                ContactResponse(
                    id = contact.id,
                    userId = contact.userId,
                    contactId = contact.contactId,
                    displayName = contact.displayName,
                    relationship = contact.relationship,
                    notes = contact.notes,
                    createdAt = contact.createdAt ?: System.currentTimeMillis().toString(),
                    updatedAt = contact.updatedAt ?: System.currentTimeMillis().toString(),
                    user = contact.user
                )
            }

            val response = ContactListResponse(
                contacts = contactResponses.map { it.toContact() },
                totalCount = recentContacts.size
            )

            return Response.success(response)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun addToFavorites(
        authToken: String,
        contactId: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")

            // Update contact to favorite
            val updatedContact = contact.copy(
                isFavorite = true,
                updatedAt = System.currentTimeMillis().toString()
            )

            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contactId)
            mockDataStorage.addContact(userId, updatedContact)

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    override suspend fun removeFromFavorites(
        authToken: String,
        contactId: String
    ): Response<Unit> {
        simulateNetworkDelay()

        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")

            // Find the contact
            val contacts = mockDataStorage.getContacts(userId)
            val contact = contacts.find { it.id == contactId }
                ?: return createErrorResponse(404, "Contact not found")

            // Update contact to not favorite
            val updatedContact = contact.copy(
                isFavorite = false,
                updatedAt = System.currentTimeMillis().toString()
            )

            // Remove old contact and add updated one
            mockDataStorage.removeContact(userId, contactId)
            mockDataStorage.addContact(userId, updatedContact)

            return Response.success(Unit)

        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }

    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
