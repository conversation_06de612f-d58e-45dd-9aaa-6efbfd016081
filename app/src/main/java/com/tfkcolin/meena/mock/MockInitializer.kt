package com.tfkcolin.meena.mock

import android.content.Context
import androidx.startup.Initializer
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.mock.storage.MockDataStorage
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Initializes mock data when the app starts in mock mode.
 * This class is responsible for triggering the initialization of MockDataStorage.
 */
@Singleton
class MockInitializer @Inject constructor(
    private val mockDataStorage: MockDataStorage
) {

    private val initializationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * Initialize mock data if needed.
     * This will ensure a current user exists and mock data is set up.
     */
    fun initialize() {
        if (AppConfig.useMockBackend) {
            initializationScope.launch {
                try {
                    mockDataStorage.initializeIfNeeded()
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Mock data initialization triggered successfully.")
                    }
                } catch (e: Exception) {
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Error initializing mock data: ${e.message}")
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    // Delegate methods to MockDataStorage for DeveloperMenuViewModel
    suspend fun getCurrentMockUser() = mockDataStorage.getCurrentUser()
    suspend fun getAllMockUsers() = mockDataStorage.getUsers()
    suspend fun getMockChats() = mockDataStorage.getChats(mockDataStorage.tokenManager.getUserId() ?: "")
    suspend fun resetMockData() = mockDataStorage.clearAllData()
}

/**
 * App Startup initializer for mock data.
 */
class MockDataInitializer : Initializer<MockInitializer> {

    override fun create(context: Context): MockInitializer {
        // Get the MockInitializer from Hilt
        val entryPoint = EntryPointAccessors.fromApplication(
            context,
            MockInitializerEntryPoint::class.java
        )

        val mockInitializer = entryPoint.mockInitializer()
        mockInitializer.initialize()

        return mockInitializer
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}

/**
 * Hilt entry point for accessing MockInitializer during app startup.
 */
@dagger.hilt.EntryPoint
@dagger.hilt.InstallIn(dagger.hilt.components.SingletonComponent::class)
interface MockInitializerEntryPoint {
    fun mockInitializer(): MockInitializer
}
