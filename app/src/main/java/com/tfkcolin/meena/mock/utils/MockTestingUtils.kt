package com.tfkcolin.meena.mock.utils

import com.tfkcolin.meena.mock.storage.MockDataStorage
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for managing mock data for testing purposes.
 */
@Singleton
class MockTestingUtils @Inject constructor(
    private val mockDataStorage: MockDataStorage
) {

    /**
     * Reset all mock data in the application.
     * This clears all users, contacts, chats, and messages from the local database
     * and re-initializes the mock data storage with a fresh current user.
     */
    suspend fun resetAllMockData() {
        mockDataStorage.clearAllData()
        mockDataStorage.initializeIfNeeded()
    }
}
