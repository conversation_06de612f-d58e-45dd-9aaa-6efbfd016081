package com.tfkcolin.meena.mock.ai

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.mock.data.MockDataGenerator
import kotlinx.coroutines.delay
import kotlin.random.Random

/**
 * Generates mock AI responses for testing chat functionalities.
 */
class AIResponseGenerator {

    private val conversationHistory = mutableMapOf<String, MutableList<ConversationMessage>>()

    data class ConversationMessage(
        val content: String,
        val isFromUser: Boolean,
        val timestamp: Long = System.currentTimeMillis()
    )

    private val predefinedResponses = listOf(
        "Okay.",
        "Understood.",
        "Thanks for the message.",
        "Got it.",
        "Interesting.",
        "Tell me more.",
        "I see.",
        "Right.",
        "Acknowledged."
    )

    /**
     * Generate a response to a user message from a mock AI contact.
     *
     * @param userMessage The message sent by the user.
     * @param chatId The ID of the chat.
     * @param mockAIContactId The user ID of the mock AI contact responding.
     * @return An AIResponse containing the generated text and interaction cues.
     */
    suspend fun generateResponse(
        userMessage: String,
        chatId: String,
        mockAIContactId: String
    ): AIResponse {
        // Add user message to conversation history
        addToHistory(chatId, ConversationMessage(userMessage, true))

        // Simulate thinking delay
        val delayDuration = Random.nextLong(
            AppConfig.MockConfig.AI_RESPONSE_DELAY_MIN,
            AppConfig.MockConfig.AI_RESPONSE_DELAY_MAX
        )
        delay(delayDuration)

        // Generate response: either echo or a random predefined string
        val responseText = if (Random.nextBoolean()) {
            "Echo: \"$userMessage\"" // Echo the user's message
        } else {
            predefinedResponses.random() // Random predefined response
        }

        // Add AI response to conversation history
        addToHistory(chatId, ConversationMessage(responseText, false))

        return AIResponse(
            text = responseText,
            shouldShowTyping = true, // Always show typing for mock AI
            typingDuration = delayDuration // Typing duration matches delay
        )
    }

    /**
     * Add message to conversation history.
     */
    private fun addToHistory(chatId: String, message: ConversationMessage) {
        val history = conversationHistory.getOrPut(chatId) { mutableListOf() }
        history.add(message)

        // Keep only last 20 messages to prevent memory issues
        if (history.size > 20) {
            history.removeAt(0)
        }
    }

    /**
     * Clear conversation history for a chat.
     */
    fun clearHistory(chatId: String) {
        conversationHistory.remove(chatId)
    }

    /**
     * Get conversation history for a chat.
     */
    fun getHistory(chatId: String): List<ConversationMessage> {
        return conversationHistory[chatId]?.toList() ?: emptyList()
    }
}

/**
 * AI response data class.
 */
data class AIResponse(
    val text: String,
    val shouldShowTyping: Boolean,
    val typingDuration: Long
)
