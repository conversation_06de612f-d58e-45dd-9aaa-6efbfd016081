<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/auth_navigation"
    app:startDestination="@id/welcomeScreen">

    <fragment
        android:id="@+id/welcomeScreen"
        android:name="com.tfkcolin.meena.ui.auth.WelcomeScreen"
        android:label="Welcome"
        tools:layout="@layout/fragment_welcome">
        <action
            android:id="@+id/action_welcomeScreen_to_registrationScreen"
            app:destination="@id/registrationScreen" />
        <action
            android:id="@+id/action_welcomeScreen_to_loginScreen"
            app:destination="@id/loginScreen" />
    </fragment>

    <fragment
        android:id="@+id/registrationScreen"
        android:name="com.tfkcolin.meena.ui.auth.RegistrationScreen"
        android:label="Registration"
        tools:layout="@layout/fragment_registration">
        <action
            android:id="@+id/action_registrationScreen_to_uniqueIdScreen"
            app:destination="@id/uniqueIdScreen" />
    </fragment>

    <fragment
        android:id="@+id/uniqueIdScreen"
        android:name="com.tfkcolin.meena.ui.auth.UniqueIdScreen"
        android:label="Unique ID"
        tools:layout="@layout/fragment_unique_id">
        <action
            android:id="@+id/action_uniqueIdScreen_to_secretPhraseScreen"
            app:destination="@id/secretPhraseScreen" />
    </fragment>

    <fragment
        android:id="@+id/secretPhraseScreen"
        android:name="com.tfkcolin.meena.ui.auth.SecretPhraseScreen"
        android:label="Secret Phrase"
        tools:layout="@layout/fragment_secret_phrase">
        <action
            android:id="@+id/action_secretPhraseScreen_to_setupPinScreen"
            app:destination="@id/setupPinScreen" />
    </fragment>

    <fragment
        android:id="@+id/setupPinScreen"
        android:name="com.tfkcolin.meena.ui.auth.SetupPinScreen"
        android:label="Setup PIN"
        tools:layout="@layout/fragment_setup_pin">
        <action
            android:id="@+id/action_setupPinScreen_to_mainNavigation"
            app:destination="@id/mainNavigation" />
    </fragment>

    <fragment
        android:id="@+id/loginScreen"
        android:name="com.tfkcolin.meena.ui.auth.LoginScreen"
        android:label="Login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginScreen_to_mainNavigation"
            app:destination="@id/mainNavigation" />
    </fragment>

    <navigation
        android:id="@+id/mainNavigation"
        app:startDestination="@id/homeScreen">
        <!-- This is a placeholder for the main navigation graph -->
        <fragment
            android:id="@+id/homeScreen"
            android:name="com.tfkcolin.meena.ui.home.HomeScreen"
            android:label="Home"
            tools:layout="@layout/fragment_home" />
    </navigation>
</navigation>
