<resources>
    <string name="app_name">Meena</string>

    <!-- Error Messages -->
    <string name="error_unknown">An unexpected error occurred. Please try again.</string>
    <string name="error_no_internet">No internet connection. Please check your network settings.</string>
    <string name="error_timeout">Connection timed out. Please try again.</string>
    <string name="error_network">Network error. Please check your connection and try again.</string>
    <string name="error_api">Server error. Please try again later.</string>
    <string name="error_auth">Authentication failed. Please log in again.</string>
    <string name="error_validation">Invalid input. Please check your entries.</string>
    <string name="error_file">File operation failed. Please try again.</string>
    <string name="error_database">Database operation failed. Please try again.</string>
    <string name="error_websocket">Connection error. Please try again.</string>
    <string name="error_upload">Failed to upload file. Please try again.</string>
    <string name="error_download">Failed to download file. Please try again.</string>
    <string name="error_message_send">Failed to send message. Please try again.</string>
    <string name="error_group_create">Failed to create group. Please try again.</string>
    <string name="error_group_update">Failed to update group. Please try again.</string>
    <string name="error_group_leave">Failed to leave group. Please try again.</string>
    <string name="error_contact_add">Failed to add contact. Please try again.</string>
    <string name="error_contact_remove">Failed to remove contact. Please try again.</string>
    <string name="error_contact_block">Failed to block contact. Please try again.</string>
    <string name="error_contact_unblock">Failed to unblock contact. Please try again.</string>

    <!-- Media Operation Messages -->
    <string name="media_upload_success">File uploaded successfully.</string>
    <string name="media_download_success">File downloaded successfully.</string>
    <string name="media_upload_progress">Uploading file: %1$d%%</string>
    <string name="media_download_progress">Downloading file: %1$d%%</string>
    <string name="media_upload_cancelled">File upload cancelled.</string>
    <string name="media_download_cancelled">File download cancelled.</string>

    <!-- Account Recovery Screen Strings -->
    <string name="account_recovery_title">Account Recovery</string>
    <string name="recover_account_headline">Recover Your Account</string>
    <string name="recover_account_description">Enter your Meena ID, recovery phrase, and recovery PIN to reset your password.</string>
    <string name="meena_id_registration_hint">Enter the 9-character Meena ID you received during registration</string>
    <string name="meena_id_label">Meena ID</string>
    <string name="meena_id_placeholder">Your 9-character Meena ID</string>
    <string name="recovery_phrase_registration_hint">Enter the 9-word recovery phrase you received during registration</string>
    <string name="recovery_phrase_label">Recovery Phrase (9 words)</string>
    <string name="recovery_phrase_placeholder">Enter all 9 words separated by spaces</string>
    <string name="recovery_pin_registration_hint">Enter the 6-digit PIN you created during registration</string>
    <string name="recovery_pin_label">Recovery PIN (6 digits)</string>
    <string name="pin_length_error">PIN must be 6 digits</string>
    <string name="new_password_label">New Password</string>
    <string name="confirm_new_password_label">Confirm New Password</string>
    <string name="recover_account_button">Recover Account</string>
    <string name="back_to_sign_in_button">Back to Sign In</string>

    <!-- Login Screen Strings (newly extracted) -->
    <string name="login_title">Log In</string>
    <string name="welcome_to_meena">Welcome to Meena</string>
    <string name="sign_in_to_your_account">Sign in to your account</string>
    <string name="password_label">Password</string>
    <string name="forgot_password_button">Forgot Password?</string>
    <string name="sign_in_button">Sign In</string>
    <string name="dont_have_account_question">Don\'t have an account?</string>
    <string name="create_account_button">Create Account</string>

    <!-- Registration Screen Strings (newly extracted) -->
    <string name="create_your_account_title">Create Your Account</string>
    <string name="enter_display_name_description">Enter your display name to get started</string>
    <string name="display_name_label">Display Name</string>
    <string name="continue_button">Continue</string>

    <!-- Secret Phrase Screen Strings (newly extracted) -->
    <string name="secret_recovery_phrase_title">Secret Recovery Phrase</string>
    <string name="your_secret_recovery_phrase_headline">Your Secret Recovery Phrase</string>
    <string name="secret_phrase_instruction">This is your secret recovery phrase. Write it down and keep it safe:</string>
    <string name="loading_text">Loading…</string>
    <string name="secret_phrase_important_warning">⚠️ IMPORTANT: If you lose your MEENA ID or PIN, you\'ll need this secret phrase to recover your account. No one can help you recover it if lost.</string>
    <string name="secret_phrase_confirmation_checkbox">I have saved my secret phrase in a secure location</string>

    <!-- Setup PIN Screen Strings (newly extracted) -->
    <string name="set_your_pin_title">Set Your PIN</string>
    <string name="set_your_pin_headline">Set Your PIN</string>
    <string name="set_pin_description">Choose a secure PIN to protect your MEENA account</string>
    <string name="pin_label">PIN</string>
    <string name="confirm_pin_label">Confirm PIN</string>
    <string name="pins_do_not_match_error">PINs do not match</string>
    <string name="complete_registration_button">Complete Registration</string>

    <!-- Two-Factor Authentication Screen Strings (newly extracted) -->
    <string name="two_factor_auth_title">Two-Factor Authentication</string>
    <string name="two_factor_auth_description">Enter the verification code from your authenticator app.</string>
    <string name="verification_code_label">Verification Code</string>
    <string name="verify_button">Verify</string>

    <!-- Unique ID Screen Strings (newly extracted) -->
    <string name="your_meena_id_title">Your MEENA ID</string>
    <string name="your_unique_meena_id_headline">Your Unique MEENA ID</string>
    <string name="unique_meena_id_instruction">This is your unique MEENA ID. You\'ll use it to log in:</string>
    <string name="meena_id_important_warning">⚠️ IMPORTANT: Write down your MEENA ID and keep it safe. You\'ll need it to log in to your account.</string>
    <string name="meena_id_confirmation_checkbox">I have saved my MEENA ID in a secure location</string>

    <!-- Unique Welcome Screen Strings (newly extracted) -->
    <string name="meena_logo_content_description">Meena Logo</string>
    <string name="app_name_meena">MEENA</string>
    <string name="app_tagline_secure_messaging">Ultra-secure messaging</string>
    <string name="create_account_no_info_prompt">Create an account without a phone number or personal information:</string>
    <string name="create_new_account_button">Create a new account</string>
    <string name="already_a_member_question">Already a member?</string>
    <string name="login_button">Connexion</string>

    <!-- Chat List Screen Strings -->
    <string name="chats_screen_title">Chats</string>
    <string name="show_active_chats_content_description">Show Active Chats</string>
    <string name="show_archived_chats_content_description">Show Archived Chats</string>
    <string name="refresh_content_description">Refresh</string>
    <string name="new_group_content_description">New Group</string>
    <string name="new_group_button">New Group</string>
    <string name="new_chat_content_description">New Chat</string>
    <string name="new_chat_button">New Chat</string>
    <string name="close_menu_content_description">Close Menu</string>
    <string name="open_menu_content_description">Open Menu</string>
    <string name="no_chats_headline">No Chats</string>
    <string name="start_new_conversation_prompt">Start a new conversation by tapping the + button</string>
    <string name="no_messages_yet">No messages yet</string>
    <string name="delete_chat_dialog_title">Delete Chat</string>
    <string name="delete_chat_dialog_message">Are you sure you want to delete this chat? This action cannot be undone.</string>
    <string name="delete_button">Delete</string>
    <string name="cancel_button">Cancel</string>
    <string name="no_direct_messages">No direct messages yet</string>
    <string name="no_groups">No groups yet</string>
    <string name="no_channels">No channels yet</string>

    <!-- Chat Screen Strings -->
    <string name="delete_for_me_title">Delete for Me</string>
    <string name="delete_for_me_message">This message will be deleted for you. Other people in the chat will still be able to see it.</string>
    <string name="delete_for_me_button">Delete for Me</string>
    <string name="delete_for_everyone_title">Delete for Everyone</string>
    <string name="delete_for_everyone_message">This message will be deleted for everyone in the chat. This cannot be undone.</string>
    <string name="delete_for_everyone_button">Delete for Everyone</string>
    <string name="toast_camera_not_implemented">Camera not implemented yet</string>
    <string name="toast_video_not_implemented">Video not implemented yet</string>
    <string name="toast_audio_not_implemented">Audio not implemented yet</string>
    <string name="message_reply_sender_yourself">yourself</string>
    <string name="typing_indicator_text">Typing</string>
    <string name="cancel_reply_content_description">Cancel reply</string>
    <string name="attachments_selected_text">%1$d attachment(s) selected</string>
    <string name="clear_attachments_content_description">Clear attachments</string>
    <string name="type_reply_placeholder">Type a reply…</string>
    <string name="type_message_placeholder">Type a message</string>
    <string name="send_button_content_description">Send</string>
    <string name="attach_file_content_description">Attach file</string>

    <!-- Chat Search Screen Strings -->
    <string name="search_in_chat_title">Search in Chat</string>
    <string name="search_in_this_chat_placeholder">Search in this chat</string>
    <string name="no_results_found_headline">No results found</string>
    <string name="try_different_search_term_prompt">Try a different search term</string>
    <string name="enter_search_term_to_find_messages">Enter a search term to find messages in this chat</string>

    <!-- Forward Message Screen Strings -->
    <string name="message_forwarded_successfully">Message forwarded successfully</string>
    <string name="forward_message_title">Forward Message</string>
    <string name="message_preview_headline">Message Preview</string>
    <string name="original_message_content_placeholder">Original message content would be shown here</string>
    <string name="add_comment_optional_label">Add a comment (optional)</string>
    <string name="select_chat_to_forward_to_prompt">Select a chat to forward to:</string>
    <string name="forward_button">Forward</string>

    <!-- New Chat Screen Strings -->
    <string name="new_chat_screen_title">New Chat</string>
    <string name="search_contacts_placeholder">Search contacts</string>
    <string name="search_content_description">Search</string>
    <string name="no_contacts_found_headline">No Contacts Found</string>
    <string name="contact_item_default_user_name">User</string>
    <string name="contact_item_id_prefix">ID: %1$s</string>

    <string name="search_messages_title">Search Messages</string>
    <string name="search_messages_placeholder">Search messages</string>
    <string name="enter_search_term_to_find_messages_global">Enter a search term to find messages</string>

    <!-- Add Contact Screen Strings -->
    <string name="contact_added_successfully">Contact added successfully</string>
    <string name="add_contact_screen_title">Add Contact</string>
    <string name="scan_qr_code_content_description">Scan QR Code</string>
    <string name="add_new_contact_headline">Add a New Contact</string>
    <string name="add_contact_instruction">Enter the Meena ID of the person you want to add to your contacts, or scan their QR code.</string>
    <string name="display_name_optional_label">Display Name (Optional)</string>
    <string name="relationship_friend">Friend</string>
    <string name="relationship_family">Family</string>
    <string name="relationship_colleague">Colleague</string>
    <string name="relationship_acquaintance">Acquaintance</string>
    <string name="notes_optional_label">Notes (Optional)</string>
    <string name="add_contact_action_button">Add Contact</string>
    <string name="or_scan_qr_code_prompt">Or scan a QR code</string>
    <string name="scan_qr_code_button">Scan QR Code</string>

    <!-- Contact Detail Screen Strings -->
    <string name="contact_blocked_successfully_message">Contact blocked successfully</string>
    <string name="contact_unblocked_successfully_message">Contact unblocked successfully</string>
    <string name="contact_added_to_favorites_message">Contact added to favorites</string>
    <string name="contact_removed_from_favorites_message">Contact removed from favorites</string>
    <string name="contact_details_screen_title">Contact Details</string>
    <string name="edit_contact_content_description">Edit Contact</string>
    <string name="contact_not_found_headline">Contact not found</string>
    <string name="go_back_button">Go Back</string>
    <string name="blocked_content_description">Blocked</string>
    <string name="blocked_status">Blocked</string>
    <string name="contact_information_headline">Contact Information</string>
    <string name="user_id_label">User ID:</string>
    <string name="relationship_label">Relationship:</string>
    <string name="relationship_none">None</string>
    <string name="notes_label">Notes:</string>
    <string name="added_on_label">Added on:</string>
    <string name="unknown_date">Unknown</string>
    <string name="last_active_label">Last active:</string>
    <string name="message_button">Message</string>
    <string name="remove_from_favorites_button">Remove from Favorites</string>
    <string name="add_to_favorites_button">Add to Favorites</string>
    <string name="unblock_contact_button">Unblock Contact</string>
    <string name="block_contact_button">Block Contact</string>
    <string name="block_contact_dialog_title">Block Contact</string>
    <string name="block_contact_dialog_message">Are you sure you want to block this contact? They will not be able to message you or call you.</string>
    <string name="block_button_text">Block</string>
    <string name="unblock_contact_dialog_title">Unblock Contact</string>
    <string name="unblock_contact_dialog_message">Are you sure you want to unblock this contact? They will be able to message you and call you again.</string>
    <string name="unblock_button_text">Unblock</string>

    <!-- Contact Group Detail Screen Strings -->
    <string name="edit_group">Edit Group</string>
    <string name="delete_group">Delete Group</string>
    <string name="add_member">Add Member</string>
    <string name="group_details">Group Details</string>
    <string name="enter_new_group_name">Enter a new name for the group</string>
    <string name="group_name_label">Group Name</string>
    <string name="save_button">Save</string>
    <string name="confirm_delete_group_message">Are you sure you want to delete this group? This action cannot be undone.</string>
    <string name="select_contact_to_add">Select a contact to add to the group</string>
    <string name="all_contacts_in_group">All contacts are already in this group</string>
    <string name="add_button">Add</string>
    <string name="group_not_found">Group not found</string>
    <string name="no_members_in_group">No members in this group</string>
    <string name="members_title">Members</string>
    <string name="group_updated_successfully">Group updated successfully</string>
    <string name="group_deleted_successfully">Group deleted successfully</string>
    <string name="member_added_to_group">Member added to group</string>
    <string name="member_removed_from_group">Member removed from group</string>
    <string name="message_group_button">Message Group</string>
    <string name="share_group_button">Share Group</string>

    <!-- Contact Groups Screen Strings -->
    <string name="contact_groups">Contact Groups</string>
    <string name="create_group">Create Group</string>
    <string name="enter_name_for_new_group">Enter a name for the new group</string>
    <string name="create_button">Create</string>
    <string name="group_created_successfully">Group created successfully</string>
    <string name="no_contact_groups">No Contact Groups</string>
    <string name="create_your_first_group">Create your first group by tapping the + button</string>

    <!-- Contact List Screen -->
    <string name="contacts_title">Contacts</string>
    <string name="add_contact_button">Add Contact</string>
    <string name="search_button">Search</string>
    <string name="contact_groups_button">Contact Groups</string>
    <string name="refresh_button">Refresh</string>
    <string name="contact_updated_successfully">Contact updated successfully</string>
    <string name="contact_blocked_successfully">Contact blocked successfully</string>
    <string name="contact_unblocked_successfully">Contact unblocked successfully</string>
    <string name="no_contacts">No Contacts</string>
    <string name="add_your_first_contact">Add your first contact by tapping the + button</string>
    <string name="favorites_section">Favorites</string>
    <string name="recent_section">Recent</string>
    <string name="all_contacts_section">All Contacts</string>
    <string name="search_results">Search Results</string>
    <string name="no_contacts_found_for_query">No contacts found for %s</string>

    <!-- Edit Contact Screen Strings -->
    <string name="search_icon_description">Search</string>
    <string name="contact_groups_icon_description">Contact Groups</string>
    <string name="refresh_icon_description">Refresh</string>
    <string name="add_contact_icon_description">Add Contact</string>
    <string name="edit_contact">Edit Contact</string>
    <string name="contact_not_found">Contact not found</string>
    <string name="notes_label_optional">Notes (Optional)</string>
    <string name="friend">Friend</string>
    <string name="family">Family</string>
    <string name="colleague">Colleague</string>
    <string name="acquaintance">Acquaintance</string>
    <string name="save_changes_button">Save Changes</string>
    <string name="update_information_for_this_contact">Update the information for this contact.</string>

    <!-- Group Creation Screen strings -->
    <string name="group_description_label_optional">Group Description (Optional)</string>
    <string name="add_members_button">Add Members</string>
    <string name="add_members_with_count">%1$d selected</string>
    <string name="group_name_cannot_be_empty">Group name cannot be empty</string>
    <string name="public_chat">Public</string>
    <string name="private_chat">Private</string>
    <string name="secret">Secret</string>
    <string name="public_group_description">Anyone can find and join this group</string>
    <string name="private_group_description">Only people who are invited can join</string>
    <string name="secret_group_description">This group is not discoverable and messages are end-to-end encrypted</string>
    <string name="back_icon_description">Back</string>
    <string name="create_group_icon_description">Create Group</string>
    <string name="add_group_photo">Add Group Photo</string>
    <string name="privacy_type_title">Privacy Type</string>

    <!-- Group Details Screen -->
    <string name="group_details_title">Group Details</string>
    <string name="change_avatar">Change Avatar</string>
    <string name="edit_name">Edit Name</string>
    <string name="edit_description">Edit Description</string>
    <string name="edit_group_name">Edit Group Name</string>
    <string name="member_options">Member Options</string>
    <string name="member">Member</string>
    <string name="edit_group_description">Edit Group Description</string>
    <string name="leave_group">Leave Group</string>
    <string name="are_you_sure_leave_group">Are you sure you want to leave this group?</string>
    <string name="leave">Leave</string>
    <string name="cancel">Cancel</string>
    <string name="remove_member">Remove Member</string>
    <string name="are_you_sure_remove_member">Are you sure you want to remove this member from the group?</string>
    <string name="remove">Remove</string>
    <string name="make_admin">Make Admin</string>
    <string name="make_this_member_admin">Make this member an admin? Admins can add or remove members and change group settings.</string>
    <string name="make_admin_button">Make Admin</string>
    <string name="remove_admin">Remove Admin</string>
    <string name="remove_admin_privileges">Remove admin privileges from this member?</string>
    <string name="remove_admin_button">Remove Admin</string>
    <string name="public_group">Public Group</string>
    <string name="private_group">Private Group</string>
    <string name="secret_group">Secret Group</string>
    <string name="members_label">%d members</string>
    <string name="add_description">Add Description</string>
    <string name="members_section">Members</string>
    <string name="add_members">Add Members</string>
    <string name="you_label">(You)</string>
    <string name="admin_label">Admin</string>
    <string name="make_admin_menu">Make Admin</string>
    <string name="remove_admin_menu">Remove Admin</string>
    <string name="remove_from_group">Remove from Group</string>
    <string name="more_options">More Options</string>
    <string name="leave_group_menu">Leave Group</string>
    <string name="save">Save</string>

    <!-- Edit Profile Screen -->
    <string name="edit_profile_title">Edit Profile</string>
    <string name="edit_your_profile">Edit Your Profile</string>
    <string name="profile_visibility_info">This information will be visible to other users.</string>
    <string name="bio_label">Bio (Optional)</string>
    <string name="meena_id_title">Your Meena ID (Cannot be changed)</string>
    <string name="meena_id_description">This is your unique identifier in Meena. Share it with friends to connect.</string>
    <string name="save_changes">Save Changes</string>

    <!-- Profile Screen -->
    <string name="profile_title">Profile</string>
    <string name="user_data_not_available">User data not available.</string>
    <string name="anonymous_user">Anonymous User</string>
    <string name="show_qr_code">Show QR Code</string>
    <string name="navigate">Navigate</string>
    <string name="meena_id_copied">Meena ID copied!</string>
    <string name="copy_id">Copy ID</string>
    <string name="edit_profile">Edit Profile</string>
    <string name="security">Security</string>
    <string name="notifications">Notifications</string>
    <string name="appearance">Appearance</string>
    <string name="language">Language</string>
    <string name="logout">Logout</string>
    <string name="confirm_logout">Confirm Logout</string>
    <string name="logout_confirmation_message">Are you sure you want to log out?</string>

    <!-- Security Settings Screen -->
    <string name="security_settings_title">Security Settings</string>
    <string name="back">Back</string>
    <string name="account_recovery">Account Recovery</string>
    <string name="account_recovery_description">Your recovery phrase is the only way to recover your account if you lose access. Keep it safe and secure.</string>
    <string name="recovery_phrase">Recovery Phrase</string>
    <string name="view_your_recovery_phrase">View your recovery phrase</string>
    <string name="recovery_pin">Recovery PIN</string>
    <string name="set_a_pin_for_additional_recovery">Optional: Set a PIN for additional recovery</string>
    <string name="remote_wipe_pin">Remote Wipe PIN</string>
    <string name="set_a_pin_to_remotely_wipe_data">Optional: Set a PIN to remotely wipe data</string>
    <string name="device_security">Device Security</string>
    <string name="secure_your_app_on_this_device">Secure your app on this device with these optional settings.</string>
    <string name="biometric_authentication">Biometric Authentication</string>
    <string name="use_fingerprint_or_face_id_to_unlock">Use fingerprint or face ID to unlock</string>
    <string name="screen_lock">Screen Lock</string>
    <string name="lock_the_app_when_not_in_use">Lock the app when not in use</string>

    <!-- Appearance Settings -->
    <string name="theme">Theme</string>
    <string name="choose_how_the_app_appears_on_your_device">Choose how the app appears on your device</string>
    <string name="system_default">System default</string>
    <string name="follow_your_devices_theme_settings">Follow your device\'s theme settings</string>
    <string name="light">Light</string>
    <string name="always_use_light_theme">Always use light theme</string>
    <string name="dark">Dark</string>
    <string name="always_use_dark_theme">Always use dark theme</string>

    <!-- Language Settings -->
    <string name="select_language">Select Language</string>
    <string name="choose_your_preferred_language_for_the_app">Choose your preferred language for the app</string>

    <!-- Notifications Settings -->
    <string name="control_how_and_when_you_receive_notifications">Control how and when you receive notifications</string>
    <string name="enable_notifications">Enable Notifications</string>
    <string name="receive_notifications_for_new_messages_and_events">Receive notifications for new messages and events</string>
    <string name="message_previews">Message Previews</string>
    <string name="show_message_content_in_notifications">Show message content in notifications</string>
    <string name="chat_features">Chat Features</string>
    <string name="control_chat_related_features_and_notifications">Control chat-related features and notifications</string>
    <string name="read_receipts">Read Receipts</string>
    <string name="let_others_know_when_youve_read_their_messages">Let others know when you\'ve read their messages</string>
    <string name="typing_indicators">Typing Indicators</string>
    <string name="let_others_know_when_youre_typing">Let others know when you\'re typing</string>

    <!-- Stories Screen -->
    <string name="stories">Stories</string>
    <string name="your_stories">Your Stories</string>
    <string name="friends_stories">Friends\' Stories</string>
    <string name="an_error_occurred">An error occurred</string>

    <!-- Story Creation Screen -->
    <string name="create_story">Create Story</string>
    <string name="close">Close</string>
    <string name="share">Share</string>
    <string name="add_text">Add text</string>
    <string name="add_sticker">Add sticker</string>
    <string name="draw">Draw</string>
    <string name="pick_image">Pick image</string>
    <string name="take_photo">Take photo</string>
    <string name="add_a_caption">Add a caption&#8230;</string>
    <string name="tap_to_add_a_photo_or_video">Tap to add a photo or video</string>
    <string name="camera">Camera</string>
    <string name="gallery">Gallery</string>
    <string name="enter_text_here">Enter text here&#8230;</string>
    <string name="done">Done</string>

    <!-- Story Viewer Screen -->
    <string name="video_content">Video Content</string>
    <string name="close_story">Close story</string>
    <string name="reply_to_story_placeholder">Reply to story&#8230;</string>
    <string name="cancel_reply">Cancel reply</string>
    <string name="send_reply">Send reply</string>
    <string name="reply_to_author_placeholder">Reply to %s&#8230;</string>
</resources>