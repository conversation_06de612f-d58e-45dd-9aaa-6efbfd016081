package com.tfkcolin.meena.mock
//
//import android.content.Context
//import android.util.Log
//import com.google.gson.Gson
//import com.tfkcolin.meena.data.models.AuthResponse
//import com.tfkcolin.meena.data.models.ContactListResponse
//import com.tfkcolin.meena.data.models.ContactResponse
//import com.tfkcolin.meena.data.models.Tokens
//import okhttp3.mockwebserver.Dispatcher
//import okhttp3.mockwebserver.MockResponse
//import okhttp3.mockwebserver.MockWebServer
//import okhttp3.mockwebserver.RecordedRequest
//import java.util.UUID
//import java.util.concurrent.TimeUnit
//
///**
// * Mock server for testing the app.
// */
//class MockServer(private val context: Context) {
//
//    private val mockWebServer = MockWebServer()
//    private val gson = Gson()
//
//    // In-memory storage for mock data
//    private val users = mutableMapOf<String, MockUser>()
//    private val contacts = mutableMapOf<String, MutableList<MockContact>>()
//
//    /**
//     * Start the mock server.
//     *
//     * @return The URL of the mock server.
//     */
//    fun start(): String {
//        // Add some mock data
//        addMockData()
//
//        // Set up the dispatcher
//        mockWebServer.dispatcher = object : Dispatcher() {
//            override fun dispatch(request: RecordedRequest): MockResponse {
//                val path = request.path?.split("?")?.firstOrNull() ?: ""
//                val method = request.method ?: ""
//
//                Log.d("MockServer", "Received request: $method $path")
//
//                return when {
//                    // Auth endpoints
//                    path == "/auth/register" && method == "POST" -> handleRegister(request)
//                    path == "/auth/login" && method == "POST" -> handleLogin(request)
//                    path == "/auth/login/2fa" && method == "POST" -> handleTwoFactorAuth(request)
//                    path == "/auth/refresh" && method == "POST" -> handleRefreshToken(request)
//                    path == "/auth/recovery" && method == "POST" -> handleRecovery(request)
//                    path == "/auth/password" && method == "POST" -> handlePasswordChange(request)
//
//                    // Contact endpoints
//                    path == "/contacts" && method == "GET" -> handleGetContacts(request)
//                    path == "/contacts" && method == "POST" -> handleAddContact(request)
//                    path.startsWith("/contacts/") && method == "PATCH" -> handleUpdateContact(request)
//                    path.contains("/block") && method == "POST" -> handleBlockContact(request)
//                    path.contains("/unblock") && method == "POST" -> handleUnblockContact(request)
//
//                    // Default response
//                    else -> MockResponse().setResponseCode(404).setBody("Not found")
//                }
//            }
//        }
//
//        // Start the server
//        mockWebServer.start()
//        return mockWebServer.url("/").toString()
//    }
//
//    /**
//     * Stop the mock server.
//     */
//    fun shutdown() {
//        mockWebServer.shutdown()
//    }
//
//    /**
//     * Add mock data to the server.
//     */
//    private fun addMockData() {
//        // Add some mock users
//        val user1 = MockUser(
//            userId = UUID.randomUUID().toString(),
//            userHandle = "user1",
//            displayName = "User One",
//            email = "<EMAIL>",
//            phoneNumber = "+1234567890",
//            password = "password123",
//            profilePictureUrl = null,
//            bio = "I am user one",
//            recoveryPhrase = "apple banana cherry dog elephant frog giraffe hippo iguana",
//            recoveryPin = "123456",
//            remoteWipePin = "654321"
//        )
//
//        val user2 = MockUser(
//            userId = UUID.randomUUID().toString(),
//            userHandle = "user2",
//            displayName = "User Two",
//            email = "<EMAIL>",
//            phoneNumber = "+0987654321",
//            password = "password123",
//            profilePictureUrl = null,
//            bio = "I am user two",
//            recoveryPhrase = "jungle kangaroo lion monkey nightingale octopus penguin quail rabbit",
//            recoveryPin = "123456",
//            remoteWipePin = "654321"
//        )
//
//        users[user1.userHandle] = user1
//        users[user2.userHandle] = user2
//
//        // Add some mock contacts
//        val contact1 = MockContact(
//            userId = user1.userId,
//            contactUserId = user2.userId,
//            contactUserHandle = user2.userHandle,
//            displayName = "My Friend",
//            isBlocked = false,
//            createdAt = "2023-01-01T00:00:00Z"
//        )
//
//        val contact2 = MockContact(
//            userId = user2.userId,
//            contactUserId = user1.userId,
//            contactUserHandle = user1.userHandle,
//            displayName = "My Other Friend",
//            isBlocked = false,
//            createdAt = "2023-01-01T00:00:00Z"
//        )
//
//        contacts[user1.userId] = mutableListOf(contact1)
//        contacts[user2.userId] = mutableListOf(contact2)
//    }
//
//    /**
//     * Handle a registration request.
//     */
//    private fun handleRegister(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val registerRequest = gson.fromJson(body, Map::class.java)
//
//            val userHandle = registerRequest["user_handle"] as? String ?: generateUserHandle()
//            val password = registerRequest["password"] as? String
//            val email = registerRequest["email"] as? String
//            val phoneNumber = registerRequest["phone_number"] as? String
//
//            // Validate request
//            if (password.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Password is required")
//            }
//
//            // Check if user handle is already taken
//            if (users.containsKey(userHandle)) {
//                return MockResponse().setResponseCode(409).setBody("User handle already exists")
//            }
//
//            // Create new user
//            val userId = UUID.randomUUID().toString()
//            val recoveryPhrase = "apple banana cherry dog elephant frog giraffe hippo iguana"
//
//            val user = MockUser(
//                userId = userId,
//                userHandle = userHandle,
//                displayName = null,
//                email = email,
//                phoneNumber = phoneNumber,
//                password = password,
//                profilePictureUrl = null,
//                bio = null,
//                recoveryPhrase = recoveryPhrase,
//                recoveryPin = null,
//                remoteWipePin = null
//            )
//
//            users[userHandle] = user
//            contacts[userId] = mutableListOf()
//
//            // Create response
//            val authResponse = AuthResponse(
//                userId = userId,
//                userHandle = userHandle,
//                tokens = Tokens(
//                    accessToken = "mock_access_token_$userId",
//                    refreshToken = "mock_refresh_token_$userId"
//                ),
//                requires2fa = false,
//                recoveryPhrase = recoveryPhrase
//            )
//
//            return MockResponse()
//                .setResponseCode(201)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(authResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling register request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a login request.
//     */
//    private fun handleLogin(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val loginRequest = gson.fromJson(body, Map::class.java)
//
//            val identifier = loginRequest["identifier"] as? String
//            val password = loginRequest["password"] as? String
//
//            // Validate request
//            if (identifier.isNullOrBlank() || password.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Identifier and password are required")
//            }
//
//            // Find user by identifier (handle, email, or phone)
//            val user = users.values.find {
//                it.userHandle == identifier || it.email == identifier || it.phoneNumber == identifier
//            }
//
//            // Check if user exists and password is correct
//            if (user == null || user.password != password) {
//                return MockResponse().setResponseCode(401).setBody("Invalid credentials")
//            }
//
//            // Create response
//            val authResponse = AuthResponse(
//                userId = user.userId,
//                userHandle = user.userHandle,
//                tokens = Tokens(
//                    accessToken = "mock_access_token_${user.userId}",
//                    refreshToken = "mock_refresh_token_${user.userId}"
//                ),
//                requires2fa = false
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(authResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling login request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a two-factor authentication request.
//     */
//    private fun handleTwoFactorAuth(request: RecordedRequest): MockResponse {
//        // For simplicity, just return success
//        try {
//            val body = request.body.readUtf8()
//            val twoFactorRequest = gson.fromJson(body, Map::class.java)
//
//            val userId = twoFactorRequest["user_id"] as? String
//            val code = twoFactorRequest["code"] as? String
//
//            // Validate request
//            if (userId.isNullOrBlank() || code.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("User ID and code are required")
//            }
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(404).setBody("User not found")
//            }
//
//            // For simplicity, accept any code
//            val authResponse = AuthResponse(
//                userId = user.userId,
//                userHandle = user.userHandle,
//                tokens = Tokens(
//                    accessToken = "mock_access_token_${user.userId}",
//                    refreshToken = "mock_refresh_token_${user.userId}"
//                ),
//                requires2fa = false
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(authResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling 2FA request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a token refresh request.
//     */
//    private fun handleRefreshToken(request: RecordedRequest): MockResponse {
//        // For simplicity, just return new tokens
//        try {
//            val body = request.body.readUtf8()
//            val refreshRequest = gson.fromJson(body, Map::class.java)
//
//            val refreshToken = refreshRequest["refresh_token"] as? String
//
//            // Validate request
//            if (refreshToken.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Refresh token is required")
//            }
//
//            // Extract user ID from token (mock implementation)
//            val userId = refreshToken.substringAfter("mock_refresh_token_")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Invalid refresh token")
//            }
//
//            val authResponse = AuthResponse(
//                userId = user.userId,
//                userHandle = user.userHandle,
//                tokens = Tokens(
//                    accessToken = "mock_access_token_${user.userId}_new",
//                    refreshToken = "mock_refresh_token_${user.userId}_new"
//                ),
//                requires2fa = false
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(authResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling refresh token request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle an account recovery request.
//     */
//    private fun handleRecovery(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val recoveryRequest = gson.fromJson(body, Map::class.java)
//
//            val userHandle = recoveryRequest["user_handle"] as? String
//            val recoveryPhrase = recoveryRequest["recovery_phrase"] as? String
//            val recoveryPin = recoveryRequest["recovery_pin"] as? String
//            val newPassword = recoveryRequest["new_password"] as? String
//
//            // Validate request
//            if (userHandle.isNullOrBlank() || recoveryPhrase.isNullOrBlank() ||
//                recoveryPin.isNullOrBlank() || newPassword.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("All fields are required")
//            }
//
//            // Find user by handle
//            val user = users[userHandle]
//
//            if (user == null) {
//                return MockResponse().setResponseCode(404).setBody("User not found")
//            }
//
//            // For simplicity, accept any recovery phrase and PIN
//            // Update password
//            user.password = newPassword
//
//            val authResponse = AuthResponse(
//                userId = user.userId,
//                userHandle = user.userHandle,
//                tokens = Tokens(
//                    accessToken = "mock_access_token_${user.userId}",
//                    refreshToken = "mock_refresh_token_${user.userId}"
//                ),
//                requires2fa = false
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(authResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling recovery request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a password change request.
//     */
//    private fun handlePasswordChange(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val passwordChangeRequest = gson.fromJson(body, Map::class.java)
//
//            val currentPassword = passwordChangeRequest["current_password"] as? String
//            val newPassword = passwordChangeRequest["new_password"] as? String
//
//            // Validate request
//            if (currentPassword.isNullOrBlank() || newPassword.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Current and new passwords are required")
//            }
//
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Check if current password is correct
//            if (user.password != currentPassword) {
//                return MockResponse().setResponseCode(401).setBody("Current password is incorrect")
//            }
//
//            // Update password
//            user.password = newPassword
//
//            return MockResponse().setResponseCode(200)
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling password change request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a get contacts request.
//     */
//    private fun handleGetContacts(request: RecordedRequest): MockResponse {
//        try {
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Get contacts for user
//            val userContacts = contacts[userId] ?: mutableListOf()
//
//            // Convert to response format
//            val contactResponses = userContacts.map { contact ->
//                val contactUser = users.values.find { it.userId == contact.contactUserId }
//
//                ContactResponse(
//                    userId = contact.contactUserId,
//                    userHandle = contact.contactUserHandle,
//                    displayName = contact.displayName ?: contactUser?.displayName,
//                    profilePictureUrl = contactUser?.profilePictureUrl,
//                    isBlocked = contact.isBlocked
//                )
//            }
//
//            val contactListResponse = ContactListResponse(
//                contacts = contactResponses,
//                totalCount = contactResponses.size
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(contactListResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling get contacts request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle an add contact request.
//     */
//    private fun handleAddContact(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val addContactRequest = gson.fromJson(body, Map::class.java)
//
//            val contactUserHandle = addContactRequest["contact_user_handle"] as? String
//            val displayName = addContactRequest["display_name"] as? String
//
//            // Validate request
//            if (contactUserHandle.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Contact user handle is required")
//            }
//
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Find contact user by handle
//            val contactUser = users[contactUserHandle]
//
//            if (contactUser == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact user not found")
//            }
//
//            // Check if contact already exists
//            val userContacts = contacts[userId] ?: mutableListOf()
//
//            if (userContacts.any { it.contactUserId == contactUser.userId }) {
//                return MockResponse().setResponseCode(409).setBody("Contact already exists")
//            }
//
//            // Add contact
//            val contact = MockContact(
//                userId = userId,
//                contactUserId = contactUser.userId,
//                contactUserHandle = contactUser.userHandle,
//                displayName = displayName,
//                isBlocked = false,
//                createdAt = "2023-01-01T00:00:00Z"
//            )
//
//            userContacts.add(contact)
//            contacts[userId] = userContacts
//
//            // Create response
//            val contactResponse = ContactResponse(
//                userId = contactUser.userId,
//                userHandle = contactUser.userHandle,
//                displayName = displayName ?: contactUser.displayName,
//                profilePictureUrl = contactUser.profilePictureUrl,
//                isBlocked = false
//            )
//
//            return MockResponse()
//                .setResponseCode(201)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(contactResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling add contact request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle an update contact request.
//     */
//    private fun handleUpdateContact(request: RecordedRequest): MockResponse {
//        try {
//            val body = request.body.readUtf8()
//            val updateContactRequest = gson.fromJson(body, Map::class.java)
//
//            val displayName = updateContactRequest["display_name"] as? String
//
//            // Validate request
//            if (displayName.isNullOrBlank()) {
//                return MockResponse().setResponseCode(400).setBody("Display name is required")
//            }
//
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Extract contact user handle from path
//            val path = request.path ?: ""
//            val contactUserHandle = path.substringAfter("/contacts/").substringBefore("/")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Find contact user by handle
//            val contactUser = users[contactUserHandle]
//
//            if (contactUser == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact user not found")
//            }
//
//            // Find contact
//            val userContacts = contacts[userId] ?: mutableListOf()
//            val contact = userContacts.find { it.contactUserId == contactUser.userId }
//
//            if (contact == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact not found")
//            }
//
//            // Update contact
//            contact.displayName = displayName
//
//            // Create response
//            val contactResponse = ContactResponse(
//                userId = contactUser.userId,
//                userHandle = contactUser.userHandle,
//                displayName = displayName,
//                profilePictureUrl = contactUser.profilePictureUrl,
//                isBlocked = contact.isBlocked
//            )
//
//            return MockResponse()
//                .setResponseCode(200)
//                .setHeader("Content-Type", "application/json")
//                .setBody(gson.toJson(contactResponse))
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling update contact request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle a block contact request.
//     */
//    private fun handleBlockContact(request: RecordedRequest): MockResponse {
//        try {
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Extract contact user handle from path
//            val path = request.path ?: ""
//            val contactUserHandle = path.substringAfter("/contacts/").substringBefore("/block")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Find contact user by handle
//            val contactUser = users[contactUserHandle]
//
//            if (contactUser == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact user not found")
//            }
//
//            // Find contact
//            val userContacts = contacts[userId] ?: mutableListOf()
//            val contact = userContacts.find { it.contactUserId == contactUser.userId }
//
//            if (contact == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact not found")
//            }
//
//            // Block contact
//            contact.isBlocked = true
//
//            return MockResponse().setResponseCode(200)
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling block contact request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Handle an unblock contact request.
//     */
//    private fun handleUnblockContact(request: RecordedRequest): MockResponse {
//        try {
//            // Extract user ID from token (mock implementation)
//            val authHeader = request.getHeader("Authorization") ?: ""
//            val token = authHeader.substringAfter("Bearer ").trim()
//            val userId = token.substringAfter("mock_access_token_").substringBefore("_new")
//
//            // Extract contact user handle from path
//            val path = request.path ?: ""
//            val contactUserHandle = path.substringAfter("/contacts/").substringBefore("/unblock")
//
//            // Find user by ID
//            val user = users.values.find { it.userId == userId }
//
//            if (user == null) {
//                return MockResponse().setResponseCode(401).setBody("Unauthorized")
//            }
//
//            // Find contact user by handle
//            val contactUser = users[contactUserHandle]
//
//            if (contactUser == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact user not found")
//            }
//
//            // Find contact
//            val userContacts = contacts[userId] ?: mutableListOf()
//            val contact = userContacts.find { it.contactUserId == contactUser.userId }
//
//            if (contact == null) {
//                return MockResponse().setResponseCode(404).setBody("Contact not found")
//            }
//
//            // Unblock contact
//            contact.isBlocked = false
//
//            return MockResponse().setResponseCode(200)
//
//        } catch (e: Exception) {
//            Log.e("MockServer", "Error handling unblock contact request", e)
//            return MockResponse().setResponseCode(500).setBody("Internal server error")
//        }
//    }
//
//    /**
//     * Generate a random user handle.
//     */
//    private fun generateUserHandle(): String {
//        val chars = "abcdefghijklmnopqrstuvwxyz0123456789"
//        return (1..9).map { chars.random() }.joinToString("")
//    }
//
//    /**
//     * Mock user data class.
//     */
//    data class MockUser(
//        val userId: String,
//        val userHandle: String,
//        var displayName: String?,
//        val email: String?,
//        val phoneNumber: String?,
//        var password: String,
//        val profilePictureUrl: String?,
//        val bio: String?,
//        val recoveryPhrase: String?,
//        val recoveryPin: String?,
//        val remoteWipePin: String?
//    )
//
//    /**
//     * Mock contact data class.
//     */
//    data class MockContact(
//        val userId: String,
//        val contactUserId: String,
//        val contactUserHandle: String,
//        var displayName: String?,
//        var isBlocked: Boolean,
//        val createdAt: String
//    )
//}
