package com.tfkcolin.meena.di
//
//import android.content.Context
//import com.tfkcolin.meena.BuildConfig
//import com.tfkcolin.meena.mock.MockServer
//import dagger.Module
//import dagger.Provides
//import dagger.hilt.InstallIn
//import dagger.hilt.android.qualifiers.ApplicationContext
//import dagger.hilt.components.SingletonComponent
//import javax.inject.Named
//import javax.inject.Singleton
//
///**
// * Module for providing mock server for testing.
// */
//@Module
//@InstallIn(SingletonComponent::class)
//object MockModule {
//
//    /**
//     * Provide the mock server.
//     */
//    @Provides
//    @Singleton
//    fun provideMockServer(@ApplicationContext context: Context): MockServer {
//        return MockServer(context)
//    }
//
//    /**
//     * Provide the base URL for the API.
//     * In debug builds, use the mock server.
//     * In release builds, use the real server.
//     */
//    @Provides
//    @Singleton
//    @Named("baseUrl")
//    fun provideBaseUrl(mockServer: MockServer): String {
//        return if (BuildConfig.DEBUG) {
//            // Start the mock server and return its URL
//            mockServer.start()
//        } else {
//            // Use the real server URL in production
//            "https://api.meena.com/"
//        }
//    }
//}
