{"formatVersion": 1, "database": {"version": 1, "identityHash": "e5ad5077d96d38e87606b2898de5fc80", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `userHandle` TEXT NOT NULL, `displayName` TEXT, `profilePictureUrl` TEXT, `bio` TEXT, `phoneNumber` TEXT, `email` TEXT, `subscriptionTier` TEXT NOT NULL, `verificationStatus` TEXT NOT NULL, `isActive` INTEGER NOT NULL, `createdAt` TEXT, `lastSeenAt` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userHandle", "columnName": "userHandle", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profilePictureUrl", "columnName": "profilePictureUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bio", "columnName": "bio", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subscriptionTier", "columnName": "subscriptionTier", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verificationStatus", "columnName": "verificationStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastSeenAt", "columnName": "lastSeenAt", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "contacts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `contactUserId` TEXT NOT NULL, `displayName` TEXT, `isBlocked` INTEGER NOT NULL, `createdAt` TEXT, PRIMARY KEY(`userId`, `contactUserId`), FOREIGN KEY(`userId`) REFERENCES `users`(`userId`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIG<PERSON> KEY(`contactUserId`) REFERENCES `users`(`userId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contactUserId", "columnName": "contactUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isBlocked", "columnName": "isBlocked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId", "contactUserId"]}, "indices": [{"name": "index_contacts_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_contacts_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_contacts_contactUserId", "unique": false, "columnNames": ["contactUserId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_contacts_contactUserId` ON `${TABLE_NAME}` (`contactUserId`)"}], "foreignKeys": [{"table": "users", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["userId"], "referencedColumns": ["userId"]}, {"table": "users", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["contactUserId"], "referencedColumns": ["userId"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e5ad5077d96d38e87606b2898de5fc80')"]}}