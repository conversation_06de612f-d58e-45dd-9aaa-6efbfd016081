# Database Scripts

This directory contains scripts for managing the database.

## Database Initialization

The `initialize_database_fixed.sql` script is used to initialize or reset the database. It:

1. Drops all existing tables and enum types
2. Creates all tables with the correct structure and relationships
3. Adds all necessary indexes
4. Creates functions for the contacts API

### How to Use

1. Log in to your Railway dashboard
2. Navigate to your PostgreSQL database
3. Open the SQL Editor
4. Copy the contents of `initialize_database_fixed.sql`
5. Paste it into the SQL Editor and run it

This script is the single source of truth for the database schema and should be kept in sync with the master schema file (`docs/database/master_schema.sql`).

## Key Changes

The initialization script includes several important design decisions:

1. **Boolean `is_verified` field**: Uses a simple boolean field instead of an enum for verification status
2. **Proper table dependencies**: Creates tables in the correct order to respect dependencies
3. **Integrated contact queries**: Includes the functions used by the contacts API

## Troubleshooting

If you encounter any issues when running the script:

1. Check the PostgreSQL logs for error messages
2. Verify that all tables were created successfully
3. Test the affected functionality in the application

If you need to make changes to the database schema:

1. Update the master schema file (`docs/database/master_schema.sql`)
2. Update this initialization script
3. Create a migration script if needed for existing databases
