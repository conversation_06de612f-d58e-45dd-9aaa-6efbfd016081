-- Migration script to change from is_verified boolean to verification_status enum
-- This script should be run on the production database to migrate the data

-- Start a transaction so we can roll back if something goes wrong
BEGIN;

-- Create the verification_status_enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status_enum') THEN
        CREATE TYPE verification_status_enum AS ENUM ('none', 'pending', 'verified', 'rejected');
    END IF;
END$$;

-- Add the verification_status column with a default value
ALTER TABLE users ADD COLUMN verification_status verification_status_enum;

-- Migrate data from is_verified to verification_status
UPDATE users SET verification_status = 
    CASE 
        WHEN is_verified = TRUE THEN 'verified'::verification_status_enum 
        ELSE 'none'::verification_status_enum 
    END;

-- Make verification_status NOT NULL
ALTER TABLE users ALTER COLUMN verification_status SET NOT NULL;

-- Set the default value for verification_status
ALTER TABLE users ALTER COLUMN verification_status SET DEFAULT 'none'::verification_status_enum;

-- Drop the is_verified column
ALTER TABLE users DROP COLUMN is_verified;

-- Update database functions that use is_verified
-- Function: get_contacts
CREATE OR REPLACE FUNCTION get_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_blocked = false
    ORDER BY c.is_favorite DESC, c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function: get_favorite_contacts
CREATE OR REPLACE FUNCTION get_favorite_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_favorite = true AND c.is_blocked = false
    ORDER BY c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function: get_contact_by_id
CREATE OR REPLACE FUNCTION get_contact_by_id(p_user_id UUID, p_contact_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.id = p_contact_id;
END;
$$ LANGUAGE plpgsql;

-- Commit the transaction
COMMIT;
