#!/bin/bash

# Schema Generation Script
# This script generates individual schema files from the master schema file

# Set variables
MASTER_SCHEMA_FILE="docs/database/master_schema.sql"
SCHEMA_DIR="backend/internal/database/schema"
TEMP_DIR="/tmp/meena_schema_generation"

# Create temp directory
mkdir -p $TEMP_DIR

echo "Generating schema files from master schema file..."

# Create schema directory if it doesn't exist
mkdir -p $SCHEMA_DIR

# Function to extract a table definition from the master schema file
extract_table_definition() {
    local table_name=$1
    local output_file="$SCHEMA_DIR/${table_name}.sql"

    echo "Extracting schema for $table_name..."

    # Extract the table definition
    awk -v table="CREATE TABLE $table_name" -v table_name="$table_name" '
        $0 ~ table {
            print "-- Create " table_name " table";
            print $0;
            in_table = 1;
            next;
        }
        in_table && $0 ~ /^\);/ {
            print $0;
            in_table = 0;
            next;
        }
        in_table {
            print $0;
        }
    ' $MASTER_SCHEMA_FILE > $output_file

    # Extract indexes for the table
    echo "" >> $output_file
    echo "-- Create indexes for faster lookups" >> $output_file
    grep -E "CREATE INDEX.*ON $table_name" $MASTER_SCHEMA_FILE >> $output_file

    echo "Generated $output_file"
}

# Extract table names from the master schema file
grep -E "^CREATE TABLE" $MASTER_SCHEMA_FILE | sed 's/CREATE TABLE //g' | sed 's/ (//g' > $TEMP_DIR/tables.txt

# Generate individual schema files for each table
while read table_name; do
    extract_table_definition $table_name
done < $TEMP_DIR/tables.txt

# Generate a schema file for enums
echo "Generating enum types schema file..."
echo "-- Enum types for Meena database" > $SCHEMA_DIR/enum_types.sql
awk '/^CREATE TYPE/ && !/--/ {print}' $MASTER_SCHEMA_FILE >> $SCHEMA_DIR/enum_types.sql
echo "Generated $SCHEMA_DIR/enum_types.sql"

# Generate a schema file for extensions
echo "Generating extensions schema file..."
grep -E "^CREATE EXTENSION" $MASTER_SCHEMA_FILE > $SCHEMA_DIR/extensions.sql
echo "Generated $SCHEMA_DIR/extensions.sql"

# Generate a schema file for functions
echo "Generating functions schema file..."
echo "-- Database functions for Meena" > $SCHEMA_DIR/functions.sql

# Extract function definitions
awk '/^CREATE OR REPLACE FUNCTION/ {
    # Extract function name and parameters
    func_name = $0;
    gsub(/^CREATE OR REPLACE FUNCTION /, "", func_name);
    gsub(/\).*$/, ")", func_name);
    print "\n-- Function: " func_name;
    print;
    in_function = 1;
    next;
}
in_function {
    print;
    if ($0 ~ /\$\$ LANGUAGE/) {
        in_function = 0;
    }
}' $MASTER_SCHEMA_FILE >> $SCHEMA_DIR/functions.sql

echo "Generated $SCHEMA_DIR/functions.sql"

# Clean up
rm -rf $TEMP_DIR

echo "Schema files generation complete!"
echo "Generated files are in $SCHEMA_DIR"

exit 0
