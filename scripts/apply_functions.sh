#!/bin/bash

# Apply Database Functions Script
# This script applies only the database functions without resetting the entire database

# Set variables
FUNCTIONS_FILE="scripts/apply_database_functions.sql"
TEMP_DIR="/tmp/meena_db_functions"
LOG_FILE="$TEMP_DIR/db_functions.log"

# Create temp directory
mkdir -p $TEMP_DIR

echo "Applying database functions..."

# Function to get database connection string
get_db_url() {
    if [ -n "$DATABASE_URL" ]; then
        echo $DATABASE_URL
    elif command -v railway &> /dev/null; then
        railway variables get DATABASE_URL
    else
        echo "Error: DATABASE_URL not set and Railway CLI not available"
        exit 1
    fi
}

# Get database URL
DB_URL=$(get_db_url)
if [ -z "$DB_URL" ]; then
    exit 1
fi

# Apply the functions
echo "Applying functions from $FUNCTIONS_FILE..."
psql "$DB_URL" -f "$FUNCTIONS_FILE" > "$LOG_FILE" 2>&1

# Check if the functions were applied successfully
if [ $? -eq 0 ]; then
    echo "Functions applied successfully!"
else
    echo "Error applying functions. Check $LOG_FILE for details."
    exit 1
fi

# Verify the functions
echo "Verifying database functions..."
psql "$DB_URL" -c "
    SELECT proname, proargnames, prosrc
    FROM pg_proc
    WHERE proname IN ('get_contacts', 'get_favorite_contacts', 'get_contact_by_id')
    ORDER BY proname;
"

echo "Database functions application complete!"
exit 0
