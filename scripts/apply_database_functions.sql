-- Apply database functions for contacts
-- This script adds or updates the database functions for contacts without resetting the database

-- Create a function to get all contacts for a user
CREATE OR REPLACE FUNCTION get_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified B<PERSON><PERSON>EA<PERSON>,
    contact_is_gold_member B<PERSON><PERSON>EA<PERSON>,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_blocked = false
    ORDER BY c.is_favorite DESC, c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get favorite contacts for a user
CREATE OR REPLACE FUNCTION get_favorite_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_favorite = true AND c.is_blocked = false
    ORDER BY c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get a contact by ID
CREATE OR REPLACE FUNCTION get_contact_by_id(p_user_id UUID, p_contact_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.id = p_contact_id;
END;
$$ LANGUAGE plpgsql;
