#!/bin/bash

# Database Initialization Script
# This script initializes the database with the schema defined in initialize_database_fixed.sql
# It can be used to set up a new database from scratch

# Set variables
SCHEMA_FILE="scripts/initialize_database_fixed.sql"
TEMP_DIR="/tmp/meena_db_init"
LOG_FILE="$TEMP_DIR/db_init.log"

# Create temp directory
mkdir -p $TEMP_DIR

echo "Initializing database from master schema..."

# Function to get database connection string
get_db_url() {
    if [ -n "$DATABASE_URL" ]; then
        echo $DATABASE_URL
    elif command -v railway &> /dev/null; then
        railway variables get DATABASE_URL
    else
        echo "Error: DATABASE_URL not set and Railway CLI not available"
        exit 1
    fi
}

# Get database URL
DB_URL=$(get_db_url)
if [ -z "$DB_URL" ]; then
    exit 1
fi

# Apply the schema
echo "Applying schema from $SCHEMA_FILE..."
psql "$DB_URL" -f "$SCHEMA_FILE" > "$LOG_FILE" 2>&1

# Check if the schema was applied successfully
if [ $? -eq 0 ]; then
    echo "Schema applied successfully!"
else
    echo "Error applying schema. Check $LOG_FILE for details."
    exit 1
fi

# Verify the database structure
echo "Verifying database structure..."
psql "$DB_URL" -c "
    SELECT table_name, 'Created successfully' AS status
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name;
"

echo "Database initialization complete!"
exit 0
