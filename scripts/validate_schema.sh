#!/bin/bash

# Schema Validation Script
# This script validates that the actual database schema matches the master schema file

# Set variables
MASTER_SCHEMA_FILE="docs/database/master_schema.sql"
TEMP_DIR="/tmp/meena_schema_validation"
ACTUAL_SCHEMA_FILE="$TEMP_DIR/actual_schema.sql"
DIFF_FILE="$TEMP_DIR/schema_diff.txt"

# Create temp directory
mkdir -p $TEMP_DIR

# Function to get database connection string
get_db_url() {
    if [ -n "$DATABASE_URL" ]; then
        echo $DATABASE_URL
    elif command -v railway &> /dev/null; then
        railway variables get DATABASE_URL
    else
        echo "Error: DATABASE_URL not set and Railway CLI not available"
        exit 1
    fi
}

# Get database URL
DB_URL=$(get_db_url)
if [ -z "$DB_URL" ]; then
    exit 1
fi

echo "Validating database schema against master schema file..."

# Dump the actual schema
echo "Dumping actual database schema..."
pg_dump --schema-only --no-owner --no-privileges --no-tablespaces --no-security-labels \
    --no-publications --no-subscriptions --no-comments \
    "$DB_URL" > $ACTUAL_SCHEMA_FILE

if [ $? -ne 0 ]; then
    echo "Error: Failed to dump database schema"
    exit 1
fi

# Clean up the schema files for comparison
# This removes comments, empty lines, and other non-essential differences
clean_schema_file() {
    local input_file=$1
    local output_file="$input_file.clean"
    
    # Remove comments, empty lines, and normalize whitespace
    grep -v "^--" $input_file | grep -v "^$" | sed 's/[ \t]\+/ /g' > $output_file
    
    # Sort the file to make comparison easier
    sort $output_file > "$output_file.sorted"
    
    # Return the path to the cleaned and sorted file
    echo "$output_file.sorted"
}

# Clean the schema files
CLEAN_MASTER=$(clean_schema_file $MASTER_SCHEMA_FILE)
CLEAN_ACTUAL=$(clean_schema_file $ACTUAL_SCHEMA_FILE)

# Compare the schemas
echo "Comparing schemas..."
diff -u $CLEAN_MASTER $CLEAN_ACTUAL > $DIFF_FILE

# Check if there are differences
if [ -s $DIFF_FILE ]; then
    echo "Schema validation failed! Differences found:"
    cat $DIFF_FILE
    echo ""
    echo "Please update the master schema file or fix the database schema."
    exit 1
else
    echo "Schema validation passed! The database schema matches the master schema file."
fi

# Clean up
rm -rf $TEMP_DIR

exit 0
