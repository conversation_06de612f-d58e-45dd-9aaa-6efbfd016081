package com.tfkcolin.meena.ui.viewmodels

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.UploadProgress
import com.tfkcolin.meena.data.services.MediaService
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.FileUtils
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import kotlinx.coroutines.flow.map

/**
 * UI state for media operations.
 */
data class MediaUiState(
    val properties: UiStateProperties = UiStateProperties(),
    val uploadOperation: OperationState = OperationState(),
    val downloadOperation: OperationState = OperationState(),
    val selectedMedia: List<Uri> = emptyList(),
    val uploadedAttachments: List<MediaAttachment> = emptyList(),
    val downloadedFiles: Map<String, File> = emptyMap()
) : UiState {
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error

    val isUploading: Boolean get() = uploadOperation.isInProgress
    val isDownloading: Boolean get() = downloadOperation.isInProgress
}

/**
 * ViewModel for media operations.
 */
@HiltViewModel
class MediaViewModel @Inject constructor(
    private val mediaService: MediaService,
    private val fileUtils: FileUtils,
    val mediaAttachmentHelper: MediaAttachmentHelper,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state
    private val _uiState = MutableStateFlow(MediaUiState())
    val uiState: StateFlow<MediaUiState> = _uiState.asStateFlow()

    // Map to store upload progress by ID
    private val _uploadProgressMap = MutableStateFlow<Map<String, UploadProgress>>(emptyMap())
    val uploadProgressMap: StateFlow<Map<String, UploadProgress>> = _uploadProgressMap.asStateFlow()

    // Map to store download progress by ID
    private val _downloadProgressMap = MutableStateFlow<Map<String, DownloadProgress>>(emptyMap())
    val downloadProgressMap: StateFlow<Map<String, DownloadProgress>> = _downloadProgressMap.asStateFlow()

    /**
     * Upload a media file.
     *
     * @param uri The URI of the media file.
     * @param contentType The content type of the file.
     * @return The uploaded media attachment.
     */
    fun uploadMedia(uri: Uri, contentType: String) {
        viewModelScope.launch {
            // Update state to indicate upload is in progress
            _uiState.update { it.copy(
                uploadOperation = it.uploadOperation.start()
            ) }

            // Upload the media
            val result = mediaService.uploadMedia(uri, contentType)

            // Update state based on result
            if (result.isSuccess) {
                val attachment = result.getOrNull()!!
                _uiState.update { it.copy(
                    uploadOperation = it.uploadOperation.success(),
                    uploadedAttachments = it.uploadedAttachments + attachment
                ) }

                // Observe upload progress
                attachment.uploadId?.let { uploadId ->
                    viewModelScope.launch {
                        mediaService.observeUploadProgress(uploadId).collect { progress ->
                            _uploadProgressMap.update { map ->
                                map + (attachment.id to progress)
                            }
                        }
                    }
                }
            } else {
                val error = result.exceptionOrNull()?.message ?: "Unknown error"
                _uiState.update { it.copy(
                    uploadOperation = it.uploadOperation.failure(error),
                    properties = it.properties.copy(error = error)
                ) }
            }
        }
    }

    /**
     * Download a media file.
     *
     * @param url The URL of the media file.
     * @param fileName The name to save the file as.
     */
    fun downloadMedia(url: String, fileName: String) {
        viewModelScope.launch {
            // Update state to indicate download is in progress
            _uiState.update { it.copy(
                downloadOperation = it.downloadOperation.start()
            ) }

            // Download the media
            val result = mediaService.downloadMedia(url, fileName)

            // Update state based on result
            if (result.isSuccess) {
                val file = result.getOrNull()!!
                _uiState.update { it.copy(
                    downloadOperation = it.downloadOperation.success(),
                    downloadedFiles = it.downloadedFiles + (url to file)
                ) }
            } else {
                val error = result.exceptionOrNull()?.message ?: "Unknown error"
                _uiState.update { it.copy(
                    downloadOperation = it.downloadOperation.failure(error),
                    properties = it.properties.copy(error = error)
                ) }
            }
        }
    }

    /**
     * Download a media attachment.
     *
     * @param attachment The media attachment to download.
     */
    fun downloadMediaAttachment(attachment: MediaAttachment) {
        val url = attachment.url
        val fileName = attachment.fileName ?: "download_${System.currentTimeMillis()}"
        downloadMedia(url, fileName)
    }

    /**
     * Get attachments for a message.
     *
     * @param messageId The ID of the message.
     * @return A flow of media attachments.
     */
    fun getAttachmentsForMessage(messageId: String): Flow<List<MediaAttachment>> {
        return mediaService.getAttachmentsForMessage(messageId)
    }

    /**
     * Cancel an upload.
     *
     * @param id The ID of the upload to cancel.
     */
    fun cancelUpload(id: String) {
        viewModelScope.launch {
            mediaService.cancelUpload(id)
        }
    }

    /**
     * Cancel a download.
     *
     * @param id The ID of the download to cancel.
     */
    fun cancelDownload(id: String) {
        viewModelScope.launch {
            mediaService.cancelDownload(id)
        }
    }

    /**
     * Select media files.
     *
     * @param uris The URIs of the selected media files.
     */
    fun selectMedia(uris: List<Uri>) {
        _uiState.update { it.copy(
            selectedMedia = uris
        ) }
    }

    /**
     * Clear selected media.
     */
    fun clearSelectedMedia() {
        _uiState.update { it.copy(
            selectedMedia = emptyList()
        ) }
    }

    /**
     * Clear error.
     */
    override fun clearError() {
        super.clearError()
        _uiState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
    }

    /**
     * Reset operation states.
     */
    fun resetOperationStates() {
        _uiState.update { it.copy(
            uploadOperation = it.uploadOperation.reset(),
            downloadOperation = it.downloadOperation.reset()
        ) }
    }

    /**
     * Get the upload progress for a specific attachment.
     *
     * @param id The ID of the attachment.
     * @return A flow of upload progress.
     */
    fun getUploadProgress(id: String): Flow<UploadProgress?> {
        return uploadProgressMap.map { it[id] }
    }

    /**
     * Get the download progress for a specific attachment.
     *
     * @param id The ID of the attachment.
     * @return A flow of download progress.
     */
    fun getDownloadProgress(id: String): Flow<DownloadProgress?> {
        return downloadProgressMap.map { it[id] }
    }

    /**
     * Get the cached file for a media attachment.
     *
     * @param attachment The media attachment.
     * @return The cached file.
     */
    fun getCachedFile(attachment: MediaAttachment): File {
        val cacheDir = File(fileUtils.getCacheDir(), "media")
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return File(cacheDir, "${attachment.id}_${attachment.name ?: "file"}")
    }
}
