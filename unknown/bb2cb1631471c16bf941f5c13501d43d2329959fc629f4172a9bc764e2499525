package com.tfkcolin.meena.data.api

import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.Message
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * API interface for chat endpoints.
 */
interface ChatApi {

    /**
     * Get a chat by ID.
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @return The chat.
     */
    @GET("/api/v1/conversations/{chat_id}")
    suspend fun getChatById(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String
    ): Response<ChatResponse>

    /**
     * Create a new chat.
     *
     * @param authToken The authentication token.
     * @param request The create chat request.
     * @return The created chat.
     */
    @POST("/api/v1/conversations")
    suspend fun createChat(
        @Header("Authorization") authToken: String,
        @Body request: CreateChatRequest
    ): Response<ChatResponse>

    /**
     * Get all chats for the current user.
     *
     * @param authToken The authentication token.
     * @param limit The maximum number of chats to return.
     * @param offset The offset for pagination.
     * @return A list of chats.
     */
    @GET("/api/v1/conversations")
    suspend fun getChats(
        @Header("Authorization") authToken: String,
        @Query("limit") limit: Int = 30,
        @Query("offset") offset: Int = 0
    ): Response<ChatListResponse>

    /**
     * Create a new group.
     *
     * @param authToken The authentication token.
     * @param request The create group request.
     * @return The created group.
     */
    @POST("/api/v1/groups")
    suspend fun createGroup(
        @Header("Authorization") authToken: String,
        @Body request: CreateGroupRequest
    ): Response<GroupResponse>

    /**
     * Get details of a specific group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @return The group details.
     */
    @GET("/api/v1/groups/{group_id}")
    suspend fun getGroup(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String
    ): Response<GroupResponse>

    /**
     * Update a group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @param request The update group request.
     * @return The updated group.
     */
    @PATCH("/api/v1/groups/{group_id}")
    suspend fun updateGroup(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String,
        @Body request: UpdateGroupRequest
    ): Response<GroupResponse>

    /**
     * Get the members of a group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @param limit The maximum number of members to return.
     * @param offset The offset for pagination.
     * @return A list of group members.
     */
    @GET("/api/v1/groups/{group_id}/members")
    suspend fun getGroupMembers(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<GroupMembersResponse>

    /**
     * Add members to a group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @param request The add members request.
     * @return A response indicating success or failure.
     */
    @POST("/api/v1/groups/{group_id}/members")
    suspend fun addGroupMembers(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String,
        @Body request: GroupMembersRequest
    ): Response<GroupResponse>

    /**
     * Remove a member from a group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @param userHandle The user handle to remove.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/groups/{group_id}/members/{user_handle}")
    suspend fun removeGroupMember(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String,
        @Path("user_handle") userHandle: String
    ): Response<Unit>

    /**
     * Change a member's role in a group.
     *
     * @param authToken The authentication token.
     * @param groupId The group ID.
     * @param userHandle The user handle to update.
     * @param request The update role request.
     * @return A response indicating success or failure.
     */
    @PATCH("/api/v1/groups/{group_id}/members/{user_handle}")
    suspend fun updateGroupMemberRole(
        @Header("Authorization") authToken: String,
        @Path("group_id") groupId: String,
        @Path("user_handle") userHandle: String,
        @Body request: UpdateRoleRequest
    ): Response<GroupMemberResponse>

    /**
     * Update chat settings (mute/pin/archive).
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @param request The chat settings request.
     * @return A response indicating success or failure.
     */
    @PUT("/api/v1/conversations/{chat_id}")
    suspend fun updateChatSettings(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String,
        @Body request: ChatSettingsRequest
    ): Response<Unit>

    /**
     * Delete a chat.
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/conversations/{chat_id}")
    suspend fun deleteChat(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String
    ): Response<Unit>

    /**
     * Get messages for a chat.
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @param limit The maximum number of messages to return.
     * @param offset The offset for pagination.
     * @return The messages.
     */
    @GET("/api/v1/conversations/{chat_id}/messages")
    suspend fun getMessages(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<MessageListResponse>

    /**
     * Send a message to a chat.
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @param request The send message request.
     * @return The sent message.
     */
    @POST("/api/v1/conversations/{chat_id}/messages")
    suspend fun sendMessage(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String,
        @Body request: SendMessageRequest
    ): Response<MessageResponse>

    /**
     * Update a message's status.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @param status The new status.
     * @return A response indicating success or failure.
     */
    @PATCH("/api/v1/messages/{message_id}/status")
    suspend fun updateMessageStatus(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Query("status") status: String
    ): Response<Unit>

    /**
     * Update multiple messages' statuses.
     *
     * @param authToken The authentication token.
     * @param request The update batch message status request.
     * @return A response indicating success or failure.
     */
    @PATCH("/api/v1/messages/status/batch")
    suspend fun updateBatchMessageStatus(
        @Header("Authorization") authToken: String,
        @Body request: UpdateBatchMessageStatusRequest
    ): Response<Unit>

    /**
     * Edit a message.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @param request The edit message request.
     * @return A response containing the edited message.
     */
    @PATCH("/api/v1/messages/{message_id}")
    suspend fun editMessage(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Body request: EditMessageRequest
    ): Response<MessageResponse>

    /**
     * Delete a message for self.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/messages/{message_id}")
    suspend fun deleteMessageForSelf(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Query("scope") scope: String = "self"
    ): Response<Unit>

    /**
     * Delete a message for everyone.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @return A response indicating success or failure.
     */
    @DELETE("/api/v1/messages/{message_id}")
    suspend fun deleteMessageForEveryone(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Query("scope") scope: String = "everyone"
    ): Response<Unit>

    /**
     * Forward a message to another conversation.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID to forward.
     * @param request The forward message request.
     * @return A response containing the forwarded message.
     */
    @POST("/api/v1/messages/{message_id}/forward")
    suspend fun forwardMessage(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Body request: ForwardMessageRequest
    ): Response<MessageResponse>

    /**
     * Add a reaction to a message.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @param request The reaction request.
     * @return A response indicating success or failure.
     */
    @POST("/api/v1/messages/{message_id}/reactions")
    suspend fun addReaction(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Body request: MessageReactionRequest
    ): Response<Unit>

    /**
     * Remove a reaction from a message.
     *
     * @param authToken The authentication token.
     * @param messageId The message ID.
     * @param emoji The emoji to remove.
     * @return A response indicating success or failure.
     */
    @HTTP(method = "DELETE", path = "/api/v1/messages/{message_id}/reactions", hasBody = true)
    suspend fun removeReaction(
        @Header("Authorization") authToken: String,
        @Path("message_id") messageId: String,
        @Body request: MessageReactionRequest
    ): Response<Unit>

    /**
     * Reply to a message.
     *
     * @param authToken The authentication token.
     * @param chatId The chat ID.
     * @param request The send message request with reply_to_message_id.
     * @return The sent message.
     */
    @POST("/api/v1/conversations/{chat_id}/messages")
    suspend fun replyToMessage(
        @Header("Authorization") authToken: String,
        @Path("chat_id") chatId: String,
        @Body request: SendMessageRequest
    ): Response<MessageResponse>
}

/**
 * Chat list response.
 */
data class ChatListResponse(
    val chats: List<Chat>,
    val total_count: Int
)

/**
 * Chat response.
 */
data class ChatResponse(
    val chat: Chat
)

/**
 * Create chat request.
 */
data class CreateChatRequest(
    val participant_ids: List<String>
)

/**
 * Message list response.
 */
data class MessageListResponse(
    val messages: List<Message>,
    val total_count: Int
)

/**
 * Message response.
 */
data class MessageResponse(
    val message: Message
)

/**
 * Send message request.
 */
data class SendMessageRequest(
    val content: String,
    val content_type: String = "text",
    val media_url: String? = null,
    val reply_to_message_id: String? = null
)

/**
 * Update batch message status request.
 */
data class UpdateBatchMessageStatusRequest(
    val message_ids: List<String>,
    val status: String
)

/**
 * Edit message request.
 */
data class EditMessageRequest(
    val content: String
)

/**
 * Chat settings request.
 */
data class ChatSettingsRequest(
    val is_muted: Boolean? = null,
    val is_archived: Boolean? = null,
    val is_pinned: Boolean? = null
)

/**
 * Forward message request.
 */
data class ForwardMessageRequest(
    val chat_id: String,
    val additional_content: String? = null
)

// Group-related models moved to GroupResponses.kt

// Removed duplicate ConversationSettingsRequest class
