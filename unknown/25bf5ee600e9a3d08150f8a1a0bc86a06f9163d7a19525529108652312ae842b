package com.tfkcolin.meena.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * User model representing a user in the system.
 * This model is used for both API responses and local database storage.
 *
 * For anonymous sign-in, the primary identifiers are userId, user<PERSON><PERSON><PERSON> (Meena ID), and displayName.
 * Other fields are kept for compatibility with the backend but may not be used in the UI.
 */
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    @SerializedName("user_id")
    val userId: String,

    @SerializedName("user_handle")
    val userHandle: String,

    @SerializedName("display_name")
    val displayName: String?,

    @SerializedName("profile_picture_url")
    val profilePictureUrl: String?,

    @SerializedName("bio")
    val bio: String?,

    @SerializedName("phone_number")
    val phoneNumber: String?,

    @SerializedName("email")
    val email: String?,

    @SerializedName("subscription_tier")
    val subscriptionTier: String = "free",

    @SerializedName("verification_status")
    val verificationStatus: String = "none",

    @SerializedName("is_active")
    val isActive: Boolean = true,

    @SerializedName("created_at")
    val createdAt: String?,

    @SerializedName("last_seen_at")
    val lastSeenAt: String?
)
