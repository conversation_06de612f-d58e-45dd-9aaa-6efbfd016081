package com.tfkcolin.meena.ui.stories.viewmodel

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.UiState
import com.tfkcolin.meena.ui.base.UiStateProperties
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.ui.stories.models.Story
import com.tfkcolin.meena.ui.stories.models.StoryItem
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/**
 * ViewModel for story operations.
 */
@HiltViewModel
class StoryViewModel @Inject constructor(
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state
    private val _storyState = MutableStateFlow(StoryState())
    val storyState: StateFlow<StoryState> = _storyState.asStateFlow()

    init {
        // Load stories
        loadStories()
    }

    /**
     * Load stories from the API.
     */
    fun loadStories() {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            // TODO: Replace with actual API call
            // For now, use mock data
            val mockStories = createMockStories()
            val mockUserStories = createMockUserStories()

            _storyState.update { it.copy(
                stories = mockStories,
                userStories = mockUserStories,
                properties = it.properties.copy(isLoading = false)
            ) }
        }
    }

    /**
     * Create a new story.
     *
     * @param mediaUri The URI of the media.
     * @param caption The caption for the story.
     */
    fun createStory(mediaUri: Uri, caption: String = "") {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                createStoryOperation = it.createStoryOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            // TODO: Replace with actual API call
            // For now, simulate a delay
            kotlinx.coroutines.delay(1000)

            // Add the new story to the user's stories
            val newStory = StoryItem(
                id = UUID.randomUUID().toString(),
                userName = "You",
                userAvatarUrl = "https://i.pravatar.cc/150?img=1",
                storyImageUrl = mediaUri.toString(),
                isViewed = false,
                hasMultipleStories = false
            )

            _storyState.update { it.copy(
                userStories = listOf(newStory) + it.userStories,
                createStoryOperation = it.createStoryOperation.success(),
                properties = it.properties.copy(isLoading = false)
            ) }
        }
    }

    /**
     * View a story.
     *
     * @param storyId The ID of the story.
     */
    fun viewStory(storyId: String) {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                viewStoryOperation = it.viewStoryOperation.start()
            ) }

            // TODO: Replace with actual API call
            // For now, simulate a delay
            kotlinx.coroutines.delay(500)

            // Mark the story as viewed
            val updatedStories = _storyState.value.stories.map { story ->
                if (story.id == storyId) {
                    story.copy(isViewed = true)
                } else {
                    story
                }
            }

            _storyState.update { it.copy(
                stories = updatedStories,
                viewStoryOperation = it.viewStoryOperation.success()
            ) }
        }
    }

    /**
     * React to a story.
     *
     * @param storyId The ID of the story.
     * @param reaction The reaction emoji.
     */
    fun reactToStory(storyId: String, reaction: String) {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                reactToStoryOperation = it.reactToStoryOperation.start()
            ) }

            // TODO: Replace with actual API call
            // For now, simulate a delay
            kotlinx.coroutines.delay(500)

            _storyState.update { it.copy(
                reactToStoryOperation = it.reactToStoryOperation.success()
            ) }
        }
    }

    /**
     * Reply to a story.
     *
     * @param storyId The ID of the story.
     * @param reply The reply message.
     */
    fun replyToStory(storyId: String, reply: String) {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                replyToStoryOperation = it.replyToStoryOperation.start()
            ) }

            // TODO: Replace with actual API call
            // For now, simulate a delay
            kotlinx.coroutines.delay(500)

            _storyState.update { it.copy(
                replyToStoryOperation = it.replyToStoryOperation.success()
            ) }
        }
    }

    /**
     * Delete a story.
     *
     * @param storyId The ID of the story.
     */
    fun deleteStory(storyId: String) {
        launchWithErrorHandling {
            _storyState.update { it.copy(
                deleteStoryOperation = it.deleteStoryOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            // TODO: Replace with actual API call
            // For now, simulate a delay
            kotlinx.coroutines.delay(1000)

            // Remove the story from the user's stories
            val updatedUserStories = _storyState.value.userStories.filter { it.id != storyId }

            _storyState.update { it.copy(
                userStories = updatedUserStories,
                deleteStoryOperation = it.deleteStoryOperation.success(),
                properties = it.properties.copy(isLoading = false)
            ) }
        }
    }

    /**
     * Create mock stories for testing.
     */
    private fun createMockStories(): List<StoryItem> {
        return listOf(
            StoryItem(
                id = "1",
                userName = "John Doe",
                userAvatarUrl = "https://i.pravatar.cc/150?img=1",
                storyImageUrl = "https://picsum.photos/seed/1/300/500",
                isViewed = false,
                hasMultipleStories = true
            ),
            StoryItem(
                id = "2",
                userName = "Jane Smith",
                userAvatarUrl = "https://i.pravatar.cc/150?img=2",
                storyImageUrl = "https://picsum.photos/seed/2/300/500",
                isViewed = true,
                hasMultipleStories = false
            ),
            StoryItem(
                id = "3",
                userName = "Bob Johnson",
                userAvatarUrl = "https://i.pravatar.cc/150?img=3",
                storyImageUrl = "https://picsum.photos/seed/3/300/500",
                isViewed = false,
                hasMultipleStories = true,
                isLive = true
            ),
            StoryItem(
                id = "4",
                userName = "Alice Brown",
                userAvatarUrl = "https://i.pravatar.cc/150?img=4",
                storyImageUrl = "https://picsum.photos/seed/4/300/500",
                isViewed = false,
                hasMultipleStories = false
            ),
            StoryItem(
                id = "5",
                userName = "Charlie Wilson",
                userAvatarUrl = "https://i.pravatar.cc/150?img=5",
                storyImageUrl = "https://picsum.photos/seed/5/300/500",
                isViewed = true,
                hasMultipleStories = true
            )
        )
    }

    /**
     * Create mock user stories for testing.
     */
    private fun createMockUserStories(): List<StoryItem> {
        return listOf(
            StoryItem(
                id = "user1",
                userName = "Your Story",
                userAvatarUrl = "https://i.pravatar.cc/150?img=10",
                storyImageUrl = "https://picsum.photos/seed/10/300/500",
                isViewed = false,
                hasMultipleStories = true
            )
        )
    }
}

/**
 * UI state for story operations.
 */
data class StoryState(
    val stories: List<StoryItem> = emptyList(),
    val userStories: List<StoryItem> = emptyList(),
    val createStoryOperation: OperationState = OperationState(),
    val viewStoryOperation: OperationState = OperationState(),
    val reactToStoryOperation: OperationState = OperationState(),
    val replyToStoryOperation: OperationState = OperationState(),
    val deleteStoryOperation: OperationState = OperationState(),
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
