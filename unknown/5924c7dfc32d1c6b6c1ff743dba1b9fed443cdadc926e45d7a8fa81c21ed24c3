package com.tfkcolin.meena.ui.chat.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.components.foundation.UserAvatar

/**
 * A composable that displays a message item in a chat.
 *
 * @param message The message to display.
 * @param isFromCurrentUser Whether the message is from the current user.
 * @param onLongClick The callback for when the message is long-pressed.
 * @param modifier The modifier for the component.
 */
@Composable
fun MessageItem(
    message: Message,
    isFromCurrentUser: Boolean,
    onLongClick: (Message) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        horizontalAlignment = if (isFromCurrentUser) Alignment.End else Alignment.Start
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Bottom
        ) {
            // For incoming messages in groups, show the avatar
            if (!isFromCurrentUser) {
                UserAvatar(
                    imageUrl = null, // Replace with sender's avatar URL if available
                    userName = message.senderId,
                    size = 32.dp,
                    isOnline = false
                )
                Spacer(modifier = Modifier.width(8.dp))
            } else {
                // Spacer for alignment when no avatar
                Spacer(modifier = Modifier.size(40.dp))
            }

            // Message content
            Column(
                horizontalAlignment = if (isFromCurrentUser) Alignment.End else Alignment.Start,
                modifier = Modifier.weight(1f)
            ) {
                // Message bubble
                MessageBubble(
                    message = message,
                    isFromCurrentUser = isFromCurrentUser,
                    onLongClick = onLongClick
                )
            }

            // Spacer for outgoing messages
            if (isFromCurrentUser) {
                Spacer(modifier = Modifier.size(40.dp))
            }
        }
    }
}

@Composable
private fun MessageBubble(
    message: Message,
    isFromCurrentUser: Boolean,
    onLongClick: (Message) -> Unit
) {
    val bubbleColor = if (isFromCurrentUser) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        MaterialTheme.colorScheme.secondaryContainer
    }
    val textColor = if (isFromCurrentUser) {
        MaterialTheme.colorScheme.onPrimaryContainer
    } else {
        MaterialTheme.colorScheme.onSecondaryContainer
    }

    // Determine bubble shape based on message direction
    val bubbleShape = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = if (isFromCurrentUser) 16.dp else 4.dp,
        bottomEnd = if (isFromCurrentUser) 4.dp else 16.dp
    )

    Box(
        modifier = Modifier
            .clip(bubbleShape)
            .background(bubbleColor)
            .clickable { onLongClick(message) }
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            // Reply message if any
            if (message.replyToMessageId != null) {
                ReplyPreview(
                    replyText = "Reply to message", // Replace with actual reply content if available
                    replyUserName = "User", // Replace with actual user name if available
                    isOutgoing = isFromCurrentUser
                )
            }

            // Media content if any
            if (message.hasAttachments && message.attachments != null) {
                // Display a placeholder for attachments
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            if (isFromCurrentUser)
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                            else
                                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.7f)
                        )
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "${message.attachments?.size ?: 0} attachment(s)",
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isFromCurrentUser)
                            MaterialTheme.colorScheme.onPrimaryContainer
                        else
                            MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
            }

            // Message text
            if (message.content.isNotEmpty()) {
                Text(
                    text = message.content,
                    color = textColor,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Time and status
            Row(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(top = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Format timestamp to time
                val time = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault())
                    .format(java.util.Date(message.timestamp))

                Text(
                    text = time,
                    style = MaterialTheme.typography.labelSmall,
                    color = textColor.copy(alpha = 0.7f)
                )

                // Show status for outgoing messages
                if (isFromCurrentUser) {
                    Spacer(modifier = Modifier.width(4.dp))
                    MessageStatusIndicator(status = message.status)
                }
            }
        }
    }
}

@Composable
private fun ReplyPreview(
    replyText: String,
    replyUserName: String,
    isOutgoing: Boolean
) {
    val accentColor = if (isOutgoing) {
        MaterialTheme.colorScheme.primary
    } else {
        MaterialTheme.colorScheme.secondary
    }

    Box(
        modifier = Modifier
            .padding(bottom = 8.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.3f))
            .padding(8.dp)
    ) {
        Column {
            Text(
                text = replyUserName,
                style = MaterialTheme.typography.labelMedium,
                color = accentColor
            )

            Text(
                text = replyText,
                style = MaterialTheme.typography.bodySmall,
                maxLines = 1,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun MessageStatusIndicator(status: String) {
    val statusText = when (status) {
        "sending" -> "⌛"
        "sent" -> "✓"
        "delivered" -> "✓✓"
        "read" -> "✓✓"
        "failed" -> "❌"
        else -> "⚠️"
    }

    val statusColor = when (status) {
        "read" -> MaterialTheme.colorScheme.primary
        "failed" -> MaterialTheme.colorScheme.error
        else -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
    }

    Text(
        text = statusText,
        style = MaterialTheme.typography.labelSmall,
        color = statusColor,
        textAlign = TextAlign.End
    )
}
