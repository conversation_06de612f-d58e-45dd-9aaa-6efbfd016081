package com.tfkcolin.meena.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import android.app.Service
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import com.tfkcolin.meena.MainActivity
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.models.ChunkedUploadInitRequest
import com.tfkcolin.meena.data.preferences.UserPreferences
import com.tfkcolin.meena.utils.FileUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import javax.inject.Inject

/**
 * Foreground service for uploading media files.
 * This service continues to run even when the app is in the background.
 */
@AndroidEntryPoint
class MediaUploadForegroundService : Service() {

    // Create a coroutine scope for the service
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "media_upload_channel"
        private const val CHANNEL_NAME = "Media Upload"

        const val ACTION_UPLOAD = "com.tfkcolin.meena.action.UPLOAD_MEDIA"
        const val ACTION_CANCEL = "com.tfkcolin.meena.action.CANCEL_UPLOAD"

        const val EXTRA_FILE_URI = "file_uri"
        const val EXTRA_FILE_PATH = "file_path"
        const val EXTRA_MIME_TYPE = "mime_type"
        const val EXTRA_IS_ENCRYPTED = "is_encrypted"
        const val EXTRA_MESSAGE_ID = "message_id"
        const val EXTRA_UPLOAD_ID = "upload_id"
    }

    @Inject
    lateinit var mediaApi: MediaApi

    @Inject
    lateinit var userPreferences: UserPreferences

    @Inject
    lateinit var fileUtils: FileUtils

    private val _uploadProgress = MutableStateFlow<UploadProgress>(UploadProgress.Idle)
    val uploadProgress = _uploadProgress.asStateFlow()

    private var currentUploadId: String? = null

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_UPLOAD -> {
                val filePath = intent.getStringExtra(EXTRA_FILE_PATH)
                val mimeType = intent.getStringExtra(EXTRA_MIME_TYPE)
                val isEncrypted = intent.getBooleanExtra(EXTRA_IS_ENCRYPTED, false)
                val messageId = intent.getStringExtra(EXTRA_MESSAGE_ID)

                if (filePath != null && mimeType != null && messageId != null) {
                    startForeground(NOTIFICATION_ID, createNotification(0))
                    uploadFile(filePath, mimeType, isEncrypted, messageId)
                }
            }
            ACTION_CANCEL -> {
                currentUploadId?.let { uploadId ->
                    serviceScope.launch {
                        try {
                            // Cancel the upload on the server
                            // This would be implemented in a real app
                            _uploadProgress.value = UploadProgress.Cancelled
                        } catch (e: Exception) {
                            _uploadProgress.value = UploadProgress.Error(e.message ?: "Unknown error")
                        } finally {
                            stopSelf()
                        }
                    }
                } ?: stopSelf()
            }
        }

        return START_NOT_STICKY
    }

    private fun uploadFile(filePath: String, mimeType: String, isEncrypted: Boolean, messageId: String) {
        serviceScope.launch {
            try {
                _uploadProgress.value = UploadProgress.Preparing

                val file = File(filePath)
                if (!file.exists()) {
                    _uploadProgress.value = UploadProgress.Error("File not found")
                    stopSelf()
                    return@launch
                }

                // Determine if we should use chunked upload based on file size
                if (file.length() > 5 * 1024 * 1024) { // 5MB threshold
                    uploadLargeFile(file, mimeType, isEncrypted, messageId)
                } else {
                    uploadSmallFile(file, mimeType, isEncrypted, messageId)
                }
            } catch (e: Exception) {
                _uploadProgress.value = UploadProgress.Error(e.message ?: "Unknown error")
                updateNotification(0, error = true)
                stopSelf()
            }
        }
    }

    private suspend fun uploadSmallFile(file: File, mimeType: String, isEncrypted: Boolean, messageId: String) {
        try {
            _uploadProgress.value = UploadProgress.Uploading(0)
            updateNotification(0)

            val authToken = "Bearer ${userPreferences.getAuthToken()}"

            // If the file is encrypted, get a pre-signed URL
            if (isEncrypted) {
                val response = mediaApi.getEncryptedUploadUrl(
                    authToken,
                    file.name,
                    "application/octet-stream",
                    file.length()
                )

                if (response.isSuccessful) {
                    val uploadUrlResponse = response.body()
                    uploadUrlResponse?.let {
                        // Upload to the pre-signed URL with progress tracking
                        val result = uploadWithProgress(file, it.uploadUrl, "application/octet-stream")
                        if (result) {
                            _uploadProgress.value = UploadProgress.Success(it.mediaId)
                            updateNotification(100)
                        } else {
                            _uploadProgress.value = UploadProgress.Error("Upload failed")
                            updateNotification(0, error = true)
                        }
                    }
                } else {
                    _uploadProgress.value = UploadProgress.Error("Failed to get upload URL")
                    updateNotification(0, error = true)
                }
            } else {
                // Regular upload with progress tracking
                val progressRequestBody = ProgressRequestBody(
                    file,
                    mimeType.toMediaType()
                ) { progress ->
                    _uploadProgress.value = UploadProgress.Uploading(progress)
                    updateNotification(progress)
                }

                val response = mediaApi.uploadMedia(authToken, progressRequestBody)
                if (response.isSuccessful) {
                    val media = response.body()
                    media?.let {
                        _uploadProgress.value = UploadProgress.Success(it.id)
                        updateNotification(100)
                    }
                } else {
                    _uploadProgress.value = UploadProgress.Error("Upload failed: ${response.errorBody()?.string()}")
                    updateNotification(0, error = true)
                }
            }
        } catch (e: Exception) {
            _uploadProgress.value = UploadProgress.Error(e.message ?: "Unknown error")
            updateNotification(0, error = true)
        } finally {
            // Wait a moment before stopping the service so the user can see the completion notification
            withContext(Dispatchers.IO) {
                Thread.sleep(2000)
            }
            stopSelf()
        }
    }

    private suspend fun uploadLargeFile(file: File, mimeType: String, isEncrypted: Boolean, messageId: String) {
        try {
            _uploadProgress.value = UploadProgress.Preparing
            updateNotification(0)

            val authToken = "Bearer ${userPreferences.getAuthToken()}"
            val chunkSize = 5 * 1024 * 1024 // 5MB chunks

            // Initialize chunked upload
            val initRequest = ChunkedUploadInitRequest(
                fileName = file.name,
                contentType = if (isEncrypted) "application/octet-stream" else mimeType,
                totalSize = file.length(),
                chunkSize = chunkSize,
                isEncrypted = isEncrypted
            )

            val initResponse = mediaApi.initiateChunkedUpload(authToken, initRequest)
            if (!initResponse.isSuccessful) {
                _uploadProgress.value = UploadProgress.Error("Failed to initialize chunked upload")
                updateNotification(0, error = true)
                stopSelf()
                return
            }

            val uploadSession = initResponse.body()
            if (uploadSession == null) {
                _uploadProgress.value = UploadProgress.Error("Invalid upload session")
                updateNotification(0, error = true)
                stopSelf()
                return
            }

            currentUploadId = uploadSession.uploadId

            // Calculate number of chunks
            val totalChunks = (file.length() + chunkSize - 1) / chunkSize
            var uploadedChunks = 0L
            var totalUploaded = 0L

            // Upload each chunk
            withContext(Dispatchers.IO) {
                file.inputStream().use { inputStream ->
                    val buffer = ByteArray(chunkSize)
                    var chunkIndex = 0
                    var bytesRead: Int

                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        val chunkFile = File(cacheDir, "chunk_${uploadSession.uploadId}_$chunkIndex")
                        chunkFile.outputStream().use { outputStream ->
                            outputStream.write(buffer, 0, bytesRead)
                        }

                        // Upload the chunk
                        val chunkResponse = mediaApi.uploadChunk(
                            authToken,
                            uploadSession.uploadId,
                            chunkIndex,
                            chunkFile.asRequestBody("application/octet-stream".toMediaType())
                        )

                        // Delete the temporary chunk file
                        chunkFile.delete()

                        if (!chunkResponse.isSuccessful) {
                            _uploadProgress.value = UploadProgress.Error("Failed to upload chunk $chunkIndex")
                            updateNotification(0, error = true)
                            stopSelf()
                            return@withContext
                        }

                        uploadedChunks++
                        totalUploaded += bytesRead
                        val progress = (totalUploaded * 100 / file.length()).toInt()
                        _uploadProgress.value = UploadProgress.Uploading(progress)
                        updateNotification(progress)

                        chunkIndex++
                    }
                }
            }

            // Complete the upload
            val completeResponse = mediaApi.completeChunkedUpload(authToken, uploadSession.uploadId)
            if (completeResponse.isSuccessful) {
                val media = completeResponse.body()
                media?.let {
                    _uploadProgress.value = UploadProgress.Success(it.id)
                    updateNotification(100)
                }
            } else {
                _uploadProgress.value = UploadProgress.Error("Failed to complete upload")
                updateNotification(0, error = true)
            }
        } catch (e: Exception) {
            _uploadProgress.value = UploadProgress.Error(e.message ?: "Unknown error")
            updateNotification(0, error = true)
        } finally {
            // Wait a moment before stopping the service
            withContext(Dispatchers.IO) {
                Thread.sleep(2000)
            }
            stopSelf()
        }
    }

    private suspend fun uploadWithProgress(file: File, uploadUrl: String, mimeType: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Implementation would use OkHttp to upload to the pre-signed URL with progress tracking
                // This is a placeholder that simulates a successful upload
                for (i in 0..100 step 5) {
                    _uploadProgress.value = UploadProgress.Uploading(i)
                    updateNotification(i)
                    Thread.sleep(100)
                }
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Used for media upload notifications"
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(progress: Int, error: Boolean = false): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        val cancelIntent = PendingIntent.getService(
            this,
            0,
            Intent(this, MediaUploadForegroundService::class.java).apply {
                action = ACTION_CANCEL
            },
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(if (error) "Upload Failed" else "Uploading Media")
            .setContentText(if (error) "Tap to retry" else "Progress: $progress%")
            .setSmallIcon(R.drawable.baseline_upload_24)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(!error)

        if (!error) {
            builder.addAction(R.drawable.baseline_cancel_24, "Cancel", cancelIntent)
            builder.setProgress(100, progress, progress == 0)
        }

        return builder.build()
    }

    private fun updateNotification(progress: Int, error: Boolean = false) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification(progress, error))
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cancel all coroutines when the service is destroyed
        serviceScope.cancel()
    }

    /**
     * Custom RequestBody that tracks upload progress.
     */
    private inner class ProgressRequestBody(
        private val file: File,
        private val contentType: okhttp3.MediaType,
        private val progressCallback: (Int) -> Unit
    ) : okhttp3.RequestBody() {

        override fun contentType() = contentType

        override fun contentLength() = file.length()

        override fun writeTo(sink: okio.BufferedSink) {
            val fileLength = file.length()
            val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
            file.inputStream().use { inputStream ->
                var uploaded = 0L
                var read: Int
                while (inputStream.read(buffer).also { read = it } != -1) {
                    sink.write(buffer, 0, read)
                    uploaded += read
                    val progress = ((uploaded * 100) / fileLength).toInt()
                    progressCallback(progress)
                }
            }
        }
    }
}

/**
 * Sealed class representing the different states of an upload.
 */
sealed class UploadProgress {
    object Idle : UploadProgress()
    object Preparing : UploadProgress()
    data class Uploading(val progress: Int) : UploadProgress()
    data class Success(val mediaId: String) : UploadProgress()
    data class Error(val message: String) : UploadProgress()
    object Cancelled : UploadProgress()
}
