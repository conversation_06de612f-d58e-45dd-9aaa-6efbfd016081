package com.tfkcolin.meena.ui.components

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AudioFile
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.VideoFile
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Attachment selector component.
 *
 * @param onAttachmentSelected The callback for when an attachment is selected.
 * @param onDismiss The callback for when the selector is dismissed.
 * @param modifier The modifier for the component.
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun AttachmentSelector(
    onAttachmentSelected: (MediaAttachment) -> Unit,
    onDismiss: () -> Unit,
    mediaAttachmentHelper: MediaAttachmentHelper, // Added helper instance
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var cameraUri by remember { mutableStateOf<Uri?>(null) }
    var showLocationSelector by remember { mutableStateOf(false) }

    // Permission states
    val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)
    val storagePermissionState = rememberPermissionState(Manifest.permission.READ_EXTERNAL_STORAGE)

    // Activity result launchers
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            mediaAttachmentHelper.createAttachmentFromUri(it)?.let { attachment ->
                onAttachmentSelected(attachment)
                onDismiss()
            }
        }
    }

    val videoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            mediaAttachmentHelper.createAttachmentFromUri(it)?.let { attachment ->
                onAttachmentSelected(attachment)
                onDismiss()
            }
        }
    }

    val audioPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            mediaAttachmentHelper.createAttachmentFromUri(it)?.let { attachment ->
                onAttachmentSelected(attachment)
                onDismiss()
            }
        }
    }

    val documentPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            mediaAttachmentHelper.createAttachmentFromUri(it)?.let { attachment ->
                onAttachmentSelected(attachment)
                onDismiss()
            }
        }
    }

    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && cameraUri != null) {
            mediaAttachmentHelper.createAttachmentFromUri(cameraUri!!)?.let { attachment ->
                onAttachmentSelected(attachment)
                onDismiss()
            }
        }
    }

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        color = MaterialTheme.colorScheme.surface,
        tonalElevation = 4.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Add Attachment",
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold
                    )
                )

                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close"
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Camera
                AttachmentOption(
                    icon = Icons.Default.Image,
                    label = "Camera",
                    onClick = {
                        if (cameraPermissionState.status.isGranted) {
                            // Create a file to save the image
                            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                            val imageFile = File(context.cacheDir, "IMG_${timeStamp}.jpg")

                            cameraUri = FileProvider.getUriForFile(
                                context,
                                "${context.packageName}.provider",
                                imageFile
                            )

                            cameraLauncher.launch(cameraUri!!)
                        } else {
                            cameraPermissionState.launchPermissionRequest()
                        }
                    }
                )

                // Gallery
                AttachmentOption(
                    icon = Icons.Default.Image,
                    label = "Gallery",
                    onClick = {
                        if (storagePermissionState.status.isGranted) {
                            imagePickerLauncher.launch("image/*")
                        } else {
                            storagePermissionState.launchPermissionRequest()
                        }
                    }
                )

                // Video
                AttachmentOption(
                    icon = Icons.Default.VideoFile,
                    label = "Video",
                    onClick = {
                        if (storagePermissionState.status.isGranted) {
                            videoPickerLauncher.launch("video/*")
                        } else {
                            storagePermissionState.launchPermissionRequest()
                        }
                    }
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Audio
                AttachmentOption(
                    icon = Icons.Default.AudioFile,
                    label = "Audio",
                    onClick = {
                        if (storagePermissionState.status.isGranted) {
                            audioPickerLauncher.launch("audio/*")
                        } else {
                            storagePermissionState.launchPermissionRequest()
                        }
                    }
                )

                // Document
                AttachmentOption(
                    icon = Icons.Default.Description,
                    label = "Document",
                    onClick = {
                        if (storagePermissionState.status.isGranted) {
                            documentPickerLauncher.launch("*/*")
                        } else {
                            storagePermissionState.launchPermissionRequest()
                        }
                    }
                )

                // Location
                AttachmentOption(
                    icon = Icons.Default.LocationOn,
                    label = "Location",
                    onClick = {
                        showLocationSelector = true
                    }
                )
            }
        }
    }

    // Show location selector if needed
    if (showLocationSelector) {
        LocationSelector(
            onLocationSelected = onAttachmentSelected,
            onDismiss = { showLocationSelector = false }
        )
    }
}

/**
 * Attachment option component.
 *
 * @param icon The icon for the option.
 * @param label The label for the option.
 * @param onClick The click handler.
 * @param modifier The modifier for the component.
 */
@Composable
fun AttachmentOption(
    icon: ImageVector,
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable(onClick = onClick)
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(56.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
