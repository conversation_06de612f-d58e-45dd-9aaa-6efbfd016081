# Meena Messaging App

Meena is a secure, feature-rich messaging application built with modern Android development practices.

## Documentation

For comprehensive documentation, please refer to the [docs](./docs/README.md) directory.

### Key Documentation

- [Architecture Overview](./docs/architecture/README.md)
- [Component Responsibilities](./docs/component-responsibilities.md)
- [Reusable UI Components](./docs/reusable-components.md)
- [API Documentation](./docs/api/README.md)
- [Database Documentation](./docs/database/README.md)

## Development

### Prerequisites

- Android Studio Arctic Fox or later
- JDK 11 or later
- Gradle 7.0 or later

### Getting Started

1. Clone the repository
2. Open the project in Android Studio
3. Sync the project with Gradle files
4. Run the app on an emulator or physical device

## Features

- One-to-one messaging
- Group chats
- Media sharing (images, videos, audio, documents)
- End-to-end encryption
- Message reactions
- Message forwarding
- Message editing and deletion
- User profiles
- Contact management
- Push notifications
- Offline messaging

## Contributing

Please read our [Contributing Guide](./docs/guides/contributing.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
