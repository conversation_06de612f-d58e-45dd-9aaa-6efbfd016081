package com.tfkcolin.meena.data.models

/**
 * Extension function to convert a ContactResponse to a Contact model.
 */
fun ContactResponse.toContact(): Contact {
    return Contact(
        id = this.id,
        userId = this.userId,
        contactId = this.contactId,
        displayName = this.displayName,
        notes = this.notes,
        relationship = this.relationship,
        isFavorite = false, // Default value since ContactResponse doesn't have this property
        lastInteractionAt = null, // Default value since ContactResponse doesn't have this property
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        user = this.user
    )
}

/**
 * Extension function to check if a contact is blocked.
 */
fun Contact.isBlocked(): Boolean {
    return relationship == "blocked"
}

/**
 * Extension function to check if a contact is a favorite.
 */
fun Contact.isFavorite(): Boolean {
    return isFavorite
}

/**
 * Extension function to get the first letter of a contact's display name.
 */
fun Contact.getInitial(): String {
    val displayName = this.displayName ?: "User"
    return displayName.firstOrNull()?.uppercase() ?: "U"
}
