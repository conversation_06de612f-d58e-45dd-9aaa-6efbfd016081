package com.tfkcolin.meena.data.models

import com.google.gson.annotations.SerializedName

/**
 * User profile model representing a user's public profile information.
 *
 * For anonymous sign-in, the primary identifiers are id, handle (Meena ID), and displayName.
 * Other fields are kept for compatibility with the backend but may not be used in the UI.
 */
data class UserProfile(
    @SerializedName("id")
    val id: String,

    @SerializedName("handle")
    val handle: String,

    @SerializedName("display_name")
    val displayName: String?,

    @SerializedName("avatar_url")
    val avatarUrl: String?,

    @SerializedName("status")
    val status: String?, // "online", "offline", "away"

    @SerializedName("last_seen")
    val lastSeen: String?,

    @SerializedName("bio")
    val bio: String?
) {
    /**
     * Check if the user is online.
     *
     * @return True if the user is online, false otherwise.
     */
    fun isOnline(): Boolean {
        return status == "online"
    }

    /**
     * Get the display name or handle if not set.
     *
     * @return The display name to show.
     */
    fun getDisplayNameOrHandle(): String {
        return displayName ?: handle
    }
}
