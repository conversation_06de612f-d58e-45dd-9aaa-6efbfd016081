package com.tfkcolin.meena.data.local.typeconverters

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Type converter for Map<String, List<String>> objects.
 */
class MapStringListConverter {
    private val gson = Gson()

    /**
     * Convert a Map<String, List<String>> to a JSON string.
     *
     * @param map The map to convert.
     * @return The JSON string representation of the map.
     */
    @TypeConverter
    fun fromMap(map: Map<String, List<String>>?): String? {
        return map?.let { gson.toJson(it) }
    }

    /**
     * Convert a JSON string to a Map<String, List<String>>.
     *
     * @param json The JSON string to convert.
     * @return The map.
     */
    @TypeConverter
    fun toMap(json: String?): Map<String, List<String>>? {
        if (json == null) return null
        val type = object : TypeToken<Map<String, List<String>>>() {}.type
        return gson.fromJson(json, type)
    }
}
