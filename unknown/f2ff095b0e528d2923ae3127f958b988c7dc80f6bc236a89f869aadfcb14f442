package models

import "time"

// Contact represents a contact in a user's contact list
type Contact struct {
	ID                string       `json:"id"`
	UserID            string       `json:"user_id"`
	ContactID         string       `json:"contact_id"`
	DisplayName       string       `json:"display_name"`
	Relationship      string       `json:"relationship"` // friend, blocked, pending
	Notes             string       `json:"notes"`
	CreatedAt         time.Time    `json:"created_at"`
	UpdatedAt         time.Time    `json:"updated_at"`
	IsFavorite        bool         `json:"is_favorite"`
	LastInteractionAt *time.Time   `json:"last_interaction_at,omitempty"`
	User              *UserProfile `json:"user,omitempty"` // The contact's user profile
}

// ContactListItem represents a contact in a list
type ContactListItem struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	ContactID    string    `json:"contact_id"`
	DisplayName  string    `json:"display_name"`
	UserHandle   string    `json:"user_handle"`
	AvatarURL    string    `json:"avatar_url"`
	Relationship string    `json:"relationship"`
	LastActive   time.Time `json:"last_active"`
	IsOnline     bool      `json:"is_online"`
}

// AddContactRequest represents a request to add a contact
type AddContactRequest struct {
	UserHandle  string `json:"user_handle" binding:"required"`
	DisplayName string `json:"display_name"`
	Notes       string `json:"notes"`
}

// UpdateContactRequest represents a request to update a contact
type UpdateContactRequest struct {
	DisplayName  string `json:"display_name"`
	Notes        string `json:"notes"`
	Relationship string `json:"relationship"`
	IsFavorite   bool   `json:"is_favorite"`
}

// BlockContactRequest represents a request to block a contact
type BlockContactRequest struct {
	ContactID string `json:"contact_id" binding:"required"`
}

// UnblockContactRequest represents a request to unblock a contact
type UnblockContactRequest struct {
	ContactID string `json:"contact_id" binding:"required"`
}

// RemoveContactRequest represents a request to remove a contact
type RemoveContactRequest struct {
	ContactID string `json:"contact_id" binding:"required"`
}
