#!/bin/bash

# <PERSON><PERSON>t to initialize the database on Railway
# This script should be run after deploying the backend to Railway

echo "Initializing database on Railway..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "Railway CLI is not installed. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if logged in to Railway
railway whoami &> /dev/null
if [ $? -ne 0 ]; then
    echo "Not logged in to Railway. Please login first:"
    echo "railway login"
    exit 1
fi

# Get the DATABASE_URL from Railway
echo "Fetching DATABASE_URL from Railway..."
DATABASE_URL=$(railway variables get DATABASE_URL)
if [ -z "$DATABASE_URL" ]; then
    echo "Failed to get DATABASE_URL from Railway. Make sure you're in the correct project."
    exit 1
fi

# Path to the schema file
SCHEMA_FILE="../scripts/initialize_database_fixed.sql"
if [ ! -f "$SCHEMA_FILE" ]; then
    echo "Schema file not found: $SCHEMA_FILE"
    echo "Please run this script from the backend directory."
    exit 1
fi

# Initialize the database
echo "Applying database schema..."
psql "$DATABASE_URL" -f "$SCHEMA_FILE"

if [ $? -eq 0 ]; then
    echo "Database initialization completed successfully!"
else
    echo "Database initialization failed."
    exit 1
fi

# Verify the database structure
echo "Verifying database structure..."
psql "$DATABASE_URL" -c "
    SELECT table_name, 'Created successfully' AS status
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name;
"

echo "Database initialization complete!"
exit 0
