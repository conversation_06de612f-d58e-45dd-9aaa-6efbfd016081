package com.tfkcolin.meena.ui.components

import android.content.Intent
import android.net.Uri
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.VolumeOff
import androidx.compose.material.icons.automirrored.filled.VolumeUp
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.VolumeOff
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.media3.common.MediaItem // Ensure androidx.media3 import
import androidx.media3.common.Player // Ensure androidx.media3 import
import androidx.media3.exoplayer.ExoPlayer // Ensure androidx.media3 import
import androidx.media3.ui.PlayerView
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.ui.theme.MeenaTheme
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import com.tfkcolin.meena.utils.FileUtils // Import FileUtils
import kotlinx.coroutines.delay
import java.io.File

/**
 * Enhanced attachment viewer component for viewing media attachments in full screen.
 * Supports video and audio playback.
 *
 * @param attachment The media attachment to view.
 * @param onDismiss The callback for when the viewer is dismissed.
 * @param viewModel The media view model.
 */
@Composable
fun EnhancedAttachmentViewer(
    attachment: MediaAttachment,
    onDismiss: () -> Unit,
    mediaAttachmentHelper: MediaAttachmentHelper, // Added helper instance
    viewModel: MediaViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val fileUtils = remember { FileUtils(context) } // Instantiate FileUtils

    // State for zoom and pan
    var scale by remember { mutableFloatStateOf(1f) }
    var offsetX by remember { mutableFloatStateOf(0f) }
    var offsetY by remember { mutableFloatStateOf(0f) }

    // State for media playback
    var isPlaying by remember { mutableStateOf(false) }
    var isMuted by remember { mutableStateOf(false) }
    var currentPosition by remember { mutableLongStateOf(0L) }
    var duration by remember { mutableLongStateOf(0L) }
    var bufferedPosition by remember { mutableLongStateOf(0L) }
    var showControls by remember { mutableStateOf(true) }

    // Get download progress by observing the map and filtering by ID
    val downloadProgressMap by viewModel.downloadProgressMap.collectAsState()
    val downloadProgress = remember(downloadProgressMap, attachment.id) {
        downloadProgressMap[attachment.id]
    }

    // Determine the cached file path
    val cachedFile = remember(attachment.id) {
        val cacheDir = File(context.cacheDir, "media_cache") // Using a dedicated cache sub-directory
        // Ensure the cache directory exists
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        // Construct filename, using ID if name is null or empty
        val fileName = attachment.name?.takeIf { it.isNotBlank() } ?: attachment.id
        File(cacheDir, fileName)
    }
    val isFileCached = remember(cachedFile) { cachedFile.exists() == true }

    // Determine if we need to download the file
    val needsDownload = !isFileCached &&
            downloadProgress == null &&
            attachment.url.startsWith("http")

    // Create ExoPlayer instance
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            // Set repeat mode
            repeatMode = Player.REPEAT_MODE_ONE

            // Add listener
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    if (playbackState == Player.STATE_READY) {
                        duration = <EMAIL>
                    }
                }

                override fun onIsPlayingChanged(playing: Boolean) {
                    isPlaying = playing
                }
            })
        }
    }

    // Update player with media source
    LaunchedEffect(attachment, isFileCached) {
        if (isFileCached && (attachment.type == "video" || attachment.type == "audio")) {
            val mediaItem = MediaItem.fromUri(Uri.fromFile(cachedFile))
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
        } else if (attachment.url.startsWith("http") && (attachment.type == "video" || attachment.type == "audio")) {
            val mediaItem = MediaItem.fromUri(attachment.url)
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
        }
    }

    // Update current position and buffered position
    LaunchedEffect(isPlaying) {
        while (isPlaying) {
            currentPosition = exoPlayer.currentPosition
            bufferedPosition = exoPlayer.bufferedPosition
            delay(1000)
        }
    }

    // Auto-hide controls after a delay
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(3000)
            showControls = false
        }
    }

    // Clean up ExoPlayer when the composable is disposed
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        MeenaTheme {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.9f))
                    .clickable { showControls = !showControls }
            ) {
                // Media content
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectTransformGestures { _, pan, zoom, _ ->
                                // Only allow zoom/pan for images
                                if (attachment.type == "image") {
                                    scale = (scale * zoom).coerceIn(0.5f, 3f)
                                    // Apply bounds to panning based on scale
                                    val maxOffsetX = (size.width * (scale - 1)) / 2
                                    val maxOffsetY = (size.height * (scale - 1)) / 2
                                    offsetX = (offsetX + pan.x).coerceIn(-maxOffsetX, maxOffsetX)
                                    offsetY = (offsetY + pan.y).coerceIn(-maxOffsetY, maxOffsetY)
                                }
                            }
                        }
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        )
                ) {
                    when (attachment.type) {
                        "image" -> {
                            AsyncImage(
                                model = ImageRequest.Builder(context)
                                    .data(if (isFileCached) cachedFile else attachment.url)
                                    .crossfade(true)
                                    .build(),
                                contentDescription = attachment.name ?: "Image",
                                contentScale = ContentScale.Fit,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        "video", "audio" -> {
                            if (isFileCached || attachment.url.startsWith("http")) {
                                AndroidView(
                                    factory = { ctx ->
                                        PlayerView(ctx).apply {
                                            player = exoPlayer
                                            useController = false // Disable default controls
                                            layoutParams = FrameLayout.LayoutParams(
                                                ViewGroup.LayoutParams.MATCH_PARENT,
                                                ViewGroup.LayoutParams.MATCH_PARENT
                                            )
                                        }
                                    },
                                    modifier = Modifier.fillMaxSize()
                                )
                            } else {
                                // Show placeholder or error if file not cached and not downloadable
                                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                                    Text("Cannot play media", color = Color.White)
                                }
                            }
                        }
                        else -> {
                            // Handle other attachment types (e.g., documents)
                            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                                Text("Unsupported attachment type", color = Color.White)
                            }
                        }
                    }
                }

                // Top controls (Close, Share, Download)
                if (showControls) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.TopCenter)
                            .background(Color.Black.copy(alpha = 0.5f))
                            .padding(8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(onClick = onDismiss) {
                            Icon(Icons.Default.Close, contentDescription = "Close", tint = Color.White)
                        }

                        Row {
                            // Share Button
                            if (isFileCached) {
                                IconButton(onClick = { /* TODO: Implement share */ }) {
                                    Icon(Icons.Default.Share, contentDescription = "Share", tint = Color.White)
                                }
                            }

                            // Download Button
                            if (needsDownload) {
                                IconButton(onClick = { viewModel.downloadMediaAttachment(attachment) }) {
                                    Icon(Icons.Default.Download, contentDescription = "Download", tint = Color.White)
                                }
                            } else if (downloadProgress is DownloadProgress.Downloading) {
                                CircularProgressIndicator(modifier = Modifier.size(24.dp), color = Color.White, strokeWidth = 2.dp)
                            }
                        }
                    }
                }

                // Media Playback Controls (for video/audio)
                if ((attachment.type == "video" || attachment.type == "audio") && showControls) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .background(Color.Black.copy(alpha = 0.5f))
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    ) {
                        // Seek bar
                        Slider(
                            value = currentPosition.toFloat(),
                            onValueChange = { exoPlayer.seekTo(it.toLong()) },
                            valueRange = 0f..(duration.toFloat().coerceAtLeast(1f)),
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Time labels and controls
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = mediaAttachmentHelper.formatDuration(currentPosition),
                                color = Color.White,
                                style = MaterialTheme.typography.bodySmall
                            )

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                // Play/Pause Button
                                IconButton(onClick = {
                                    if (isPlaying) exoPlayer.pause() else exoPlayer.play()
                                }) {
                                    Icon(
                                        imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                                        contentDescription = if (isPlaying) "Pause" else "Play",
                                        tint = Color.White
                                    )
                                }

                                // Mute/Unmute Button
                                IconButton(onClick = {
                                    isMuted = !isMuted
                                    exoPlayer.volume = if (isMuted) 0f else 1f
                                }) {
                                    Icon(
                                        imageVector = if (isMuted) Icons.AutoMirrored.Filled.VolumeOff else Icons.AutoMirrored.Filled.VolumeUp,
                                        contentDescription = if (isMuted) "Unmute" else "Mute",
                                        tint = Color.White
                                    )
                                }
                            }

                            Text(
                                text = mediaAttachmentHelper.formatDuration(duration),
                                color = Color.White,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }

                // Download Progress Indicator
                if (downloadProgress is DownloadProgress.Downloading) {
                    LinearProgressIndicator(
                        progress = {
                            downloadProgress.progress / 100f // Pass Float directly
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .padding(bottom = if (attachment.type == "video" || attachment.type == "audio") 70.dp else 8.dp) // Adjust padding based on controls
                            .padding(horizontal = 16.dp),
                    )
                } else if (downloadProgress is DownloadProgress.Error) {
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        Text("Download failed: ${downloadProgress.error}", color = Color.Red)
                    }
                } else if (needsDownload && attachment.type != "image" && attachment.type != "video" && attachment.type != "audio") {
                    // Show download button for other types if needed
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(attachment.name ?: "File", color = Color.White)
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = attachment.size?.let { mediaAttachmentHelper.formatFileSize(it) } ?: "",
                                color = Color.White.copy(alpha = 0.7f),
                                style = MaterialTheme.typography.bodySmall
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            IconButton(
                                onClick = { viewModel.downloadMediaAttachment(attachment) },
                                modifier = Modifier
                                    .size(56.dp)
                                    .clip(CircleShape)
                                    .background(MaterialTheme.colorScheme.primary)
                            ) {
                                Icon(Icons.Default.Download, contentDescription = "Download", tint = Color.White)
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Format duration in milliseconds to a human-readable string.
 *
 * @param millis The duration in milliseconds.
 * @return The formatted duration.
 */
private fun formatDuration(millis: Long): String {
    val seconds = (millis / 1000) % 60
    val minutes = (millis / (1000 * 60)) % 60
    val hours = (millis / (1000 * 60 * 60))

    return if (hours > 0) {
        String.format("%d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%d:%02d", minutes, seconds)
    }
}
