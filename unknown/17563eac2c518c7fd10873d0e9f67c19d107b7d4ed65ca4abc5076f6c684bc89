FROM golang:1.24-alpine AS builder

# Set working directory
WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application with verbose output
RUN CGO_ENABLED=0 GOOS=linux go build -v -a -installsuffix cgo -o meena-api ./cmd/api

# Use a minimal alpine image for the final image
FROM alpine:latest

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

# Set working directory
WORKDIR /root/

# Copy the binary from the builder stage
COPY --from=builder /app/meena-api .

# Create a default .env file
RUN echo "PORT=8080\nENV=production\nJWT_SECRET=change_this_in_railway_variables" > .env

# Expose the port
EXPOSE 8080

# Run the application
CMD ["./meena-api"]
