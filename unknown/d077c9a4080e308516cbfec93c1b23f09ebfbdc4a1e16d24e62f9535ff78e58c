package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import javax.inject.Inject

/**
 * Use case for removing a contact from a group.
 */
class RemoveContactFromGroupUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Remove a contact from a group.
     *
     * @param groupId The group ID.
     * @param contactId The contact ID.
     * @return The updated contact group.
     */
    suspend operator fun invoke(groupId: String, contactId: String): ContactGroup {
        return contactGroupRepository.removeContactFromGroup(groupId, contactId)
    }
}
