#!/bin/bash

# Script to run the verification status migration on the production database
# This script should be run after deploying the updated backend code

# Set the database URL from environment variable or use a default
DATABASE_URL=${DATABASE_URL:-"postgresql://postgres:postgres@localhost:5432/meena"}

echo "Running verification status migration on database: $DATABASE_URL"
echo "This will convert the is_verified boolean field to a verification_status enum"
echo "Press Ctrl+C to cancel or any key to continue..."
read -n 1 -s

# Run the migration script
psql "$DATABASE_URL" -f scripts/migrate_verification_status.sql

# Check if the migration was successful
if [ $? -eq 0 ]; then
    echo "Migration completed successfully!"
else
    echo "Migration failed. Please check the error messages above."
    exit 1
fi

echo "Verification status migration has been applied to the database."
echo "Make sure to deploy the updated backend code before using the new field."
