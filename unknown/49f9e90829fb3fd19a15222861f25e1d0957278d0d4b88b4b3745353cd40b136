package com.tfkcolin.meena.ui.auth

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.tfkcolin.meena.domain.usecases.auth.IsLoggedInUseCase
import com.tfkcolin.meena.domain.usecases.auth.LoginUseCase
import com.tfkcolin.meena.domain.usecases.auth.RecoverAccountUseCase
import com.tfkcolin.meena.domain.usecases.auth.RegisterUseCase
import com.tfkcolin.meena.domain.usecases.auth.TwoFactorAuthUseCase
import com.tfkcolin.meena.utils.ErrorHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExperimentalCoroutinesApi
class AuthViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    @Mock
    private lateinit var registerUseCase: RegisterUseCase

    @Mock
    private lateinit var loginUseCase: LoginUseCase

    @Mock
    private lateinit var twoFactorAuthUseCase: TwoFactorAuthUseCase

    @Mock
    private lateinit var recoverAccountUseCase: RecoverAccountUseCase

    @Mock
    private lateinit var isLoggedInUseCase: IsLoggedInUseCase

    @Mock
    private lateinit var errorHandler: ErrorHandler

    private lateinit var viewModel: AuthViewModel

//    @Before
//    fun setup() {
//        MockitoAnnotations.openMocks(this)
//        Dispatchers.setMain(testDispatcher)
//
//        `when`(isLoggedInUseCase()).thenReturn(false)
//        `when`(errorHandler.getErrorMessage(any(), anyOrNull())).thenReturn("Error message")
//
//        viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//    }
//
//    @After
//    fun tearDown() {
//        Dispatchers.resetMain()
//    }
//
//    @Test
//    fun `init checks if user is logged in`() = runTest {
//        // Given
//        `when`(isLoggedInUseCase()).thenReturn(true)
//
//        // When
//        val viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//
//        // Then
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.isLoggedIn)
//        }
//    }
//
//    @Test
//    fun `register success updates state correctly`() = runTest {
//        // Given
//        val userHandle = "testuser"
//        val password = "password"
//        val email = "<EMAIL>"
//        val phoneNumber = "**********"
//        val recoveryPin = "1234"
//
//        val userId = "user123"
//        val recoveryPhrase = "word1 word2 word3 word4 word5 word6 word7 word8 word9"
//
//        val response = RegisterResponse(
//            userId = userId,
//            userHandle = userHandle,
//            recoveryPhrase = recoveryPhrase
//        )
//
//        whenever(registerUseCase(
//            userHandle = userHandle,
//            password = password,
//            email = email,
//            phoneNumber = phoneNumber,
//            recoveryPin = recoveryPin,
//            recoveryPhrase = null
//        )).thenReturn(Result.success(response))
//
//        // When
//        viewModel.register(
//            userHandle = userHandle,
//            password = password,
//            email = email,
//            phoneNumber = phoneNumber,
//            recoveryPin = recoveryPin
//        )
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.registerOperation.isSuccessful)
//            assertFalse(state.registerOperation.isInProgress)
//            assertNull(state.registerOperation.error)
//            assertTrue(state.isLoggedIn)
//            assertEquals(userId, state.userId)
//            assertEquals(userHandle, state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertNull(state.properties.error)
//        }
//
//        viewModel.recoveryPhrase.test {
//            assertEquals(recoveryPhrase, awaitItem())
//        }
//    }
//
//    @Test
//    fun `register failure updates state correctly`() = runTest {
//        // Given
//        val userHandle = "testuser"
//        val password = "password"
//        val email = "<EMAIL>"
//        val phoneNumber = "**********"
//        val recoveryPin = "1234"
//
//        val exception = Exception("Registration failed")
//        val errorMessage = "Error message"
//
//        whenever(registerUseCase(
//            userHandle = userHandle,
//            password = password,
//            email = email,
//            phoneNumber = phoneNumber,
//            recoveryPin = recoveryPin,
//            recoveryPhrase = null
//        )).thenReturn(Result.failure(exception))
//
//        whenever(errorHandler.getErrorMessage(exception, "Registration failed"))
//            .thenReturn(errorMessage)
//
//        // When
//        viewModel.register(
//            userHandle = userHandle,
//            password = password,
//            email = email,
//            phoneNumber = phoneNumber,
//            recoveryPin = recoveryPin
//        )
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertFalse(state.registerOperation.isSuccessful)
//            assertFalse(state.registerOperation.isInProgress)
//            assertEquals(errorMessage, state.registerOperation.error)
//            assertFalse(state.isLoggedIn)
//            assertNull(state.userId)
//            assertNull(state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertEquals(errorMessage, state.properties.error)
//        }
//    }
//
//    @Test
//    fun `login success without 2FA updates state correctly`() = runTest {
//        // Given
//        val identifier = "testuser"
//        val password = "password"
//
//        val userId = "user123"
//        val userHandle = "testuser"
//
//        val response = LoginResponse(
//            userId = userId,
//            userHandle = userHandle,
//            requires2fa = false
//        )
//
//        whenever(loginUseCase(identifier, password)).thenReturn(Result.success(response))
//
//        // When
//        viewModel.login(identifier, password)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.loginOperation.isSuccessful)
//            assertFalse(state.loginOperation.isInProgress)
//            assertNull(state.loginOperation.error)
//            assertTrue(state.isLoggedIn)
//            assertFalse(state.requires2fa)
//            assertEquals(userId, state.userId)
//            assertEquals(userHandle, state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertNull(state.properties.error)
//        }
//    }
//
//    @Test
//    fun `login success with 2FA updates state correctly`() = runTest {
//        // Given
//        val identifier = "testuser"
//        val password = "password"
//
//        val userId = "user123"
//        val userHandle = "testuser"
//
//        val response = LoginResponse(
//            userId = userId,
//            userHandle = userHandle,
//            requires2fa = true
//        )
//
//        whenever(loginUseCase(identifier, password)).thenReturn(Result.success(response))
//
//        // When
//        viewModel.login(identifier, password)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.loginOperation.isSuccessful)
//            assertFalse(state.loginOperation.isInProgress)
//            assertNull(state.loginOperation.error)
//            assertFalse(state.isLoggedIn)
//            assertTrue(state.requires2fa)
//            assertEquals(userId, state.userId)
//            assertEquals(userHandle, state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertNull(state.properties.error)
//        }
//    }
//
//    @Test
//    fun `login failure updates state correctly`() = runTest {
//        // Given
//        val identifier = "testuser"
//        val password = "password"
//
//        val exception = Exception("Login failed")
//        val errorMessage = "Error message"
//
//        whenever(loginUseCase(identifier, password)).thenReturn(Result.failure(exception))
//        whenever(errorHandler.getErrorMessage(exception, "Login failed"))
//            .thenReturn(errorMessage)
//
//        // When
//        viewModel.login(identifier, password)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertFalse(state.loginOperation.isSuccessful)
//            assertFalse(state.loginOperation.isInProgress)
//            assertEquals(errorMessage, state.loginOperation.error)
//            assertFalse(state.isLoggedIn)
//            assertFalse(state.requires2fa)
//            assertNull(state.userId)
//            assertNull(state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertEquals(errorMessage, state.properties.error)
//        }
//    }
//
//    @Test
//    fun `twoFactorAuth success updates state correctly`() = runTest {
//        // Given
//        val userId = "user123"
//        val userHandle = "testuser"
//        val code = "123456"
//        val method = "totp"
//
//        val response = TwoFactorAuthResponse(
//            userId = userId,
//            userHandle = userHandle
//        )
//
//        // Set userId in state
//        viewModel.login("testuser", "password")
//        testDispatcher.scheduler.advanceUntilIdle()
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertEquals(null, state.userId)
//        }
//
//        // Mock state with userId
//        val initialState = AuthState(
//            userId = userId,
//            requires2fa = true
//        )
//
//        val viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//
//        // Use reflection to set the initial state
//        val field = AuthViewModel::class.java.getDeclaredField("_authState")
//        field.isAccessible = true
//        field.set(viewModel, MutableStateFlow(initialState))
//
//        whenever(twoFactorAuthUseCase(userId, code, method)).thenReturn(Result.success(response))
//
//        // When
//        viewModel.twoFactorAuth(code, method)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.twoFactorAuthOperation.isSuccessful)
//            assertFalse(state.twoFactorAuthOperation.isInProgress)
//            assertNull(state.twoFactorAuthOperation.error)
//            assertTrue(state.isLoggedIn)
//            assertFalse(state.requires2fa)
//            assertEquals(userId, state.userId)
//            assertEquals(userHandle, state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertNull(state.properties.error)
//        }
//    }
//
//    @Test
//    fun `twoFactorAuth failure updates state correctly`() = runTest {
//        // Given
//        val userId = "user123"
//        val code = "123456"
//        val method = "totp"
//
//        val exception = Exception("2FA failed")
//        val errorMessage = "Error message"
//
//        // Mock state with userId
//        val initialState = AuthState(
//            userId = userId,
//            requires2fa = true
//        )
//
//        val viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//
//        // Use reflection to set the initial state
//        val field = AuthViewModel::class.java.getDeclaredField("_authState")
//        field.isAccessible = true
//        field.set(viewModel, MutableStateFlow(initialState))
//
//        whenever(twoFactorAuthUseCase(userId, code, method)).thenReturn(Result.failure(exception))
//        whenever(errorHandler.getErrorMessage(exception, "Two-factor authentication failed"))
//            .thenReturn(errorMessage)
//
//        // When
//        viewModel.twoFactorAuth(code, method)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertFalse(state.twoFactorAuthOperation.isSuccessful)
//            assertFalse(state.twoFactorAuthOperation.isInProgress)
//            assertEquals(errorMessage, state.twoFactorAuthOperation.error)
//            assertFalse(state.isLoggedIn)
//            assertTrue(state.requires2fa)
//            assertEquals(userId, state.userId)
//            assertNull(state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertEquals(errorMessage, state.properties.error)
//        }
//    }
//
//    @Test
//    fun `recoverAccount success updates state correctly`() = runTest {
//        // Given
//        val userHandle = "testuser"
//        val recoveryPhrase = "word1 word2 word3 word4 word5 word6 word7 word8 word9"
//        val recoveryPin = "1234"
//        val newPassword = "newpassword"
//
//        val userId = "user123"
//
//        val response = RecoverAccountResponse(
//            userId = userId,
//            userHandle = userHandle
//        )
//
//        whenever(recoverAccountUseCase(userHandle, recoveryPhrase, recoveryPin, newPassword))
//            .thenReturn(Result.success(response))
//
//        // When
//        viewModel.recoverAccount(userHandle, recoveryPhrase, recoveryPin, newPassword)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertTrue(state.recoverAccountOperation.isSuccessful)
//            assertFalse(state.recoverAccountOperation.isInProgress)
//            assertNull(state.recoverAccountOperation.error)
//            assertTrue(state.isLoggedIn)
//            assertEquals(userId, state.userId)
//            assertEquals(userHandle, state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertNull(state.properties.error)
//        }
//    }
//
//    @Test
//    fun `recoverAccount failure updates state correctly`() = runTest {
//        // Given
//        val userHandle = "testuser"
//        val recoveryPhrase = "word1 word2 word3 word4 word5 word6 word7 word8 word9"
//        val recoveryPin = "1234"
//        val newPassword = "newpassword"
//
//        val exception = Exception("Recovery failed")
//        val errorMessage = "Error message"
//
//        whenever(recoverAccountUseCase(userHandle, recoveryPhrase, recoveryPin, newPassword))
//            .thenReturn(Result.failure(exception))
//        whenever(errorHandler.getErrorMessage(exception, "Account recovery failed"))
//            .thenReturn(errorMessage)
//
//        // When
//        viewModel.recoverAccount(userHandle, recoveryPhrase, recoveryPin, newPassword)
//
//        // Then
//        testDispatcher.scheduler.advanceUntilIdle()
//
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertFalse(state.recoverAccountOperation.isSuccessful)
//            assertFalse(state.recoverAccountOperation.isInProgress)
//            assertEquals(errorMessage, state.recoverAccountOperation.error)
//            assertFalse(state.isLoggedIn)
//            assertNull(state.userId)
//            assertNull(state.userHandle)
//            assertFalse(state.properties.isLoading)
//            assertEquals(errorMessage, state.properties.error)
//        }
//    }
//
//    @Test
//    fun `clearError clears error in state`() = runTest {
//        // Given
//        val errorMessage = "Error message"
//        val initialState = AuthState(
//            properties = UiStateProperties(error = errorMessage)
//        )
//
//        val viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//
//        // Use reflection to set the initial state
//        val field = AuthViewModel::class.java.getDeclaredField("_authState")
//        field.isAccessible = true
//        field.set(viewModel, MutableStateFlow(initialState))
//
//        // When
//        viewModel.clearError()
//
//        // Then
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertNull(state.properties.error)
//        }
//    }
//
//    @Test
//    fun `resetOperationStates resets all operation states`() = runTest {
//        // Given
//        val initialState = AuthState(
//            registerOperation = OperationState(isSuccessful = true),
//            loginOperation = OperationState(isInProgress = true),
//            twoFactorAuthOperation = OperationState(error = "Error"),
//            recoverAccountOperation = OperationState(isSuccessful = true, error = "Error")
//        )
//
//        val viewModel = AuthViewModel(
//            registerUseCase,
//            loginUseCase,
//            twoFactorAuthUseCase,
//            recoverAccountUseCase,
//            isLoggedInUseCase,
//            errorHandler
//        )
//
//        // Use reflection to set the initial state
//        val field = AuthViewModel::class.java.getDeclaredField("_authState")
//        field.isAccessible = true
//        field.set(viewModel, MutableStateFlow(initialState))
//
//        // When
//        viewModel.resetOperationStates()
//
//        // Then
//        viewModel.authState.test {
//            val state = awaitItem()
//            assertFalse(state.registerOperation.isSuccessful)
//            assertFalse(state.registerOperation.isInProgress)
//            assertNull(state.registerOperation.error)
//
//            assertFalse(state.loginOperation.isSuccessful)
//            assertFalse(state.loginOperation.isInProgress)
//            assertNull(state.loginOperation.error)
//
//            assertFalse(state.twoFactorAuthOperation.isSuccessful)
//            assertFalse(state.twoFactorAuthOperation.isInProgress)
//            assertNull(state.twoFactorAuthOperation.error)
//
//            assertFalse(state.recoverAccountOperation.isSuccessful)
//            assertFalse(state.recoverAccountOperation.isInProgress)
//            assertNull(state.recoverAccountOperation.error)
//        }
//    }
}
