package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.ContactGroup
import com.tfkcolin.meena.data.repository.ContactGroupRepository
import javax.inject.Inject

/**
 * Use case for creating a contact group.
 */
class CreateContactGroupUseCase @Inject constructor(
    private val contactGroupRepository: ContactGroupRepository
) {
    /**
     * Create a contact group.
     *
     * @param name The group name.
     * @param description The group description.
     * @param contactIds The IDs of contacts to add to the group.
     * @return The created contact group.
     */
    suspend operator fun invoke(
        name: String,
        description: String? = null,
        contactIds: List<String> = emptyList()
    ): ContactGroup {
        return contactGroupRepository.createContactGroup(name, description, contactIds)
    }
}
