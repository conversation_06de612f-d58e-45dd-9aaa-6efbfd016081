package com.tfkcolin.meena.ui.stories.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.meena.ui.stories.models.StoryItem

/**
 * A horizontal scrollable bar of stories.
 *
 * @param stories The list of stories to display.
 * @param onCreateStoryClick The callback when the create story card is clicked.
 * @param onStoryClick The callback when a story is clicked.
 * @param modifier The modifier for the bar.
 * @param userAvatarUrl The URL of the user's avatar for the create story card.
 */
@Composable
fun StoriesBar(
    stories: List<StoryItem>,
    onCreateStoryClick: () -> Unit,
    onStoryClick: (StoryItem) -> Unit,
    modifier: Modifier = Modifier,
    userAvatarUrl: String = ""
) {
    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .padding(vertical = 12.dp),
        contentPadding = PaddingValues(horizontal = 16.dp),
        content = {
            // Create story card (always first)
            item {
                CreateStoryCard(
                    onClick = onCreateStoryClick,
                    userAvatarUrl = userAvatarUrl
                )
            }
            
            // Stories from friends
            items(stories) { story ->
                StoryCard(
                    story = story,
                    onClick = { onStoryClick(story) },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    )
}
