package com.tfkcolin.meena.ui.models

import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.data.models.User

/**
 * UI model for displaying a contact in the UI.
 *
 * For anonymous sign-in, the primary identifiers are handle (Meena ID) and name (display name).
 * Other fields are kept for compatibility with the backend but may not be used in the UI.
 */
data class UIContact(
    val id: String,
    val userId: String,
    val contactUserId: String,
    val handle: String,
    val name: String,
    val avatarUrl: String?,
    val isBlocked: Boolean
) {
    companion object {
        /**
         * Create a UIContact from a Contact and User.
         *
         * @param contact The Contact to convert.
         * @param user The User associated with the contact.
         * @return The UIContact.
         */
        fun fromContactAndUser(contact: Contact, user: User?): UIContact {
            return UIContact(
                id = contact.id,
                userId = contact.userId,
                contactUserId = contact.contactId,
                handle = user?.userHandle ?: contact.contactId,
                name = contact.displayName ?: user?.displayName ?: "Unknown",
                avatarUrl = user?.profilePictureUrl,
                isBlocked = contact.isBlocked()
            )
        }
    }
}
