package com.tfkcolin.meena.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Size
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.MediaType
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for media attachment operations.
 */
@Singleton
class MediaAttachmentHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val fileUtils: FileUtils
) {
    
    /**
     * Create a media attachment from a URI.
     * 
     * @param uri The URI.
     * @return The media attachment, or null if it couldn't be created.
     */
    fun createAttachmentFromUri(uri: Uri): MediaAttachment? {
        val mimeType = fileUtils.getMimeType(uri) ?: return null
        val fileName = fileUtils.getFileName(uri)
        val fileSize = fileUtils.getFileSize(uri)
        
        // Determine the media type based on the MIME type
        val mediaType = when {
            mimeType.startsWith("image/") -> MediaType.IMAGE
            mimeType.startsWith("video/") -> MediaType.VIDEO
            mimeType.startsWith("audio/") -> MediaType.AUDIO
            else -> MediaType.DOCUMENT
        }
        
        // Copy the file to the cache directory
        val cachedFile = fileUtils.copyFileToCache(uri) ?: return null
        
        // Get additional metadata based on the media type
        val (width, height, duration) = when (mediaType) {
            MediaType.IMAGE -> getImageMetadata(cachedFile)
            MediaType.VIDEO -> getVideoMetadata(uri)
            MediaType.AUDIO -> Triple(null, null, getAudioDuration(uri))
            else -> Triple(null, null, null)
        }
        
        // Create a thumbnail for images and videos
        val thumbnailUrl = when (mediaType) {
            MediaType.IMAGE -> cachedFile.absolutePath // Use the original file for images
            MediaType.VIDEO -> createVideoThumbnail(uri)?.absolutePath
            else -> null
        }
        
        return MediaAttachment(
            id = UUID.randomUUID().toString(),
            messageId = "", // This will be set when the message is created
            type = mediaType.name.lowercase(),
            url = cachedFile.absolutePath,
            thumbnailUrl = thumbnailUrl,
            name = fileName,
            size = fileSize,
            duration = duration,
            width = width,
            height = height,
            latitude = null,
            longitude = null
        )
    }
    
    /**
     * Get metadata for an image file.
     * 
     * @param file The image file.
     * @return A triple containing the width, height, and null for duration.
     */
    private fun getImageMetadata(file: File): Triple<Int?, Int?, Long?> {
        try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(file.absolutePath, options)
            
            return Triple(options.outWidth, options.outHeight, null)
        } catch (e: Exception) {
            e.printStackTrace()
            return Triple(null, null, null)
        }
    }
    
    /**
     * Get metadata for a video file.
     * 
     * @param uri The video URI.
     * @return A triple containing the width, height, and duration.
     */
    private fun getVideoMetadata(uri: Uri): Triple<Int?, Int?, Long?> {
        try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(context, uri)
            
            val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull()
            val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull()
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull()
            
            retriever.release()
            
            return Triple(width, height, duration)
        } catch (e: Exception) {
            e.printStackTrace()
            return Triple(null, null, null)
        }
    }
    
    /**
     * Get the duration of an audio file.
     * 
     * @param uri The audio URI.
     * @return The duration in milliseconds, or null if it couldn't be determined.
     */
    private fun getAudioDuration(uri: Uri): Long? {
        try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(context, uri)
            
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull()
            
            retriever.release()
            
            return duration
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Create a thumbnail for a video file.
     * 
     * @param uri The video URI.
     * @return The thumbnail file, or null if it couldn't be created.
     */
    private fun createVideoThumbnail(uri: Uri): File? {
        try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(context, uri)
            
            val bitmap = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
            retriever.release()
            
            if (bitmap != null) {
                val thumbnailFile = File(context.cacheDir, "thumbnail_${UUID.randomUUID()}.jpg")
                
                thumbnailFile.outputStream().use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
                }
                
                return thumbnailFile
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return null
    }
    
    /**
     * Format a file size for display.
     * 
     * @param size The file size in bytes.
     * @return The formatted file size.
     */
    fun formatFileSize(size: Long?): String {
        if (size == null) return "Unknown size"
        
        return when {
            size < 1024 -> "$size B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)} MB"
            else -> "${size / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * Format a duration for display.
     * 
     * @param duration The duration in milliseconds.
     * @return The formatted duration.
     */
    fun formatDuration(duration: Long?): String {
        if (duration == null) return "Unknown duration"
        
        val seconds = duration / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            else -> String.format("%d:%02d", minutes, seconds % 60)
        }
    }
}
