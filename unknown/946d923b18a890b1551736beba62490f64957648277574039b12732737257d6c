package com.tfkcolin.meena.ui.components

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.OpenInNew
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.OpenInNew
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.data.models.DownloadProgress
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.ui.viewmodels.MediaViewModel
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import kotlinx.coroutines.flow.collectLatest
import java.io.File

/**
 * Document viewer component.
 *
 * @param attachment The media attachment to view.
 * @param modifier The modifier for the component.
 * @param viewModel The media view model.
 */
@Composable
fun DocumentViewer(
    attachment: MediaAttachment,
    modifier: Modifier = Modifier,
    mediaAttachmentHelper: MediaAttachmentHelper,
    viewModel: MediaViewModel = hiltViewModel()
) {
    val context = LocalContext.current

    // Check if the file is cached locally
    val cachedFile = remember(attachment.id) {
        val cacheDir = File(context.cacheDir, "media")
        val fileName = attachment.name ?: attachment.id
        File(cacheDir, fileName)
    }

    var isFileCached by remember(cachedFile.path) {
        mutableStateOf(cachedFile.exists())
    }

    // Get file size in human-readable format
    val fileSize = remember(attachment.size) {
        attachment.size?.let { size ->
            when {
                size < 1024 -> "$size B"
                size < 1024 * 1024 -> "${size / 1024} KB"
                size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)} MB"
                else -> "${size / (1024 * 1024 * 1024)} GB"
            }
        } ?: "Unknown size"
    }

    // Get file extension
    val fileExtension = remember(attachment.name) {
        attachment.name?.substringAfterLast('.', "")?.uppercase() ?: ""
    }

    // Get download progress
    val downloadProgress by viewModel.getDownloadProgress(attachment.id)
        .collectAsState(initial = null)

    // Download the file if not cached
    LaunchedEffect(attachment.id, isFileCached) {
        if (!isFileCached) {
            viewModel.downloadMediaAttachment(attachment)

            // Listen for download completion
            viewModel.getDownloadProgress(attachment.id).collectLatest { progress ->
                if (progress is DownloadProgress.Complete) {
                    isFileCached = true
                }
            }
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Document info
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // File type icon or extension
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = MaterialTheme.colorScheme.primaryContainer,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (fileExtension.isNotEmpty()) {
                        Text(
                            text = fileExtension,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Description,
                            contentDescription = "Document",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = attachment.name ?: "Document",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Text(
                        text = fileSize,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Download progress or actions
            when {
                downloadProgress is DownloadProgress.Downloading -> {
                    val progress = (downloadProgress as DownloadProgress.Downloading).progress
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Modifier.size(48.dp)
                        CircularProgressIndicator(
                            progress = { progress / 100f },
                            modifier = modifier,
                            color = MaterialTheme.colorScheme.primary,
                            strokeWidth = 5.dp,
                            trackColor = MaterialTheme.colorScheme.primaryContainer
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Downloading... $progress%",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
                downloadProgress is DownloadProgress.Preparing -> {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Preparing download...",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
                isFileCached -> {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        // Open button
                        Button(
                            onClick = {
                                val intent = Intent(Intent.ACTION_VIEW).apply {
                                    setDataAndType(Uri.fromFile(cachedFile), "*/*")
                                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                }

                                try {
                                    context.startActivity(intent)
                                } catch (e: Exception) {
                                    // No app available to handle this file type
                                    // Show a toast or message
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.OpenInNew,
                                contentDescription = "Open"
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(text = "Open")
                        }

                        // Save button
                        Button(
                            onClick = {
                                viewModel.downloadMediaAttachment(attachment)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Download,
                                contentDescription = "Save"
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(text = "Save")
                        }
                    }
                }
                else -> {
                    // Download button
                    Button(
                        onClick = {
                            viewModel.downloadMediaAttachment(attachment)
                        },
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Download,
                            contentDescription = "Download"
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(text = "Download")
                    }
                }
            }
        }
    }
}

@Composable
fun collectAsState(initial: Nothing?): Any {
    // This is a placeholder function to make the code compile
    // In a real implementation, you would use the collectAsState extension function from kotlinx.coroutines.flow
    return remember { mutableStateOf(initial) }
}
