#!/bin/bash

# Deploy changes to the backend and database

# Step 1: Build the backend
echo "Building backend..."
cd backend && go build -o ../meena-backend ./cmd/api
cd ..

# Step 2: Deploy the backend to Railway
echo "Deploying backend to Railway..."
railway up

# Step 3: Instructions for applying database changes
echo "To apply database changes, you have two options:"
echo ""
echo "Option 1: Reset the entire database (recommended for development)"
echo "1. Log in to your Railway dashboard"
echo "2. Navigate to your PostgreSQL database"
echo "3. Open the SQL Editor"
echo "4. Copy the contents of scripts/initialize_database_fixed.sql"
echo "5. Paste it into the SQL Editor and run it"
echo ""
echo "Alternatively, if you have the Railway CLI configured, you can run:"
echo "./scripts/initialize_database.sh"
echo ""
echo "Option 2: Apply only the database functions (preserves existing data)"
echo "1. Log in to your Railway dashboard"
echo "2. Navigate to your PostgreSQL database"
echo "3. Open the SQL Editor"
echo "4. Copy the contents of scripts/apply_database_functions.sql"
echo "5. Paste it into the SQL Editor and run it"
echo ""
echo "Alternatively, if you have the Railway CLI configured, you can run:"
echo "./scripts/apply_functions.sh"
